package ${context.modelPackageName}.service;

import ${context.modelPackageName}.model.${context.bizEnBigName};

import ${context.proPackage}.core.util.ShiroUtils;
import ${context.proPackage}.core.aop.DataPermisionTypeEnum;
import ${context.proPackage}.core.shiro.DataAuthService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.Serializable;
@Service
public class ${context.bizEnBigName}ExtService {
    @Autowired
    ${context.bizEnBigName}Service ${context.bizEnName}Service;

    public void save(${context.bizEnBigName} ${context.bizEnName}){
        setEnumsName(${context.bizEnName});
        ShiroUtils.setAddAuthInfo(${context.bizEnName});
        ${context.bizEnName}Service.save(${context.bizEnName});
    }

    public void updateById(${context.bizEnBigName} ${context.bizEnName}){
        setEnumsName(${context.bizEnName});
        DataAuthService.checkPermision(${context.bizEnName}Service.getById(${context.bizEnName}.getId()),DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(${context.bizEnName});
        ${context.bizEnBigName} ${context.bizEnName}Db = queryById(${context.bizEnName}.getId());

        ${context.bizEnName}Service.updateById(${context.bizEnName});
    }
    public ${context.bizEnBigName} queryById(Serializable id){
        ${context.bizEnBigName} ${context.bizEnName} = ${context.bizEnName}Service.getById(id);
        DataAuthService.checkPermision(${context.bizEnName},DataPermisionTypeEnum.ByComp);

        return   ${context.bizEnName};
    }
    public void removeById(Serializable id){
       ${context.bizEnBigName} ${context.bizEnName} = ${context.bizEnName}Service.getById(id);
       DataAuthService.checkPermision(${context.bizEnName},DataPermisionTypeEnum.ByComp);
       ${context.bizEnBigName} ${context.bizEnName}Record = new ${context.bizEnBigName}();
       ${context.bizEnName}Record.setId(${context.bizEnName}.getId());

       ${context.bizEnName}Service.updateById( ${context.bizEnName}Record);
    }
    private void setEnumsName(${context.bizEnBigName} ${context.bizEnName}){
         <% for(enumsEle in serviceConfig.enumsConfig.enumsEleList!){
             if(dicFun.hasDictTypeName(context.tableInfo,enumsEle.enumsTname)){ %>
            ${enumsEle.EnumsClassName} ${enumsEle.enumsTname} = ${enumsEle.EnumsClassName}.getType(${context.bizEnName}.get${enumsEle.EnumsClassName}()+"");
            if(${enumsEle.enumsTname}!=null){
                ${context.bizEnName}.set${enumsEle.EnumsClassName}Name(${enumsEle.enumsTname}.name);
            }
        <%}}%>
    }
}
