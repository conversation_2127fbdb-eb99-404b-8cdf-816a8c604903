package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.common.page.LayuiPageFactory;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.shiro.ShiroUser;
import ${package.Entity}.reqparam.${entity}ListParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Service;
/**
 * <p>
 * ${table.comment} 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${superServiceClass}<${entity}> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(${entity}ListParam param) {
        QueryWrapper<${entity}> objectQueryWrapper = new QueryWrapper<>();
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);
        return layuiPageInfo;

    }
}
