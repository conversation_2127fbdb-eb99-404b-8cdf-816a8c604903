/**
 * 详情对话框
 */
var ${context.bizEnBigName}InfoDlg = {
    data: {
          <% var fieldSize = tool.toInt((table.fields.~size )); %>
         <% for(var i=0;i<fieldSize;i++){ %>
         ${table.fields[i].propertyName}:"",
         <%}%>
    }
};
layui.use('laydate', function(){
    var laydate = layui.laydate;
    <% for(var i=0;i<fieldSize;i++){ %>
     <% if(table.fields[i].type=="timestamp" || table.fields[i].type=="datetime"){ %>
        laydate.render({
                elem: '#${table.fields[i].propertyName}'
         });
    <%}}%>
});


layui.use(['form', 'ax','upload','table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var ${context.bizEnBigName} = {
            tableId: "${context.bizEnName}Table"
     };
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/${context.bizEnName}/add", function (data) {
            Feng.success("添加成功！");
            setTimeout(function () {
                   <%if(context.isOpenDia){%>
                            top.layer.closeAll();
                   <%}else{%>
                             window.location.href = Feng.ctxPath + "/${context.bizEnName}";
                   <%}%>
                        }, 1000);
        }, function (data) {
            Feng.error("添加失败！" + data.responseJSON.message)
           setTimeout(function () {
                           top.layer.closeAll();
                       }, 1000);
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });

    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/${context.bizEnName}";
    });
         <% for(var i=0;i<fieldSize;i++){ %>
          <%if(strutil.contain (strutil.toLowerCase (table.fields[i].propertyName),"pics")){%>
    //多图片上传
    upload.render({
      elem: '#${table.fields[i].propertyName}But'
      ,url: Feng.ctxPath + '/oss/upload'
      ,multiple: true
      ,before: function(obj){
          //预读本地文件示例，不支持ie8
          obj.preview(function(index, file, result){
          });
      }
      ,done: function(res){
          //上传完毕
            var rowImgDivId = excludeSpecial(res.data.ourl);
            var hidenImgEleId = rowImgDivId+'hidInput'
            $('#${table.fields[i].propertyName}').val($('#${table.fields[i].propertyName}').val() + "," + res.data.ourl);
            var imgDiv = '<div class="layui-row layui-col-md3 layui-col-space30" style="margin-top: 30px;overflow: hidden;" id="' + rowImgDivId + '">';
            imgDiv = imgDiv + '<img style=\"width:200px;height: 200px\" src="' + res.data.acurl+ '" class="layui-upload-img">';
            imgDiv = imgDiv + '        <br>';
            imgDiv = imgDiv + '            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="Feng.delDivImgDiv(\'' + rowImgDivId + '\',\'' + res.data.acurl + '\',\'' + (hidenImgEleId) + '\')"><i class="layui-icon"></i></button>';

            imgDiv = imgDiv + '</div>';

            $('#${table.fields[i].propertyName}Demo2').append(imgDiv)

          Feng.success(res.message);
      }
    });
          <%}else if(strutil.contain (strutil.toLowerCase (table.fields[i].propertyName),"pic")){%>

    //普通图片上传
    upload.render({
      elem: '#upload${table.fields[i].propertyName}'
      , url: Feng.ctxPath + '/oss/upload'
      , before: function (obj) {
          obj.preview(function (index, file, result) {
              $('#${table.fields[i].propertyName}Img').attr('src', result);
          });
      }
      , done: function (res) {
          $('#${table.fields[i].propertyName}Img').attr('src', res.data.acurl);
          $("#${table.fields[i].propertyName}Hidden").val(res.data.ourl);
          Feng.success(res.message);
      }
      , error: function () {
          Feng.error("上传图片失败！");
      }
    });

          <%}}%>

});
