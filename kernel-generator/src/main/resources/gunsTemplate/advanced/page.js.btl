layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var ${context.bizEnBigName} = {
        tableId: "${context.bizEnName}Table"
    };

    /**
     * 初始化表格的列
     */
    ${context.bizEnBigName}.initColumn = function () {
        return [[
            {type: 'checkbox'},
              <% var fieldSize = tool.toInt((table.fields.~size )); %>
              <% for(var i=0;i<fieldSize;i++){
                  if (dicFun.isDictType(table.fields[i])){
                    continue;
                  }
                  var fieldStr = "{field: '"+table.fields[i].propertyName+"',";
                  var minWidth = " minWidth: 100,";
                  var templetStr = "";
                  var title="title: '"+table.fields[i].comment+"'";
                  if(table.fields[i].type=="timestamp"){
                      minWidth = " minWidth: 200,";
                  }else if(strutil.contain (strutil.toLowerCase(table.fields[i].propertyName),"pics")){
                        templetStr =  templetStr + "templet: function(obj){ \r\n";
                        templetStr =  templetStr + "var imgStr=\"\"; \r\n";
                        templetStr =  templetStr + "var picsArray = obj."+table.fields[i].propertyName+".split(\",\"); \r\n";
                        templetStr =  templetStr + "for (var i = 0; i < picsArray.length; i++) { \r\n";
                        templetStr =  templetStr + " if(picsArray[i] && picsArray[i].length>3){ \r\n";
                        templetStr =  templetStr + " imgStr =  imgStr+'<img style=\"width:200px;height: 200px\" src=\"'+ Feng.getAliImgUrl(picsArray[i]) +'\" class=\"layui-upload-img\">' \r\n";
                        templetStr =  templetStr + "  }\r\n";
                        templetStr =  templetStr + "    }\r\n";
                        templetStr =  templetStr + "   return imgStr;\r\n";
                        templetStr =  templetStr + "    },\r\n";
                }else if(strutil.contain (strutil.toLowerCase(table.fields[i].propertyName),"pic")) {
                        templetStr =  templetStr + " templet: function(d){";
                        templetStr =  templetStr + " return '<img style=\"width:200px;height: 200px\" src=\"'+ Feng.getAliImgUrl(d."+table.fields[i].propertyName+") +'\" class=\"layui-upload-img\">'";
                        templetStr =  templetStr + "  },";
                }

                fieldStr = fieldStr+minWidth+templetStr+"hide: false,"+title+"},\r\n";
                print(fieldStr);
            }
              %>
            {align: 'center', toolbar: '#tableBar',fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + ${context.bizEnBigName}.tableId,
        url: Feng.ctxPath + '/${context.bizEnName}/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: ${context.bizEnBigName}.initColumn()
    });
    /**
     * 点击查询按钮
     */
    ${context.bizEnBigName}.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(${context.bizEnBigName}.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    ${context.bizEnBigName}.openAddDlg = function () {
        <%if(context.isOpenDia){%>
        admin.putTempData('formOk', false);
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加${context.bizChName}',
            content:  Feng.ctxPath + '/${context.bizEnName}/${context.bizEnName}_add',
            end: function () {
                layer.closeAll('loading');
                table.reload(${context.bizEnBigName}.tableId);
            }
        });
        <%}else{%>
        window.location.href = Feng.ctxPath + '/${context.bizEnName}/${context.bizEnName}_add';
        <%}%>
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    ${context.bizEnBigName}.openEditDlg = function (data) {
        <%if(context.isOpenDia){%>
        admin.putTempData('formOk', false);
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加${context.bizChName}',
            content:  Feng.ctxPath + '/${context.bizEnName}/${context.bizEnName}_update?${context.bizEnName}Id=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(${context.bizEnBigName}.tableId);
            }
        });
        <%}else{%>
        window.location.href = Feng.ctxPath + '/${context.bizEnName}/${context.bizEnName}_update?${context.bizEnName}Id=' + data.id;
        <%}%>
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    ${context.bizEnBigName}.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/${context.bizEnName}/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(${context.bizEnBigName}.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("${context.bizEnName}Id", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * ${context.bizChName}详情
     */
    ${context.bizEnBigName}.detail = function (data) {
        <%if(context.isOpenDia){%>
        admin.putTempData('formOk', false);
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '${context.bizChName}详情',
            content:  Feng.ctxPath + '/${context.bizEnName}/${context.bizEnName}_detail?${context.bizEnName}Id=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(${context.bizEnBigName}.tableId);
            }
        });
        <%}else{%>
        window.location.href = Feng.ctxPath + '/${context.bizEnName}/${context.bizEnName}_detail?${context.bizEnName}Id=' + data.id;
        <%}%>
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        ${context.bizEnBigName}.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        ${context.bizEnBigName}.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + ${context.bizEnBigName}.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            ${context.bizEnBigName}.openEditDlg(data);
        } else if (layEvent === 'delete') {
            ${context.bizEnBigName}.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            ${context.bizEnBigName}.detail(data);
        }
    });
});


