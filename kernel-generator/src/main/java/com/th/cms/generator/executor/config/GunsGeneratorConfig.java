package com.th.cms.generator.executor.config;

import com.th.cms.generator.engine.base.GunsTemplateEngine;
import com.th.cms.generator.engine.config.ContextConfig;
import com.th.cms.generator.engine.config.PackageConfigTg;
import com.th.cms.generator.engine.config.SqlConfig;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class GunsGeneratorConfig{

    GlobalConfig globalConfig = new GlobalConfig();
    DataSourceConfig dataSourceConfig ;
    StrategyConfig strategyConfig = new StrategyConfig();
    PackageConfigTg packageConfig = new PackageConfigTg();
    TableInfo tableInfo = null;
    ContextConfig contextConfig ;
    SqlConfig sqlConfig;


    /**
     *
     * @param projectPath
     * @param bizEnName
     * @param bizChName
     * @param moduleName
     * @param proPackage
     * @param tableNames  表名
     * @param parentMenuName 父菜单名称
     * @param menuName  菜单名称
     * @param isOpenDia 模态框的形式 打开新增等
     * @param dataSourceConfig
     */
    public void init(String projectPath,String bizEnName,String bizChName,String moduleName,String proPackage,String tableNames,String parentMenuName,String menuName,boolean isOpenDia,DataSourceConfig dataSourceConfig){
        this.dataSourceConfig = dataSourceConfig;
        contextConfig = new ContextConfig(projectPath,bizEnName,moduleName,isOpenDia);
        ContextConfig.proPackage=proPackage;
        contextConfig.setCoreBasePackage(proPackage);
        contextConfig.setBizChName(bizChName);
        globalConfig.setOutputDir(projectPath+"\\src\\main\\java");

        packageConfig.setParent(null);
//        Map<String,String> pathMap = packageConfig.getPathInfo();
//        pathMap.put("proPackage",contextConfig.getProPackage());
//        packageConfig.setPathInfo(pathMap);
        packageConfig.setModelPackageName(contextConfig.getModPackage());
        config();
        packageConfig.setServiceImpl(contextConfig.getServicePackage());
        packageConfig.setEntity(contextConfig.getEntityPackage());
        packageConfig.setMapper(contextConfig.getMapperPackage());
        strategyConfig.setSuperMapperClass("com.baomidou.mybatisplus.core.mapper.BaseMapper");
        strategyConfig.setInclude(tableNames);

        sqlConfig = new SqlConfig(parentMenuName,menuName,dataSourceConfig,contextConfig);

    }

    protected void config() {
        globalConfig();
        strategyConfig();
    }

    protected void globalConfig() {
        globalConfig.setFileOverride(true);
        globalConfig.setEnableCache(false);
        globalConfig.setBaseResultMap(true);
        globalConfig.setBaseColumnList(true);
        globalConfig.setOpen(false);
        globalConfig.setAuthor("winad");
        globalConfig.setServiceImplName("%sService");
        globalConfig.setSwagger2(true);
        globalConfig.setDateType(DateType.ONLY_DATE);
    }


    protected void strategyConfig() {
        strategyConfig.setTablePrefix(new String[]{"sys_"});// 此处可以修改为您的表前缀
        strategyConfig.setNaming(NamingStrategy.underline_to_camel);
    }


    public StrategyConfig getStrategyConfig() {
        return strategyConfig;
    }


    public void doMpGeneration() {
        doMpGeneration(false);
    }
    public void doMpGeneration(boolean isOnlyGenMap) {
        AutoGenerator autoGenerator = new AutoGenerator();
        autoGenerator.setGlobalConfig(globalConfig);
        autoGenerator.setDataSource(dataSourceConfig);
        autoGenerator.setStrategy(strategyConfig);
        autoGenerator.setPackageInfo(packageConfig);

        TemplateConfig templateConfig = new TemplateConfig();
        if(isOnlyGenMap){
            templateConfig.setServiceImpl(null);
        }else{
            templateConfig.setServiceImpl("gunsTemplate/advanced/serviceImpl.java.vm");
        }
        templateConfig.setService(null);
        templateConfig.setXml(null);
        templateConfig.setController(null);
        templateConfig.setEntity("gunsTemplate/advanced/entity.java.vm");
        autoGenerator.setTemplate(templateConfig);
        autoGenerator.execute();

        //获取table信息,用于guns代码生成
        List<TableInfo> tableInfoList = autoGenerator.getConfig().getTableInfoList();
        if (tableInfoList != null && tableInfoList.size() > 0) {
            this.tableInfo = tableInfoList.get(0);
        }

        List<TableField> tableFieldList = tableInfo.getFields();
        for(TableField tableField : tableFieldList){
            if(tableField.isKeyFlag()){
                if(tableField.getColumnType().getType().toLowerCase().equals("string")){
                    contextConfig.setKeyInfo(tableField.getName(),true);
                }
            }
        }
        contextConfig.setTableInfo(tableInfo);
    }

    public void doGunsGeneration(GunsGeneratorConfig gunsGeneratorConfig,boolean isGenPage) {
        gunsGeneratorConfig.setPackageConfig(packageConfig);
        GunsTemplateEngine gunsTemplateEngine = new GunsTemplateEngine(gunsGeneratorConfig);
        sqlConfig.setConnection(dataSourceConfig.getConn());
        gunsTemplateEngine.start(packageConfig,isGenPage);
    }

}
