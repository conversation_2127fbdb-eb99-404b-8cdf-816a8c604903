package com.th.cms.generator.engine.config;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import lombok.Data;

/**
 * 全局配置
 *
 * <AUTHOR>
 */
@Data
public class ContextConfig {
    private String templatePrefixPath = "gunsTemplate/advanced";
    private String projectPath;//模板输出的项目目录
    private String bizChName;   //业务名称
    private String bizEnName;   //业务英文名称
    private String bizEnBigName;//业务英文名称(大写)
    private String moduleName;  //模块名称

    public static String proPackage = "com.th.cms";//核心类库
    public static String modPackage = "com.th.thdata";//当前类库

    public static void setProPackage(String proPackage) {
        ContextConfig.proPackage = proPackage;
    }

    public static String getModPackage() {
        return modPackage;
    }

    public static void setModPackage(String modPackage) {
        ContextConfig.modPackage = modPackage;
    }

    private String coreBasePackage = proPackage+".core";
    private String modelPackageName;        //model的包名
    private String modelMapperPackageName;    //model的dao
    private String entityName;              //实体的名称
    private String outputDir;

    private TableInfo tableInfo;
    private boolean isOpenDia = true;
    String keyName ;
    private Boolean isStrId = false;

    public ContextConfig(String projectPath,String bizEnName,String moduleName,boolean isOpenDia) {
        this.projectPath = projectPath;
        setBizEnName(bizEnName);
        if (entityName == null) {
            entityName = bizEnBigName;
        }
        this.moduleName = moduleName;
        modelPackageName = proPackage + "." + "modular."+moduleName;
        modelMapperPackageName = modelPackageName+".dao";
        this.isOpenDia = isOpenDia;
    }


    public String getProPackage(){
        return this.proPackage;
    }
    public void setBizEnName(String bizEnName) {
        this.bizEnName = bizEnName;
        this.bizEnBigName = StrUtil.upperFirst(this.bizEnName);
    }

    public void setKeyInfo(String keyName,Boolean isStrId){
        this.keyName = keyName;
        this.isStrId = isStrId;
    }
    public String getServicePackage(){
        return proPackage + ".modular." + getModuleName() + ".service";
    }
    public String getEntityPackage(){
        return proPackage + ".modular."+getModuleName()+".model";
    }
    public String getMapperPackage(){
        return proPackage + ".modular."+getModuleName()+".dao";
    }
}
