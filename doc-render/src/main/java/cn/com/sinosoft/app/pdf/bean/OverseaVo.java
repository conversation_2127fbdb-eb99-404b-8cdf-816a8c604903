package cn.com.sinosoft.app.pdf.bean;

import cn.com.sinosoft.app.pdf.AbstractDocumentVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class OverseaVo extends AbstractDocumentVo{
	private String dailyId;
	Map<String,String> pmap = new HashMap<>();
	Map<String,String> checkMap = new HashMap<>();
	List<Map<String,String>> routeList = new ArrayList<>();
	private String carLicense;
	public String findPrimaryKey() {
		return this.dailyId;
	}

	public String getCarLicense() {
		return carLicense;
	}

	public List<Map<String, String>> getRouteList() {
		return routeList;
	}

	public Map<String, String> getCheckMap() {
		return checkMap;
	}

	public void setCheckMap(Map<String, String> checkMap) {
		this.checkMap = checkMap;
	}

	public void setRouteList(List<Map<String, String>> routeList) {
		this.routeList = routeList;
	}

	public void setCarLicense(String carLicense) {
		this.carLicense = carLicense;
	}

	public String getDailyId() {
		return dailyId;
	}

	public void setDailyId(String dailyId) {
		this.dailyId = dailyId;
	}

	public Map<String, String> getPmap() {
		return pmap;
	}

	public void setPmap(Map<String, String> pmap) {
		this.pmap = pmap;
	}
}
