package com.th.thdata.gen;

import com.th.cms.generate.DataSourceConfigGen;
import com.th.cms.generator.engine.config.ContextConfig;
import com.th.cms.generator.executor.config.GunsGeneratorConfig;

public class BizOrderGenerator {
    public static void main(String[] args) {
//        createCode("bizImRecord", "bizImRecord", "biz_im_record", "聊天记录", "聊天记录", "IM管理", false, true, false);
        createCode("thAccount", "thAccount", "th_account", "达人账户", "达人账户", "达人管理", false, true, false);

    }

    static String projectPath = BizOrderGenerator.class.getResource("/").getPath().split("\\/target")[0];

    /**
     * @param bizEnName      "bizTripDay";
     * @param moduleName     "trip";
     * @param tableName      "biz_trip_day";
     * @param bizCnName      "行程";
     * @param menuName       "行程详情";
     * @param parentMenuName "行程管理";
     * @param isOpenDia      是否生成页面
     * @param isOnlyGenMap   是否只生成mybatis
     */
    private static void createCode(String bizEnName, String moduleName, String tableName, String bizCnName, String menuName, String parentMenuName, boolean isOpenDia, boolean isCreateHtmlPage, boolean isOnlyGenMap) {
        String proPackage = "com.th.thdata";
        ContextConfig.proPackage="com.th.thdata";
        ContextConfig.modPackage="com.th.cms";
        GunsGeneratorConfig gunsGeneratorConfig = new GunsGeneratorConfig();
        gunsGeneratorConfig.init(projectPath, bizEnName, bizCnName, moduleName, proPackage, tableName, parentMenuName, menuName, isOpenDia, DataSourceConfigGen.dataSourceConfig());
        gunsGeneratorConfig.doMpGeneration(isOnlyGenMap);
        /**
         * guns的生成器:
         *      guns的代码生成器可以生成controller,html页面,页面对应的js
         */
        if (!isOnlyGenMap) {
            gunsGeneratorConfig.doGunsGeneration(gunsGeneratorConfig, isCreateHtmlPage);
        }
    }
}
