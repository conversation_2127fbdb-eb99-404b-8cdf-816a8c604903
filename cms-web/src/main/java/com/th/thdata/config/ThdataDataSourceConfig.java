package com.th.thdata.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;

@Slf4j
@Configuration
@MapperScan(basePackages = {"com.th.thdata.modular.*"}, sqlSessionFactoryRef = "thdataSqlSessionFactory")
public class ThdataDataSourceConfig {

    @Value("${spring.datasource.thdata.username}")
    private String userName;

    @Value("${spring.datasource.thdata.password}")
    private String passWord;

    @Value("${spring.datasource.thdata.jdbc-url}")
    private String url;

    @Value("${spring.datasource.driverClass}")
    private String driverClass;

    @Value("${spring.datasource.maxActive}")
    private Integer maxActive;

    @Value("${spring.datasource.maxWait}")
    private Integer maxWait;

    @Value("${spring.datasource.initialSize}")
    private Integer initialSize;

    @Value("${spring.datasource.minIdle}")
    private Integer minIdle;

    @Value("${spring.datasource.time-between-eviction-runs-millis}")
    private Long timeBetween;

    @Value("${spring.datasource.min-evictable-idle-time-millis}")
    private Long minEvictableIdle;

    @Value("${spring.datasource.validation-query}")
    private String validationQuery;

    @Value("${spring.datasource.keep-alive}")
    private Boolean keepAlive;

    @Value("${spring.datasource.remove-abandoned}")
    private Boolean removeAbandoned;

    @Value("${spring.datasource.remove-abandoned-time}")
    private Integer removeAbandonedTime;

    @Value("${spring.datasource.test-on-borrow}")
    private Boolean testOnBorrow;

    @Value("${spring.datasource.test-on-return}")
    private Boolean testOnReturn;

    @Value("${spring.datasource.test-while-idle}")
    private Boolean testWhileIdle;

    @Bean(name = "datasourceThdata")
    @Order(-1)
    public DataSource datasourceThdata() throws SQLException{
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setUsername(userName);
        druidDataSource.setPassword(passWord);
        druidDataSource.setUrl(url);
        druidDataSource.setDriverClassName(driverClass);
        druidDataSource.setMaxActive(maxActive);
        // 配置从连接池获取连接等待超时的时间
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMinIdle(minIdle);
        // 配置间隔多久启动一次DestroyThread，对连接池内的连接才进行一次检测，单位是毫秒。
        // 检测时:1.如果连接空闲并且超过minIdle以外的连接，如果空闲时间超过minEvictableIdleTimeMillis设置的值则直接物理关闭。2.在minIdle以内的不处理。
        druidDataSource.setTimeBetweenEvictionRunsMillis(timeBetween);
        // 配置一个连接在池中最大空闲时间，单位是毫秒
        druidDataSource.setMinEvictableIdleTimeMillis(minEvictableIdle);
        // 设置从连接池获取连接时是否检查连接有效性，true时，每次都检查;false时，不检查
        druidDataSource.setTestOnBorrow(testOnBorrow);
        // 设置往连接池归还连接时是否检查连接有效性，true时，每次都检查;false时，不检查
        druidDataSource.setTestOnReturn(testOnReturn);
        // 设置从连接池获取连接时是否检查连接有效性，true时，如果连接空闲时间超过minEvictableIdleTimeMillis进行检查，否则不检查;false时，不检查
        druidDataSource.setTestWhileIdle(testWhileIdle);
        // 检验连接是否有效的查询语句。如果数据库Driver支持ping()方法，则优先使用ping()方法进行检查，否则使用validationQuery查询进行检查。(Oracle jdbc Driver目前不支持ping方法)
        druidDataSource.setValidationQuery(validationQuery);
        // 打开后，增强timeBetweenEvictionRunsMillis的周期性连接检查，minIdle内的空闲连接，每次检查强制验证连接有效性. 参考：https://github.com/alibaba/druid/wiki/KeepAlive_cn
        druidDataSource.setKeepAlive(keepAlive);
        // 连接泄露检查，打开removeAbandoned功能 , 连接从连接池借出后，长时间不归还，将触发强制回连接。回收周期随timeBetweenEvictionRunsMillis进行，如果连接为从连接池借出状态，并且未执行任何sql，并且从借出时间起已超过removeAbandonedTimeout时间，则强制归还连接到连接池中。
        druidDataSource.setRemoveAbandoned(removeAbandoned);
        // 超时时间，秒
        druidDataSource.setRemoveAbandonedTimeout(removeAbandonedTime);
        druidDataSource.init();
        return druidDataSource;
    }

    @Bean(name="thdataSqlSessionFactory")
    @ConditionalOnBean(name = "datasourceThdata")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceThdata") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
//        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
//        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));

        PaginationInterceptor interceptor = new PaginationInterceptor();
        interceptor.setDialectType(DbType.MYSQL.getDb());

        mybatisSqlSessionFactoryBean.setPlugins(new Interceptor[]{interceptor});

        return mybatisSqlSessionFactoryBean.getObject();
    }

}
