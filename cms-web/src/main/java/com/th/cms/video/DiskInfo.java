package com.th.cms.video;

public class DiskInfo {

    public  static  String getFileD(String path){
        String diskPattern = "([A-Za-z]:)\\\\"; // 匹配Windows格式的盘符
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(diskPattern);
        java.util.regex.Matcher matcher = pattern.matcher(path);

        if (matcher.find()) {
            String disk = matcher.group(1); // 磁盘信息
            System.out.println("Disk: " + disk);
            return disk;
        } else {
            System.out.println("Cannot find disk information.");
        }
        return null;
    }
}