package com.th.cms.video;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @date 2024年12月22日 19:54
 */
@Slf4j
public class CmdService {
    public static Integer execCommand(String command) throws Exception {
        Process process;
        if (WinPlatInfo.isLinux()) {
            String[] commands = {"/bin/sh", "-c", command};
            process = Runtime.getRuntime().exec(commands);
        } else {
            String[] commands = {"cmd", "/c", command};
            process = Runtime.getRuntime().exec(commands);
        }
        //获取进程的标准输入流
        final InputStream is1 = process.getInputStream();
        //获取进城的错误流
        final InputStream is2 = process.getErrorStream();
        //启动两个线程，一个线程负责读标准输出流，另一个负责读标准错误流
        readInputStream(is1);
        readInputStream(is2);
//        String errorLog = FileUtil.readContent(is2);
//        if (StringUtils.isNotEmpty(errorLog)) {
//            log.error("-----合并操作错误流: {}", errorLog);
//        }
        process.waitFor();
        process.destroy();
        log.info("-----操作成功" + command);
        return 1;
    }


    public static Integer execCommand2(String command,String command2) throws Exception {
        Process process;
        if (WinPlatInfo.isLinux()) {
            String[] commands = {"/bin/sh", "-c", command,command2};
            process = Runtime.getRuntime().exec(commands);
        } else {
            String[] commands = {"cmd", "/c", command,command2};
            process = Runtime.getRuntime().exec(commands);
        }
        //获取进程的标准输入流
        final InputStream is1 = process.getInputStream();
        //获取进城的错误流
        final InputStream is2 = process.getErrorStream();
        //启动两个线程，一个线程负责读标准输出流，另一个负责读标准错误流
        readInputStream(is1);
        readInputStream(is2);
//        String errorLog = FileUtil.readContent(is2);
//        if (StringUtils.isNotEmpty(errorLog)) {
//            log.error("-----合并操作错误流: {}", errorLog);
//        }
        process.waitFor();
        process.destroy();
        log.info("-----操作成功" + command);
        return 1;
    }

    private static void readInputStream(InputStream inputStream) {
        new Thread(() -> {
            BufferedReader br1 = new BufferedReader(new InputStreamReader(inputStream));
            try {
                String line1 = null;
                while ((line1 = br1.readLine()) != null) {
                    if (line1 != null) {
                        log.info(line1);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }
}
