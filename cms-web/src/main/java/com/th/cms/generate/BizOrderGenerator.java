package com.th.cms.generate;

import com.th.cms.generator.executor.config.GunsGeneratorConfig;

public class BizOrderGenerator {
    public static void main(String[] args) {
        //biz_cusplat
//        createCode("bizCusplat", "bizCusplat", "biz_cusplat", "服务渠道", "服务渠道", "消息管理", false, true, false);
//        createCode("bizPlatform", "bizPlatform", "biz_platform", "平台设置", "平台设置", "基础管理", true, true, true);
//        createCode("bizNomalEnums", "bizNomalEnums", "biz_nomal_enums", "类型设置", "类型设置", "基础管理", true, true, true);
//biz_settle_fencheng
//        createCode("bizSettleFencheng", "bizSettleFencheng", "biz_settle_fencheng", "结算比例", "结算比例", "基础管理", true, true, false);

//        createCode("bizCustomerIm", "bizCustomerIm", "biz_customer_im", "客服im", "客服im", "消息管理", true, false, true);
//        createCode("bizWorkorder", "bizWorkorder", "biz_workorder", "客服工单", "客服工单", "消息管理", true, true, true);
//        biz_im_conf
//        createCode("bizImConf", "bizImConf", "biz_im_conf", "接待规则", "接待规则", "消息管理", true, true, true);
//        createCode("bizImRecord", "bizImRecord", "biz_im_record", "聊天记录", "聊天记录", "IM管理", true, true, true);
//        createCode("bizImLk", "bizImLk", "biz_im_lk", "服务单", "服务单", "IM管理", true, true, true);

//        createCode("bizCustomer", "bizCustomer", "biz_customer", "客服设置", "客服设置", "消息管理", true, true, false);
//        createCode("bizAppPolicyContent", "bizAppPolicyContent", "biz_app_policy_content", "政策内容管理", "政策内容管理", "APP管理", true, true, false);
//        createCode("bizInflucer", "bizInflucer", "biz_influcer", "达人认证", "达人认证", "达人管理", true, true, false);

        //biz_influcer_platform
//        createCode("bizInflucerPlatform", "influcer", "biz_influcer_platform", "达人平台", "达人平台", "达人管理", false, true, true);
//        createCode("bizInflucerBusi", "bizInflucerBusi", "biz_influcer_busi", "达人商务", "达人商务", "达人管理", false, true, true);
//        createCode("settleAccount", "settleAccount", "settle_account", "资金账户", "资金账户", "结算管理", false, true, true);
//        createCode("settleAccountLog", "settleAccount", "settle_account_log", "账户流水", "账户流水", "结算管理", true, true, true);
//                createCode("settleAccount", "settleAccount", "settle_plat_account", "平台资金账户", "平台资金账户", "结算管理", false, true, false);
        createCode("settleWithdraw", "settleAccount", "settle_withdraw", "提现管理", "提现管理", "结算管理", false, true, true);
//        createCode("settleWithdrawAccount", "settleAccount", "settle_withdraw_account", "提现账户", "提现账户", "结算管理", false, true, true);

//        settle_withdraw_account
//        createCode("bizInflucerTop", "settleAccount", "biz_influcer_top", "达人排行", "达人排行", "APP管理", true, true, true);
//        createCode("imGroup", "imGroup", "im_group", "群组管理", "群组管理", "消息管理", true, true, false);
//        createCode("imGroupUser", "imGroupUser", "im_group_user", "群组用户", "群组用户", "消息管理", true, true, false);

        //settle_projects
        //settle_influencer_order
//        createCode("settleInfluencerOrder", "settleInfluencerOrder", "settle_influencer_order", "结算管理", "结算管理", "结算管理", false, true, true);
//        createCode("settleInfluencerOrder", "settleInfluencerOrder", "settle_influencer_order", "结算管理", "结算管理", "结算管理", false, true, true);
        //settle_withdraw_project
//        createCode("settleBatch", "settleBatch", "settle_batch", "结算批次", "结算批次", "结算管理", false, true, true);
//        createCode("settleOrder", "settleOrder", "settle_order", "结算单", "结算单", "结算管理", false, true, true);
//        createCode("settleWithdrawBatch", "settleAccount", "settle_withdraw_batch", "提现管理", "提现管理", "结算管理", false, true, false);

//        createCode("settleProjects", "settleProjects", "settle_projects", "立项管理", "立项管理", "项目管理", true, true, true);

//        createCode("bizWorktype", "bizWorktype", "biz_worktype", "工单分类", "工单分类", "基础管理", true, true, false);
//wf_type
//wf_step
//wf_step_relation
//wf_approver
//wf_approval_request
//wf_approval_record

//        createCode("wfType", "wfType", "wf_type", "流程类型", "流程类型", "审批流", true, true, false);
//        createCode("wfStep", "wfStep", "wf_step", "流程步骤", "流程步骤", "审批流", true, true, true);
//        createCode("wfStepRelation", "wfStepRelation", "wf_step_relation", "步骤关联", "步骤关联", "审批流", true, true, true);
//        createCode("wfApprover", "wfApprover", "wf_approver", "审批人", "审批人", "审批流", true, true, false);
//        createCode("wfApprovalRequest", "wfApprovalRequest", "wf_approval_request", "审批请求", "审批请求", "审批流", true, true, true);
//        createCode("wfApprovalRecord", "wfApprovalRecord", "wf_approval_record", "审批记录", "审批记录", "审批流", true, true, true);
//        createCode("settleTemplateExcel", "settleTemplateExcel", "settle_template_excel", "结算模版", "结算模版", "基础管理", true, true, true);
//        createCode("settleTemplateField", "settleTemplateField", "settle_template_field", "模版字段", "模版字段", "基础管理", true, true, true);

    }

    static String projectPath = BizOrderGenerator.class.getResource("/").getPath().split("\\/target")[0];

    /**
     * @param bizEnName      "bizTripDay";
     * @param moduleName     "trip";
     * @param tableName      "biz_trip_day";
     * @param bizCnName      "行程";
     * @param menuName       "行程详情";
     * @param parentMenuName "行程管理";
     * @param isOpenDia      是否生成页面
     * @param isOnlyGenMap   是否只生成mybatis
     */
    private static void createCode(String bizEnName, String moduleName, String tableName, String bizCnName, String menuName, String parentMenuName, boolean isOpenDia, boolean isCreateHtmlPage, boolean isOnlyGenMap) {
        String proPackage = "com.th.cms";
        GunsGeneratorConfig gunsGeneratorConfig = new GunsGeneratorConfig();
        gunsGeneratorConfig.init(projectPath, bizEnName, bizCnName, moduleName, proPackage, tableName, parentMenuName, menuName, isOpenDia, DataSourceConfigGen.dataSourceConfig());
        gunsGeneratorConfig.doMpGeneration(isOnlyGenMap);
        /**
         * guns的生成器:
         *      guns的代码生成器可以生成controller,html页面,页面对应的js
         */
        if (!isOnlyGenMap) {
            gunsGeneratorConfig.doGunsGeneration(gunsGeneratorConfig, isCreateHtmlPage);
        }
    }
}
