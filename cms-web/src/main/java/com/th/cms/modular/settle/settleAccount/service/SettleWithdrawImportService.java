package com.th.cms.modular.settle.settleAccount.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.stylefeng.roses.core.util.DateUtils;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.IdGenerater;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.enums.WithdrawExportStatusEnum;
import com.th.cms.modular.enums.WithdrawStatus;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformService;
import com.th.cms.modular.oss.service.AliUploadOssImg;
import com.th.cms.modular.settle.settleAccount.mapper.SettleWithdrawImportMapper;
import com.th.cms.modular.settle.settleAccount.model.SettleWithdraw;
import com.th.cms.modular.settle.settleAccount.model.SettleWithdrawImport;
import com.th.cms.modular.settle.settleAccount.model.excelTemplate.SettleWithdrawImportExcel;
import com.th.cms.modular.settle.settleAccount.model.reqparam.ImportSettleWithdrawFileListParam;
import com.th.cms.modular.settle.settleInfluencerOrder.service.SettleInfluencerOrderExtService;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import com.th.cms.modular.settle.settleProjects.third.MessageProjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-17
 */
@Slf4j
@Service
public class SettleWithdrawImportService extends ServiceImpl<SettleWithdrawImportMapper, SettleWithdrawImport> implements IService<SettleWithdrawImport> {

    @Autowired
    private SettleWithdrawService settleWithdrawService;
    @Autowired
    private MessageProjectHelper messageProjectHelper;

    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(ImportSettleWithdrawFileListParam param) {
        QueryWrapper<SettleWithdrawImport> objectQueryWrapper = new QueryWrapper<>();
        if (Objects.nonNull(param.getAuditStatus())) {
            objectQueryWrapper.lambda().eq(SettleWithdrawImport::getAuditStatus, param.getAuditStatus());
        }
        /*if(StringUtils.isNotBlank(param.getNickName())){
            objectQueryWrapper.lambda().like(SettleWithdraw::getNickName," %"+param.getNickName()+"% ");
        }
        if(StringUtils.isNotBlank(param.getExportStatus())){
            objectQueryWrapper.lambda().eq(SettleWithdraw::getExportStatus,param.getExportStatus());
        }

        if(StringUtils.isNotBlank(param.getTixianStatus())){
            objectQueryWrapper.lambda().eq(SettleWithdraw::getWithdrawStatus,param.getTixianStatus());
        }
        if (StringUtils.isNotBlank(param.getUserId())){
            objectQueryWrapper.lambda().like(SettleWithdraw::getUserId,param.getUserId());
        }*/

        LayuiPageInfo<SettleWithdrawImport> layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;

    }

    /**
     * 审核拒绝
     *
     * @param settleWithdrawImport
     */
    @Transactional
    public void reject(SettleWithdrawImport settleWithdrawImport) {
        SettleWithdrawImport withdrawImport = this.getById(settleWithdrawImport.getId());
        withdrawImport.setAuditStatus(2);
        withdrawImport.setRejectFile(settleWithdrawImport.getRejectFile());
        withdrawImport.setRejectReason(settleWithdrawImport.getRejectReason());
        this.updateById(withdrawImport);
        messageProjectHelper.settleWithdrawReject(withdrawImport);
    }

    @Autowired
    SettleAccountService settleAccountService;

    @Autowired
    SettleAccountLogService settleAccountLogService;

    @Autowired
    SettleProjectsService settleProjectsService;

    @Transactional
    public void approve(Long id) {
        SettleWithdrawImport withdrawImport = this.getById(id);
        SettleWithdrawImport settleWithdrawImport = new SettleWithdrawImport();
        settleWithdrawImport.setId(withdrawImport.getId());
        Assert.notNull(settleWithdrawImport, "数据不存在");
        Assert.isTrue(withdrawImport.getAuditStatus() == 0, "已经审核过了，不要重复审核");
        settleWithdrawImport.setAuditStatus(1);
        ShiroUtils.setUpdateAuthInfo(settleWithdrawImport);

        this.updateById(settleWithdrawImport);
        File tmpFile = null;
        try {
//            tmpFile = AliUploadOssImg.saveTolotmp(settleWithdrawImport.getMainFile(), AliUploadOssImg.downloadFile(settleWithdrawImport.getMainFile(), true));
            //下面的是本级调试用的，请勿删除
            tmpFile = AliUploadOssImg.saveToTmpFullPath(new File("").getAbsolutePath() + File.separator + AliUploadOssImg.getFileNameFromUrl(settleWithdrawImport.getMainFile()), AliUploadOssImg.downloadFile(settleWithdrawImport.getMainFile(), true));
        } catch (Exception e) {
            log.error("下载文件报错", e);
            throw new RuntimeException("下载文件报错");
        }
        try {
            //文件去重操作
            List<SettleWithdrawImportExcel> excelList = EasyExcel.read(tmpFile).head(SettleWithdrawImportExcel.class).doReadAllSync();
            Assert.notEmpty(excelList, "没有找到需要导入的数据");
            List<Integer> settleWithdrawIds = excelList.stream().filter(item -> item.getStatus() == 2).map(SettleWithdrawImportExcel::getFlowNo
            ).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(settleWithdrawIds)) {
                List<SettleWithdraw> settleWithdrawList = settleWithdrawService.lambdaQuery().in(SettleWithdraw::getId, settleWithdrawIds).eq(SettleWithdraw::getWithdrawStatus, WithdrawStatus.INIT.getCode()).list();
                if (CollectionUtil.isNotEmpty(settleWithdrawList)) {
                    settleWithdrawList.forEach(x -> {
                        x.setExportStatus(WithdrawExportStatusEnum.yidaoru.getValue());
                        x.setWithdrawStatus(WithdrawStatus.Yitixian.getCode());
                        x.setWithdrawStatusName(WithdrawStatus.Yitixian.getName());
                        settleWithdrawService.updateWithDrawStatusBatch(x, StringUtils.EMPTY);
                    });
                }
            }

            List<Integer> settleWithdrawFailedIds = excelList.stream().filter(item -> item.getStatus() == -1).map(SettleWithdrawImportExcel::getFlowNo
            ).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(settleWithdrawFailedIds)) {
                Map<Integer, String> idFailedReasonMap = excelList.stream().filter(item -> item.getStatus() == -1).collect(Collectors.toMap(SettleWithdrawImportExcel::getFlowNo, SettleWithdrawImportExcel::getFailedReason));
                List<SettleWithdraw> settleWithdrawFailedList = settleWithdrawService.lambdaQuery().in(SettleWithdraw::getId, settleWithdrawFailedIds).eq(SettleWithdraw::getWithdrawStatus, WithdrawStatus.INIT.getCode()).list();
                if (CollectionUtil.isNotEmpty(settleWithdrawFailedList)) {
                    settleWithdrawFailedList.forEach(x -> {
                        x.setExportStatus(WithdrawExportStatusEnum.yidaoru.value);
                        x.setWithdrawStatus(WithdrawStatus.tixianFailed.getCode());
                        x.setWithdrawStatusName(WithdrawStatus.tixianFailed.getName());
                        x.setFailedReason(idFailedReasonMap.get(x.getId()));
                        String failedReason = "提现记录ID" + x.getId() + "提现失败，原因为" + x.getFailedReason();
                        settleWithdrawService.updateWithDrawStatusBatch(x,  failedReason);
                    });
                }
            }
        } finally {
            FileUtil.del(tmpFile);
        }

        //发送消息之类的
        messageProjectHelper.settleWithdrawApprove(withdrawImport);
    }

    @Autowired
    private SettleInfluencerOrderExtService settleInfluencerOrderExtService;

    @Autowired
    private BizPlatformService bizPlatformService;

    @Autowired
    private IdGenerater idGenerater;

    private String genBatchNo(String platformId) {
        BizPlatform bizPlatform = bizPlatformService.getById(platformId);

        String jianxie = "";
        if (bizPlatform != null && StringUtils.isNotBlank(bizPlatform.getJiancheng())) {
            jianxie = bizPlatform.getJiancheng();
        }

        String dateStr = DateUtils.getyymmCurrentDate();
        //th-pdd20250328
        Long seqNum = idGenerater.genSeqNo(jianxie + dateStr, DateTimeConstants.SECONDS_PER_DAY * 3);
        String batchNo = "tx-" + jianxie + dateStr + "-" + seqNum;
        return batchNo;
    }
}
