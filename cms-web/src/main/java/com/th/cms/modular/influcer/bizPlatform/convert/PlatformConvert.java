package com.th.cms.modular.influcer.bizPlatform.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.th.cms.modular.influcer.bizPlatform.dto.BizPlatformAddRequestDTO;
import com.th.cms.modular.influcer.bizPlatform.dto.BizPlatformDetailDTO;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatformIntroduction;

import java.util.Date;
import java.util.List;

public class PlatformConvert {
    public static BizPlatform convertPlatform(BizPlatformAddRequestDTO record) {
        BizPlatform bizPlatform = new BizPlatform();
        bizPlatform.setId(record.getId());
        bizPlatform.setPlatName(record.getPlatName());
        bizPlatform.setIconPic(record.getIconPic());
        bizPlatform.setParentId(record.getParentId());
        bizPlatform.setLevels(record.getLevels());
        bizPlatform.setJiancheng(record.getJiancheng());
        bizPlatform.setCreateTime(record.getId() == null ? new Date() : null);
        bizPlatform.setUpdateTime(new Date());
        return bizPlatform;

    }

    public static BizPlatformDetailDTO convertDetail(BizPlatform bizPlatform, List<BizPlatformIntroduction> bizPlatformIntroductions) {
        BizPlatformDetailDTO bizPlatformDetailDTO = new BizPlatformDetailDTO();
        bizPlatformDetailDTO.setId(bizPlatform.getId());
        bizPlatformDetailDTO.setPlatName(bizPlatform.getPlatName());
        bizPlatformDetailDTO.setIconPic(bizPlatform.getIconPic());
        bizPlatformDetailDTO.setParentId(bizPlatform.getParentId());
        bizPlatformDetailDTO.setLevels(bizPlatform.getLevels());
        bizPlatformDetailDTO.setJiancheng(bizPlatform.getJiancheng());
        if (CollectionUtil.isNotEmpty(bizPlatformIntroductions)) {
            bizPlatformDetailDTO.setIntroduction(bizPlatformIntroductions.get(0).getIntroduction());
        }
        return bizPlatformDetailDTO;


    }
}
