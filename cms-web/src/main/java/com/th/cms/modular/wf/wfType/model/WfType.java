package com.th.cms.modular.wf.wfType.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.th.cms.modular.wf.wfStep.model.WfStep;
import com.th.cms.modular.wf.wfStep.model.WfStepVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="WfType对象", description="")
public class WfType implements Serializable {


    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "流程编号")
    @TableField("type_no")
    private String typeNo;

    @ApiModelProperty(value = "流程类型名称")
    @TableField("type_name")
    private String typeName;

    @ApiModelProperty(value = "流程分类 高级审核  提审审核")
    @TableField("classify_type")
    private String classifyType;

    @ApiModelProperty(value = "流程业务类型")
    @TableField("type")
    private String type;

    @ApiModelProperty(value = "流程审批状态")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "是否启用 0禁用 1启用")
    @TableField("is_active")
    private Integer isActive;

    @ApiModelProperty(value = "创建人")
    @TableField("create_id")
    private Long createId;

    @ApiModelProperty(value = "创建人name")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "审批单id")
    @TableField("request_id")
    private Long requestId;

    @TableField(exist = false)
    private List<WfStep> stepList;

    @TableField(exist = false)
    private Long approveId;

    @ApiModelProperty(value = "节点操作详情")
    @TableField(exist = false)
    private List<WfStepVO> approveRecordList;

    @ApiModelProperty(value = "节点操作操作标记 AssignPermissonType")
    @TableField(exist = false)
    private Integer operateFlag;

    @ApiModelProperty(value = "待审批记录id")
    @TableField(exist = false)
    private Long recordId;

}
