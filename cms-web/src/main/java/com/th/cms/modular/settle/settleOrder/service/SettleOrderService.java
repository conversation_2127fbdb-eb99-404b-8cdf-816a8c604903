package com.th.cms.modular.settle.settleOrder.service;

import com.th.cms.modular.enums.SettleBatchStatus;
import com.th.cms.modular.settle.settleBatch.model.SettleBatch;
import com.th.cms.modular.settle.settleBatch.service.SettleBatchService;
import com.th.cms.modular.settle.settleOrder.model.SettleOrder;
import com.th.cms.modular.settle.settleOrder.dao.SettleOrderMapper;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.settle.settleOrder.model.SettleOrderProject;
import com.th.cms.modular.settle.settleOrder.model.reqparam.SettleOrderListParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.IService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 达人结算订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class SettleOrderService extends ServiceImpl<SettleOrderMapper, SettleOrder> implements IService<SettleOrder> {
    @Autowired
    SettleProjectsService settleProjectsService;
    @Autowired
    SettleOrderExtService settleOrderExtService;
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(SettleOrderListParam param) {
        QueryWrapper<SettleOrder> objectQueryWrapper = new QueryWrapper<>();

        // 根据项目简称查询
        if (StringUtils.isNotBlank(param.getProjectName())) {
            objectQueryWrapper.lambda().like(SettleOrder::getProjectName, param.getProjectName());
        }

        // 根据营收金额范围查询
        if (StringUtils.isNotBlank(param.getSettleStatus())) {
            if ("1".equals(param.getSettleStatus())) {
                // 10万以下
                objectQueryWrapper.lambda().lt(SettleOrder::getLiushuiAmount, 100000);
            } else if ("2".equals(param.getSettleStatus())) {
                // 10-20万
                objectQueryWrapper.lambda().ge(SettleOrder::getLiushuiAmount, 100000)
                    .le(SettleOrder::getLiushuiAmount, 200000);
            }
        }

        // 如果有SettleProjects相关的查询条件，先过滤项目
        List<Integer> filteredProjectIds = null;
        if (StringUtils.isNotBlank(param.getBusinessContact()) ||
            StringUtils.isNotBlank(param.getProjectTypeName()) ||
            StringUtils.isNotBlank(param.getCommissionRate()) ||
            StringUtils.isNotBlank(param.getPlatform1()) ||
            StringUtils.isNotBlank(param.getPlatform2()) ||
            StringUtils.isNotBlank(param.getPlatform3())) {

            QueryWrapper<SettleProjects> projectQueryWrapper = new QueryWrapper<>();

            // 根据项目负责人查询
            if (StringUtils.isNotBlank(param.getBusinessContact())) {
                projectQueryWrapper.lambda().like(SettleProjects::getBusinessOwnerName, param.getBusinessContact());
            }

            // 根据项目类型查询
            if (StringUtils.isNotBlank(param.getProjectTypeName())) {
                projectQueryWrapper.lambda().eq(SettleProjects::getProjectTypeName, param.getProjectTypeName());
            }

            // 根据分成比例查询
            if (StringUtils.isNotBlank(param.getCommissionRate())) {
                projectQueryWrapper.lambda().eq(SettleProjects::getCommissionRate, param.getCommissionRate());
            }

            // 根据一级平台查询
            if (StringUtils.isNotBlank(param.getPlatform1())) {
                projectQueryWrapper.lambda().eq(SettleProjects::getYijiPlatform, param.getPlatform1());
            }

            // 根据二级平台查询
            if (StringUtils.isNotBlank(param.getPlatform2())) {
                projectQueryWrapper.lambda().eq(SettleProjects::getErjiPlatform, param.getPlatform2());
            }

            // 根据三级平台查询
            if (StringUtils.isNotBlank(param.getPlatform3())) {
                projectQueryWrapper.lambda().eq(SettleProjects::getSanjiPlatform, param.getPlatform3());
            }

            List<SettleProjects> filteredProjects = settleProjectsService.list(projectQueryWrapper);
            filteredProjectIds = filteredProjects.stream().map(SettleProjects::getId).collect(Collectors.toList());

            // 如果没有匹配的项目，返回空结果
            if (filteredProjectIds.isEmpty()) {
                LayuiPageInfo emptyResult = new LayuiPageInfo();
                emptyResult.setData(new ArrayList<>());
                emptyResult.setCount(0);
                return emptyResult;
            }

            // 添加项目ID过滤条件
            objectQueryWrapper.lambda().in(SettleOrder::getProjectId, filteredProjectIds);
        }

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);

        List<SettleOrder> settleOrderList = layuiPageInfo.getData();

        List<Integer> projectIdsList = settleOrderList.stream().map(SettleOrder::getProjectId).collect(Collectors.toList());
        Map<Integer,SettleProjects> dmap = new HashMap<>();
        if(projectIdsList.size()>0){
            List<SettleProjects> projects = settleProjectsService.lambdaQuery().in(SettleProjects::getId, projectIdsList).list();
            dmap = projects.stream().collect(Collectors.toMap(SettleProjects::getId,a->a));
        }

        Map<Integer, SettleProjects> finalDmap = dmap;
        List<SettleOrderProject> settleOrderProjectList = settleOrderList.stream().map(a -> {
            SettleOrderProject settleOrderProject = new SettleOrderProject();
            SettleProjects settleProjects = finalDmap.get(a.getProjectId());
            settleOrderProject.setSettleOrder(a);
            settleOrderProject.setSettleProjects(settleProjects);
            return settleOrderProject;
        }).collect(Collectors.toList());
        List<Integer> settleOrderIdList = settleOrderList.stream().map(SettleOrder::getId).collect(Collectors.toList());
        List<SettleBatch> list = settleBatchService.lambdaQuery().in(SettleBatch::getOrderId, settleOrderIdList).list();
        List<Long> orderIdList = list.stream().filter(a -> !a.getBatchStatus().equals(SettleBatchStatus.Yifa.getCode())).map(SettleBatch::getOrderId).collect(Collectors.toList());
        settleOrderProjectList.stream().forEach(a -> {
            if (orderIdList.contains(a.getSettleOrder().getId().longValue())) {
                a.setSettleBatchStatusName(SettleBatchStatus.ShengPiing.getName());
            }else {
                if (!StringUtils.equals(a.getSettleBatchStatusName(),SettleBatchStatus.ShengPiing.getName())){
                    a.setSettleBatchStatusName(SettleBatchStatus.Yifa.getName());
                }
            }
        });
        settleOrderExtService.querySettleOrders(settleOrderList);

        layuiPageInfo.setData(settleOrderProjectList);
        return layuiPageInfo;

    }

    @Autowired
    SettleBatchService settleBatchService;
}
