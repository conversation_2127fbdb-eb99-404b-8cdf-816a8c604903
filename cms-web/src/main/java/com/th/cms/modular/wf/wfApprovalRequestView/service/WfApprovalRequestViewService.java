package com.th.cms.modular.wf.wfApprovalRequestView.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.modular.wf.wfApprovalRequestView.dao.WfApprovalRequestViewMapper;
import com.th.cms.modular.wf.wfApprovalRequestView.model.WfApprovalRequestView;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class WfApprovalRequestViewService extends ServiceImpl<WfApprovalRequestViewMapper, WfApprovalRequestView> implements IService<WfApprovalRequestView> {

}
