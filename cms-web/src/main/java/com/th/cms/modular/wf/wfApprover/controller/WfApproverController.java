package com.th.cms.modular.wf.wfApprover.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.wf.wfApprover.model.WfApprover;
import com.th.cms.modular.wf.wfApprover.service.WfApproverService;
import com.th.cms.modular.wf.wfApprover.service.WfApproverExtService;
import com.th.cms.modular.wf.wfApprover.model.reqparam.WfApproverListParam;

/**
 * 审批人控制器
 *
 * <AUTHOR>
 * @Date 2025-03-27 15:32:37
 */
@Controller
@RequestMapping("/wfApprover")
public class WfApproverController extends BaseController {

    private String PREFIX = "/modular/wfApprover/wfApprover/";
    @Autowired
    WfApproverExtService wfApproverExtService;
    @Autowired
    private WfApproverService wfApproverService;

    /**
     * 跳转到审批人首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "wfApprover.html";
    }

    /**
     * 跳转到添加审批人
     */
    @Permission
    @RequestMapping("/wfApprover_add")
    public String wfApproverAdd() {
        return PREFIX + "wfApprover_add.html";
    }

    /**
     * 跳转到修改审批人
     */
    @Permission
    @RequestMapping("/wfApprover_update")
    public String wfApproverUpdate(@RequestParam  Integer wfApproverId, Model model) {
        WfApprover wfApprover = wfApproverExtService.queryById(wfApproverId);
        model.addAttribute("item",wfApprover);
        LogObjectHolder.me().set(wfApprover);
        return PREFIX + "wfApprover_edit.html";
    }
    @RequestMapping("/wfApprover_detail")
    public String wfApproverDetail(@RequestParam Integer wfApproverId, Model model) {
        WfApprover wfApprover = wfApproverExtService.queryById(wfApproverId);
        model.addAttribute("item",wfApprover);
        LogObjectHolder.me().set(wfApprover);
        return PREFIX + "wfApprover_detail.html";
    }
    /**
     * 获取审批人列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(WfApproverListParam wfApproverParam) {
        return wfApproverService.findPageBySpec(wfApproverParam);
    }

    /**
     * 新增审批人
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/wfApprover/wfApprover_add")
    @ResponseBody
    public ResponseData add(WfApprover wfApprover) {
         wfApproverExtService.save(wfApprover);
         return ResponseData.success();
    }

    /**
     * 删除审批人
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  wfApproverId) {
        wfApproverExtService.removeById(wfApproverId);
         return ResponseData.success();
    }

    /**
     * 修改审批人
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/wfApprover/wfApprover_update")
    @ResponseBody
    public ResponseData update(WfApprover wfApprover) {
        wfApproverExtService.updateById(wfApprover);
        return ResponseData.success();
    }

    /**
     * 审批人详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  wfApproverId) {
       WfApprover detail = wfApproverExtService.queryById(wfApproverId);
       return ResponseData.success(detail);
    }
}
