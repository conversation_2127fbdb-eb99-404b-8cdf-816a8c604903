package com.th.cms.modular.settle.settleProjects.third;

import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.modular.enums.ApprovalStatus;
import com.th.cms.modular.settle.cushionBatch.model.CushionBatch;
import com.th.cms.modular.settle.settleAccount.model.SettleWithdrawImport;
import com.th.cms.modular.settle.settleBatch.model.SettleBatch;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.sysmesage.util.MessageSendHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MessageProjectHelper {
    @Autowired
    private MessageSendHelper messageSendHelper;

    /**
     * 立项提交||待审核
     * 【立项提交】您的[项目名称]立项申请已提交成功，前往立项管理中查看详情。（提交时间：YYYY-MM-DD HH:mm）<br></div><div>
     * 【待审核】来自[发起人]的立项审核 ，待处理。<br>【审核通过】「发起人」提交的[项目名称]立项申请已审核通过。<br></div><br>
     *
     * @param settleProjects 项目
     * @param user           用户
     */
    public void sendProjectSubmit(SettleProjects settleProjects, ShiroUser user) {
        messageSendHelper.sendMessage("sendProjectSubmit",null, user.getId(),null);
    }

    /**
     * 立项审核
     * 【立项驳回】您的[项目名称]立项申请需要修改，驳回原因：[驳回原因]，前往立项管理中查看详情。<br></div><div>
     * 【立项通过】您的[项目名称]立项申请审核通过，前往立项管理中查看详情。<br><br>审核人通知<br>标题：项目立项审核<br>内容：<br>
     *
     * @param settleProjects 项目
     * @param approvalStatus 审批状态
     * @param user           用户
     * @param jujuRemark     拒决原因
     */
    public void sendProjectApprove(SettleProjects settleProjects, ApprovalStatus approvalStatus, ShiroUser user, String jujuRemark) {
        messageSendHelper.sendMessage("sendProjectSubmit",null, user.getId(),null);

    }

    /**
     * 审核提醒 -结算审核提交
     * 【待审核】来自[发起人]的结算审核 ，待处理<br>
     * 【结算提交】您的[项目名称]的[结算账期]结算申请已提交成功，前往结算管理中查看详情。（提交时间：YYYY-MM-DD HH:mm）<br>
     *
     * @param settleBatch 结算批次
     */
    public void sendFinishSubmit(SettleBatch settleBatch) {
        messageSendHelper.sendMessage("sendFinishSubmit",null, null,null);

    }

    /**
     * 财务审核结算
     * 【结算通过】您的[项目名称]的[结算账期]结算申请已审核通过，前往结算管理中查看详情。<br><br>审核人通知<br>
     * 【审核通过】「发起人」提交的[结算帐期]结算申请已审核通过。<br><br>
     *
     * @param settleBatch1 结算批次
     */
    public void sendFinishCwApprove(SettleBatch settleBatch1) {
        messageSendHelper.sendMessage("sendFinishCwApprove",null, null,null);
    }

    /**
     * 财务审核结算
     * 【结算驳回】您的[项目名称]的[结算账期]结算申请需要修改，驳回原因：[驳回原因]，前往结算管理中查看详情。<br>
     *
     * @param settleBatch1 结算批次
     */
    public void sendFinishCwReject(SettleBatch settleBatch1) {
        messageSendHelper.sendMessage("sendFinishCwReject",null, null,null);

    }

    /**
     * 老板审核结算单
     * 【结算通过】您的[项目名称]的[结算账期]结算申请已审核通过，前往结算管理中查看详情。<br><br>审核人通知<br>
     * 【审核通过】「发起人」提交的[结算帐期]结算申请已审核通过。<br><br>
     *
     * @param settleBatch1 结算批次
     */
    public void sendFinishLbApprove(SettleBatch settleBatch1) {

        messageSendHelper.sendMessage("sendFinishLbApprove",null, null,null);
    }

    /**
     * 老板审核结算单
     * 【结算驳回】您的[项目名称]的[结算账期]结算申请需要修改，驳回原因：[驳回原因]，前往结算管理中查看详情。<br>
     *
     * @param settleBatch1 结算批次
     */
    public void sendFinishLbReject(SettleBatch settleBatch1) {
        messageSendHelper.sendMessage("sendFinishLbReject",null, null,null);

    }

    /**
     * 垫付提交
     * 【垫付提交】您的[项目名称]的[结算账期]垫付申请已提交成功，前往结算管理中查看详情。（提交时间：YYYY-MM-DD HH:mm）<br>
     * 【待审核】来自[发起人]的垫付审核 ，待处理<br>
     *
     * @param cushionBatch 垫付批次
     */
    public void sendCushionSubmit(CushionBatch cushionBatch) {
        messageSendHelper.sendMessage("sendCushionSubmit",null, null,null);

    }

    /**
     * 财务审核垫付单
     * 【审核通过】「发起人」提交的[结算帐期]垫付申请已审核通过。<br><br>
     * 【垫付通过】您的[项目名称]的[结算账期]垫付申请已审核通过，前往结算管理中查看详情。<br><br>审核人通知<br>
     *
     * @param cushionBatch1 垫付批次
     */
    public void cushionCwApprove(CushionBatch cushionBatch1) {
        messageSendHelper.sendMessage("cushionCwApprove",null, null,null);

    }

    /**
     * 财务审核垫付单
     * 【垫付驳回】您的[项目名称]的[结算账期]垫付申请需要修改，驳回原因：[驳回原因]，前往结算管理中查看详情。<br>
     *
     * @param cushionBatch1 垫付批次
     */
    public void cushionCwReject(CushionBatch cushionBatch1) {
        messageSendHelper.sendMessage("cushionCwReject",null, null,null);

    }


    /**
     * 财务审核垫付单
     * 【审核通过】「发起人」提交的[结算帐期]垫付申请已审核通过。<br><br>
     * 【垫付通过】您的[项目名称]的[结算账期]垫付申请已审核通过，前往结算管理中查看详情。<br><br>审核人通知<br>
     *
     * @param cushionBatch1 垫付批次
     */
    public void cushionLbApprove(CushionBatch cushionBatch1) {
        messageSendHelper.sendMessage("cushionLbApprove",null, null,null);

    }

    /**
     * 老板审核垫付单
     * 【垫付驳回】您的[项目名称]的[结算账期]垫付申请需要修改，驳回原因：[驳回原因]，前往结算管理中查看详情。<br>
     *
     * @param cushionBatch1 垫付批次
     */
    public void cushionLbReject(CushionBatch cushionBatch1) {
        messageSendHelper.sendMessage("cushionLbReject",null, null,null);

    }

    /**
     * 导入提现
     * 【提现导入提交】您已成功上传[提现单号]的打款信息文件，待审核。（提交时间：YYYY-MM-DD HH:mm）<br>
     * 【待审核】来自[发起人]的提现导入审核 ，待处理<br></div><div>
     *
     * @param settleWithdrawImport 提现申请
     */

    public void importSettleWithdrawFile(SettleWithdrawImport settleWithdrawImport) {

        messageSendHelper.sendMessage("importSettleWithdrawFile",null, null,null);
    }

    /**
     * 提现驳回
     * 【打款审核驳回】您于（提交时间：YYYY-MM-DD HH:mm）上传的打款信息已经审核驳回，驳回原因：[驳回原因]。<br><br>审核人通知<br>
     * 标题：提现导入审核<br>内容：<br><div>
     *
     * @param withdrawImport 提现申请
     */

    public void settleWithdrawReject(SettleWithdrawImport withdrawImport) {
        messageSendHelper.sendMessage("settleWithdrawReject",null, null,null);

    }

    /**
     * 提现通过
     * 【打款审核成功】您于（提交时间：YYYY-MM-DD HH:mm）上传的打款信息已经审核成功。<br>
     * 【审核通过】「发起人」提交的[提交时间：YYYY-MM-DD HH:mm]提现导入申请已审核通过。</div>
     *
     * @param withdrawImport 提现申请
     */

    public void settleWithdrawApprove(SettleWithdrawImport withdrawImport) {
        messageSendHelper.sendMessage("settleWithdrawApprove",null, null,null);

    }


}
