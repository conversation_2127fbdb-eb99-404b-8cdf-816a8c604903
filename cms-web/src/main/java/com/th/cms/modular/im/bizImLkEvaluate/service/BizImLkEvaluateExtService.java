package com.th.cms.modular.im.bizImLkEvaluate.service;


import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.im.bizImLkEvaluate.model.BizImLkEvaluate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;

@Service
public class BizImLkEvaluateExtService {
    @Autowired
    BizImLkEvaluateService bizImLkEvaluateService;

    public void save(BizImLkEvaluate bizImLkEvaluate) {
        setEnumsName(bizImLkEvaluate);
        ShiroUtils.setAddAuthInfo(bizImLkEvaluate);
        bizImLkEvaluateService.save(bizImLkEvaluate);
    }

    public void updateById(BizImLkEvaluate bizImLkEvaluate) {
        setEnumsName(bizImLkEvaluate);
        DataAuthService.checkPermision(bizImLkEvaluateService.getById(bizImLkEvaluate.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(bizImLkEvaluate);
        BizImLkEvaluate bizImLkEvaluateDb = queryById(bizImLkEvaluate.getId());

        bizImLkEvaluateService.updateById(bizImLkEvaluate);
    }

    public BizImLkEvaluate queryById(Serializable id) {
        BizImLkEvaluate bizImLkEvaluate = bizImLkEvaluateService.getById(id);
        DataAuthService.checkPermision(bizImLkEvaluate, DataPermisionTypeEnum.ByComp);

        return bizImLkEvaluate;
    }

    public void removeById(Serializable id) {
        BizImLkEvaluate bizImLkEvaluate = bizImLkEvaluateService.getById(id);
        DataAuthService.checkPermision(bizImLkEvaluate, DataPermisionTypeEnum.ByComp);
        BizImLkEvaluate bizImLkEvaluateRecord = new BizImLkEvaluate();
        bizImLkEvaluateRecord.setId(bizImLkEvaluate.getId());

        bizImLkEvaluateService.updateById(bizImLkEvaluateRecord);
    }

    private void setEnumsName(BizImLkEvaluate bizImLkEvaluate) {
    }
}
