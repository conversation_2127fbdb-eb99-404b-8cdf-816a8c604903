package com.th.cms.modular.bizOperateLog.service;

import com.th.cms.core.common.enums.DataPermisionTypeEnum;
import com.th.cms.modular.bizOperateLog.entity.BizOperateLog;
import com.th.cms.core.shiro.DataAuthService;
import com.th.cms.core.util.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;

/**
 * <p>
 * 操作日志 扩展服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class BizOperateLogExtService {

    @Autowired
    private BizOperateLogService bizOperateLogService;

    /**
     * 新增操作日志
     *
     * @param bizOperateLog 操作日志信息
     */
    public void save(BizOperateLog bizOperateLog) {
        setEnumsName(bizOperateLog);
        ShiroUtils.setAddAuthInfo(bizOperateLog);
        bizOperateLogService.save(bizOperateLog);
    }

    /**
     * 根据id更新操作日志
     *
     * @param bizOperateLog 操作日志信息
     */
    public void updateById(BizOperateLog bizOperateLog) {
        setEnumsName(bizOperateLog);
        DataAuthService.checkPermision(bizOperateLogService.getById(bizOperateLog.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(bizOperateLog);
        BizOperateLog bizOperateLogDb = queryById(bizOperateLog.getId());

        bizOperateLogService.updateById(bizOperateLog);
    }

    /**
     * 根据id查询操作日志
     *
     * @param id 主键id
     * @return 操作日志信息
     */
    public BizOperateLog queryById(Serializable id) {
        BizOperateLog bizOperateLog = bizOperateLogService.getById(id);
        DataAuthService.checkPermision(bizOperateLog, DataPermisionTypeEnum.ByComp);
        return bizOperateLog;
    }

    /**
     * 根据id删除操作日志
     *
     * @param id 主键id
     */
    public void removeById(Serializable id) {
        BizOperateLog bizOperateLog = bizOperateLogService.getById(id);
        DataAuthService.checkPermision(bizOperateLog, DataPermisionTypeEnum.ByComp);
        bizOperateLogService.removeById(id);
    }

    /**
     * 设置枚举名称
     *
     * @param bizOperateLog 操作日志信息
     */
    private void setEnumsName(BizOperateLog bizOperateLog) {
        // 设置操作类型名称
        if (bizOperateLog.getOperationType() != null) {
            switch (bizOperateLog.getOperationType()) {
                case 1:
                    bizOperateLog.setOperationTypeName("新增");
                    break;
                case 2:
                    bizOperateLog.setOperationTypeName("修改");
                    break;
                case 3:
                    bizOperateLog.setOperationTypeName("删除");
                    break;
                case 4:
                    bizOperateLog.setOperationTypeName("查询");
                    break;
                case 5:
                    bizOperateLog.setOperationTypeName("导出");
                    break;
                case 6:
                    bizOperateLog.setOperationTypeName("导入");
                    break;
                default:
                    bizOperateLog.setOperationTypeName("其他");
                    break;
            }
        }
    }
}
