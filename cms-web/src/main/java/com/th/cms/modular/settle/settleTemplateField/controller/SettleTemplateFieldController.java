package com.th.cms.modular.settle.settleTemplateField.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.settle.settleTemplateField.model.SettleTemplateField;
import com.th.cms.modular.settle.settleTemplateField.service.SettleTemplateFieldService;
import com.th.cms.modular.settle.settleTemplateField.service.SettleTemplateFieldExtService;
import com.th.cms.modular.settle.settleTemplateField.model.reqparam.SettleTemplateFieldListParam;

/**
 * 模版字段控制器
 *
 * <AUTHOR>
 * @Date 2025-04-11 09:41:58
 */
@Controller
@RequestMapping("/settleTemplateField")
public class SettleTemplateFieldController extends BaseController {

    private String PREFIX = "/modular/settleTemplateField/settleTemplateField/";
    @Autowired
    SettleTemplateFieldExtService settleTemplateFieldExtService;
    @Autowired
    private SettleTemplateFieldService settleTemplateFieldService;

    /**
     * 跳转到模版字段首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "settleTemplateField.html";
    }

    /**
     * 跳转到添加模版字段
     */
    @Permission
    @RequestMapping("/settleTemplateField_add")
    public String settleTemplateFieldAdd() {
        return PREFIX + "settleTemplateField_add.html";
    }

    /**
     * 跳转到修改模版字段
     */
    @Permission
    @RequestMapping("/settleTemplateField_update")
    public String settleTemplateFieldUpdate(@RequestParam  Integer settleTemplateFieldId, Model model) {
        SettleTemplateField settleTemplateField = settleTemplateFieldExtService.queryById(settleTemplateFieldId);
        model.addAttribute("item",settleTemplateField);
        LogObjectHolder.me().set(settleTemplateField);
        return PREFIX + "settleTemplateField_edit.html";
    }
    @RequestMapping("/settleTemplateField_detail")
    public String settleTemplateFieldDetail(@RequestParam Integer settleTemplateFieldId, Model model) {
        SettleTemplateField settleTemplateField = settleTemplateFieldExtService.queryById(settleTemplateFieldId);
        model.addAttribute("item",settleTemplateField);
        LogObjectHolder.me().set(settleTemplateField);
        return PREFIX + "settleTemplateField_detail.html";
    }
    /**
     * 获取模版字段列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(SettleTemplateFieldListParam settleTemplateFieldParam) {
        return settleTemplateFieldService.findPageBySpec(settleTemplateFieldParam);
    }

    /**
     * 新增模版字段
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/settleTemplateField/settleTemplateField_add")
    @ResponseBody
    public ResponseData add(SettleTemplateField settleTemplateField) {
         settleTemplateFieldExtService.save(settleTemplateField);
         return ResponseData.success();
    }

    /**
     * 删除模版字段
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  settleTemplateFieldId) {
        settleTemplateFieldExtService.removeById(settleTemplateFieldId);
         return ResponseData.success();
    }

    /**
     * 修改模版字段
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/settleTemplateField/settleTemplateField_update")
    @ResponseBody
    public ResponseData update(SettleTemplateField settleTemplateField) {
        settleTemplateFieldExtService.updateById(settleTemplateField);
        return ResponseData.success();
    }

    /**
     * 模版字段详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  settleTemplateFieldId) {
       SettleTemplateField detail = settleTemplateFieldExtService.queryById(settleTemplateFieldId);
       return ResponseData.success(detail);
    }
}
