package com.th.cms.modular.policy.bizAppSlideshow.model.enums;

public enum DELFLAG {
    /** 1: 删除 */
    shanchu("-1","删除"),
    /** 1: 未删除 */
    weishanchu("0","未删除"),
    ;

    public String value;
    public String name;

    DELFLAG(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static DELFLAG getType(String value) {
    DELFLAG[] DELFLAGList = DELFLAG.values();
        for (DELFLAG DELFLAG : DELFLAGList) {
            if (DELFLAG.value.equals(value)) {
                return DELFLAG;
            }
        }
       return  null;
    }
}
