package com.th.cms.modular.influcer.cooperationPlatform.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 合作平台赛道信息DTO
 *
 * <AUTHOR>
 */

@Data
public class CooperationPlatformTrackInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;
    
    @ApiModelProperty(value = "平台ID")
    private Long platformId;
    
    @ApiModelProperty(value = "标题")
    private String title;
    
    @ApiModelProperty(value = "内容")
    private String content;
    /**
     * 排序
     */
    private Integer sort;
}
