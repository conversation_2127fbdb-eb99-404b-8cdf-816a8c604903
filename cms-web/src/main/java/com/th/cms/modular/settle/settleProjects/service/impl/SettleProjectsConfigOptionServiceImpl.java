package com.th.cms.modular.settle.settleProjects.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjectsConfigOption;
import com.th.cms.modular.settle.settleProjects.mapper.SettleProjectsConfigOptionMapper;
import com.th.cms.modular.settle.settleProjects.service.ISettleProjectsConfigOptionService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 项目字段配置表选项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
@Primary
public class SettleProjectsConfigOptionServiceImpl extends ServiceImpl<SettleProjectsConfigOptionMapper, SettleProjectsConfigOption> implements ISettleProjectsConfigOptionService {

}
