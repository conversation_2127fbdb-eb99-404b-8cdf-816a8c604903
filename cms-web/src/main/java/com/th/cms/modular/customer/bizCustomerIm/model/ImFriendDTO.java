package com.th.cms.modular.customer.bizCustomerIm.model;

import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImFriendDTO implements Serializable {


    @ApiModelProperty(value = "申请的网易云信账号 ID")
    private String account_id;

    @ApiModelProperty(value = "好友的网易云信账号 ID")
    private String friend_account_id;

    private String alias;
    private String extension;
    private String customer_extension;
    private String postscript;


}
