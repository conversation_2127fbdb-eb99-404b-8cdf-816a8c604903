package com.th.cms.modular.workorder.bizWorktype.service;

import com.th.cms.modular.workorder.bizWorktype.model.BizWorktype;
import com.th.cms.modular.workorder.bizWorktype.dao.BizWorktypeMapper;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.workorder.bizWorktype.model.reqparam.BizWorktypeListParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Service;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizWorktypeService extends ServiceImpl<BizWorktypeMapper, BizWorktype> implements IService<BizWorktype> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizWorktypeListParam param) {
        QueryWrapper<BizWorktype> objectQueryWrapper = new QueryWrapper<>();
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);
        return layuiPageInfo;

    }
}
