package com.th.cms.modular.settle.settleAccount.model.excelTemplate;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;

public class WithdrawStatusConverter implements Converter<Integer> {
    @Override
    public Integer convertToJavaData(ReadConverterContext<?> context) throws Exception {
        ExcelContentProperty contentProperty = context.getContentProperty();
        String name = contentProperty.getField().getName();
        if (StringUtils.equals(name, "status")){
            String stringValue = context.getReadCellData().getStringValue();
            switch (stringValue) {
                case "提现申请中": return 1;
                case "提现成功": return 2;
                case "提现失败": return -1;
                default: return null;
            }
        }
        return Converter.super.convertToJavaData(context);
    }
}