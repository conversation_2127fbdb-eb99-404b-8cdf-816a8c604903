package com.th.cms.modular.settle.settleOrder.controller;

import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.modular.enums.ApprovalBillType;
import com.th.cms.modular.settle.settleOrder.model.SettleOrderProject;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.wf.wfApprovalRequest.model.WfRequestRsp;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestExtService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.modular.settle.settleOrder.model.SettleOrder;
import com.th.cms.modular.settle.settleOrder.service.SettleOrderService;
import com.th.cms.modular.settle.settleOrder.service.SettleOrderExtService;
import com.th.cms.modular.settle.settleOrder.model.reqparam.SettleOrderListParam;

/**
 * 结算单控制器
 *
 * <AUTHOR>
 * @Date 2025-04-02 17:49:18
 */
@Controller
@RequestMapping("/settleOrder")
public class SettleOrderController extends BaseController {

    private String PREFIX = "/modular/settleOrder/settleOrder/";
    @Autowired
    SettleOrderExtService settleOrderExtService;
    @Autowired
    private SettleOrderService settleOrderService;

    /**
     * 跳转到结算单首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "settleOrder.html";
    }
    @Autowired
    WfApprovalRequestExtService wfApprovalRequestExtService;
    /**
     * 跳转到添加结算单
     */
    @RequestMapping("/settleOrder_add")
    public String settleOrderAdd(Integer settleOrderId,Model model) {
        WfRequestRsp<SettleProjects> wfRequestRsp = wfApprovalRequestExtService.queryReqAuditInfos(null, ApprovalBillType.SettleOrder, ShiroKit.getUser().getId()+"");
        model.addAttribute("wfRequestRsp", wfRequestRsp);
        if(settleOrderId!=null && settleOrderId>0){
            model.addAttribute("item", settleOrderService.getById(settleOrderId));
        }else {
            model.addAttribute("item", new SettleOrder());

        }
        model.addAttribute("authuser", ShiroKit.getUser());

        return PREFIX + "settleOrder_add.html";
    }

    /**
     * 跳转到修改结算单
     */
    @RequestMapping("/settleOrder_update")
    public String settleOrderUpdate(@RequestParam Integer settleOrderId, Model model) {
        SettleOrder settleOrder = settleOrderExtService.queryById(settleOrderId);
        model.addAttribute("item", settleOrder);
        LogObjectHolder.me().set(settleOrder);
        return PREFIX + "settleOrder_edit.html";
    }

    @RequestMapping("/settleOrder_detail")
    public String settleOrderDetail(@RequestParam Integer settleOrderId, Model model) {
        SettleOrderProject detail = settleOrderExtService.queryOrderProjecst(settleOrderId);
        model.addAttribute("item", detail);
        LogObjectHolder.me().set(detail);
        return PREFIX + "settleOrder_detail.html";
    }

    /**
     * 获取结算单列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(SettleOrderListParam settleOrderParam) {
        return settleOrderService.findPageBySpec(settleOrderParam);
    }

    /**
     * 新增结算单
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/settleOrder/settleOrder_add")
    @ResponseBody
    public ResponseData add(SettleOrder settleOrder) {
        settleOrderExtService.save(settleOrder);
        return ResponseData.success();
    }

    /**
     * 删除结算单
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer settleOrderId) {
        settleOrderExtService.removeById(settleOrderId);
        return ResponseData.success();
    }

    /**
     * 修改结算单
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/settleOrder/settleOrder_update")
    @ResponseBody
    public ResponseData update(SettleOrder settleOrder) {
        settleOrderExtService.updateById(settleOrder);
        return ResponseData.success();
    }

    /**
     * 结算单详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer settleOrderId) {
        SettleOrderProject detail = settleOrderExtService.queryOrderProjecst(settleOrderId);
        return ResponseData.success(detail);
    }
}
