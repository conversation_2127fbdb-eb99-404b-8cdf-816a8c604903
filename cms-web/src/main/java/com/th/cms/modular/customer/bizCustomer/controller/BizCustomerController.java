package com.th.cms.modular.customer.bizCustomer.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.annotion.Permission;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.modular.customer.bizCustomer.model.BizCustomer;
import com.th.cms.modular.customer.bizCustomer.model.reqparam.BizCustomerListParam;
import com.th.cms.modular.customer.bizCustomer.service.BizCustomerExtService;
import com.th.cms.modular.customer.bizCustomer.service.BizCustomerService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 客服设置控制器
 *
 * <AUTHOR>
 * @Date 2025-03-12 20:55:14
 */
@Controller
@RequestMapping("/bizCustomer")
public class BizCustomerController extends BaseController {

    private String PREFIX = "/modular/bizCustomer/bizCustomer/";
    @Autowired
    BizCustomerExtService bizCustomerExtService;
    @Autowired
    private BizCustomerService bizCustomerService;

    /**
     * 跳转到客服设置首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizCustomer.html";
    }

    /**
     * 跳转到添加客服设置
     */
    @Permission
    @RequestMapping("/bizCustomer_add")
    public String bizCustomerAdd() {
        return "/modular/bizCustomer/user/user.html";
    }

    /**
     * 跳转到修改客服设置
     */
    @Permission
    @RequestMapping("/bizCustomer_update")
    public String bizCustomerUpdate(@RequestParam Integer bizCustomerId, Model model) {
        BizCustomer bizCustomer = bizCustomerExtService.queryById(bizCustomerId);
        model.addAttribute("item", bizCustomer);
        LogObjectHolder.me().set(bizCustomer);
        return PREFIX + "bizCustomer_edit.html";
    }

    @RequestMapping("/bizCustomer_detail")
    public String bizCustomerDetail(@RequestParam Integer bizCustomerId, Model model) {
        BizCustomer bizCustomer = bizCustomerExtService.queryById(bizCustomerId);
        model.addAttribute("item", bizCustomer);
        LogObjectHolder.me().set(bizCustomer);
        return PREFIX + "bizCustomer_detail.html";
    }


//    bizCustomer_editAddPlat

    @RequestMapping("/bizCustomer_editAddPlat")
    public String bizCustomer_editAddPlat(@RequestParam Integer bizCustomerId, Model model) {
        BizCustomer bizCustomer = bizCustomerExtService.queryById(bizCustomerId);
        model.addAttribute("item", bizCustomer);
        LogObjectHolder.me().set(bizCustomer);
        return PREFIX + "bizCustomerAddPlat.html";
    }

    /**
     * 获取客服设置列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(BizCustomerListParam bizCustomerParam) {
        return bizCustomerService.findPageBySpec(bizCustomerParam);
    }

    @RequestMapping(value = "/customerList")
    @ResponseBody
    public ResponseData customerList(BizCustomer bizCustomer) {
        return ResponseData.success(bizCustomerService.customerList(bizCustomer));
    }

    /**
     * 新增客服设置
     */
    @RequestMapping(value = "/add")
//    @RequiresPermissions("/bizCustomer/bizCustomer_add")
    @ResponseBody
    public ResponseData add(String userId) {
        String[] uids = userId.split(",");

        for (String uid : uids) {
            bizCustomerExtService.save(uid);
        }

        return ResponseData.success();
    }

    /**
     * 删除客服设置
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer bizCustomerId) {
        bizCustomerExtService.removeById(bizCustomerId);
        return ResponseData.success();
    }

    /**
     * 修改客服设置
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizCustomer/bizCustomer_update")
    @ResponseBody
    public ResponseData update(BizCustomer bizCustomer) {
        bizCustomerExtService.updateById(bizCustomer);
        return ResponseData.success();
    }

    @RequestMapping(value = "/updSort")
    @RequiresPermissions("/bizCustomer/bizCustomer_update")
    @ResponseBody
    public ResponseData updSort(BizCustomer bizCustomer) {
        bizCustomerExtService.updSort(bizCustomer);
        return ResponseData.success();
    }

    /**
     * 客服设置详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer bizCustomerId) {
        BizCustomer detail = bizCustomerExtService.queryById(bizCustomerId);
        return ResponseData.success(detail);
    }
}
