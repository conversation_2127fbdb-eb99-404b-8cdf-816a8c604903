package com.th.cms.modular.influcer.influcerDevices.model.enums;

public enum JpushReasonEnums {

    REASON_1000("1000", "系统内部错误"),
    REASON_1001("1001", "只支持 HTTP Post 方法"),
    REASON_1002("1002", "缺少了必须的参数"),
    REASON_1003("1003", "参数值不合法"),
    REASON_1004("1004", "验证失败"),
    REASON_1005("1005", "消息体太大"),
    REASON_1008("1008", "app_key 参数非法"),
    REASON_1009("1009", "推送对象中有不支持的 key"),
    REASON_1011("1011", "没有满足条件的推送目标"),
    REASON_1012("1012", "符合当前条件的推送已超过限制"),
    REASON_1020("1020", "只支持 HTTPS 请求"),
    REASON_1030("1030", "内部服务超时"),
    REASON_2002("2002", "API 调用频率超出该应用的限制"),
    REASON_2003("2003", "该应用 appkey 已被限制调用 API"),
    REASON_2004("2004", "无权限执行当前操作"),
    REASON_2005("2005", "信息发送量超出合理范围"),
    REASON_2006("2006", "非 VIP 用户"),
    REASON_2007("2007", "无权调用此接口"),
    REASON_2008("2008", "广播推送超出频率限制"),
    REASON_2009("2009", "推送请求被限制"),
    REASON_2010("2010", "推送请求被限制"),
    REASON_2011("2011", "推送时间窗口限制"),
    ;

    public String value;
    public String reason;

    JpushReasonEnums(String value, String reason) {
        this.value = value;
        this.reason = reason;
    }

    public static String getErrorReason(String value) {
        JpushReasonEnums[] jpushReasonEnums = JpushReasonEnums.values();
        for (JpushReasonEnums enums : jpushReasonEnums) {
            if (enums.value.equals(value)) {
                return enums.reason;
            }
        }
        return value + ":参数非法";
    }


}

