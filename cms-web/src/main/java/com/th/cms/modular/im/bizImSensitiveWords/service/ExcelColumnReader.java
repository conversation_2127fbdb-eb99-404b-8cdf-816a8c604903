package com.th.cms.modular.im.bizImSensitiveWords.service;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel 列读取工具类
 */
public class ExcelColumnReader {

    /**
     * 读取指定列的字符串数据（自动判断文件格式）
     * @param filePath      文件路径
     * @param sheetIndex    工作表索引（从0开始）
     * @param columnIndex   列索引（从0开始）
     * @return 包含列数据的List
     * @throws IOException  文件读取异常
     */
    public static List<String> readColumn(MultipartFile filePath, int sheetIndex, int columnIndex) throws IOException {
        try (InputStream is = filePath.getInputStream()) {
            Workbook workbook = createWorkbook(filePath, is);
            return processSheet(workbook.getSheetAt(sheetIndex), columnIndex);
        }
    }

    private static Workbook createWorkbook(MultipartFile filePath, InputStream is) throws IOException {
        if (filePath.getName().endsWith(".xls")) {
            return new HSSFWorkbook(is);
        } else if (filePath.getName().endsWith(".xlsx")) {
            return new XSSFWorkbook(is);
        }
        throw new IllegalArgumentException("不支持的文件格式: " + filePath);
    }

    private static List<String> processSheet(Sheet sheet, int columnIndex) {
        List<String> result = new ArrayList<>();
        DataFormatter formatter = new DataFormatter();

        for (Row row : sheet) {
            Cell cell = row.getCell(columnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            result.add(formatCellValue(cell, formatter));
        }

        return result;
    }

    private static String formatCellValue(Cell cell, DataFormatter formatter) {
        if (cell.getCellType() == CellType.FORMULA) {
            return handleFormulaCell(cell, formatter);
        }
        return formatter.formatCellValue(cell).trim();
    }

    private static String handleFormulaCell(Cell cell, DataFormatter formatter) {
        try {
            return formatter.formatCellValue(cell, cell.getSheet().getWorkbook().getCreationHelper()
                    .createFormulaEvaluator());
        } catch (Exception e) {
            return "公式计算错误";
        }
    }
}