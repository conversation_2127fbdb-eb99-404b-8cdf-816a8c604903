package com.th.cms.modular.policy.bizAppPolicyContent.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BizAppPolicyContent对象", description = "")
public class BizAppPolicyContent implements Serializable {

    @ApiModelProperty(value = "编号")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("policy_id")
    private Long policyId;
    @TableField("policy_name")
    private String policyName;

    @TableField("platform_id")
    private Long platformId;
    @TableField("paltform_name")
    private String platformName;

    @ApiModelProperty(value = "标题")
    @TableField("title")
    private String title;

    @ApiModelProperty(value = "图片")
    @TableField("img_pic")
    private String imgPic;

    @ApiModelProperty(value = "状态")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "排序字段")
    @TableField("sort_num")
    private Integer sortNum;

    @ApiModelProperty(value = "热点值")
    @TableField("hot_num")
    private Integer hotNum;

    @ApiModelProperty(value = "跳转链接")
    @TableField("like_url")
    private String likeUrl;

    @ApiModelProperty(value = "内容")
    @TableField("content")
    private String content;

    @ApiModelProperty(value = "添加人")
    @TableField("create_id")
    private Long createId;

    @ApiModelProperty(value = "添加人")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "部门名称")
    @TableField("dept_name")
    private String deptName;

    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private Long deptId;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty(value = "公司名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "删除")
    @TableField("del_flag")
    private Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;


}
