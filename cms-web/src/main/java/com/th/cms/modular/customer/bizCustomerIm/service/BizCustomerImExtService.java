package com.th.cms.modular.customer.bizCustomerIm.service;

import com.th.cms.modular.customer.bizCustomerIm.model.BizCustomerIm;

import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.Serializable;
import java.util.List;

@Service
public class BizCustomerImExtService {
    @Autowired
    BizCustomerImService bizCustomerImService;

    public void save(BizCustomerIm bizCustomerIm){
        setEnumsName(bizCustomerIm);
        ShiroUtils.setAddAuthInfo(bizCustomerIm);
        bizCustomerImService.save(bizCustomerIm);
    }

    public void updateById(BizCustomerIm bizCustomerIm){
        setEnumsName(bizCustomerIm);
        DataAuthService.checkPermision(bizCustomerImService.getById(bizCustomerIm.getId()),DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(bizCustomerIm);
        BizCustomerIm bizCustomerImDb = queryById(bizCustomerIm.getId());

        bizCustomerImService.updateById(bizCustomerIm);
    }
    public BizCustomerIm queryById(Serializable id){
        BizCustomerIm bizCustomerIm = bizCustomerImService.getById(id);
        DataAuthService.checkPermision(bizCustomerIm,DataPermisionTypeEnum.ByComp);

        return   bizCustomerIm;
    }
    public void removeById(Serializable id){
       BizCustomerIm bizCustomerIm = bizCustomerImService.getById(id);
       DataAuthService.checkPermision(bizCustomerIm,DataPermisionTypeEnum.ByComp);
       BizCustomerIm bizCustomerImRecord = new BizCustomerIm();
       bizCustomerImRecord.setId(bizCustomerIm.getId());

       bizCustomerImService.updateById( bizCustomerImRecord);
    }
    private void setEnumsName(BizCustomerIm bizCustomerIm){
    }

    public BizCustomerIm queryByCustomerId(Integer customerId){
        List<BizCustomerIm> bizCustomerImList = bizCustomerImService.lambdaQuery().eq(BizCustomerIm::getCustomerId,customerId).list();
        if(bizCustomerImList.size()>0){
            return bizCustomerImList.get(0);
        }
        return null;
    }

    public BizCustomerIm queryByUserId(Long userId){
        List<BizCustomerIm> bizCustomerImList = bizCustomerImService.lambdaQuery().eq(BizCustomerIm::getUserId,userId).list();
        if(bizCustomerImList.size()>0){
            return bizCustomerImList.get(0);
        }
        return null;
    }


}
