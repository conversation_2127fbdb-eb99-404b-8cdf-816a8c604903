package com.th.cms.modular.system.service;

import cn.stylefeng.roses.core.util.ToolUtil;
import cn.stylefeng.roses.kernel.model.exception.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.exception.BizExceptionEnum;
import com.th.cms.core.common.node.TreeviewNode;
import com.th.cms.core.common.node.ZTreeNode;
import com.th.cms.core.common.page.LayuiPageFactory;
import com.th.cms.modular.system.entity.Dept;
import com.th.cms.modular.system.mapper.DeptMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-07
 */
@Service
public class DeptService extends ServiceImpl<DeptMapper, Dept> {

    @Resource
    private DeptMapper deptMapper;

    /**
     * 新增部门
     *
     * <AUTHOR>
     * @Date 2018/12/23 5:00 PM
     */
    @Transactional(rollbackFor = Exception.class)
    public void addDept(Dept dept) {

        if (ToolUtil.isOneEmpty(dept, dept.getSimpleName(), dept.getFullName(), dept.getPid(), dept.getDescription())) {
            throw new ServiceException(BizExceptionEnum.REQUEST_NULL);
        }

        //完善pids,根据pid拿到pid的pids
        this.deptSetPids(dept);

        this.save(dept);
    }

    /**
     * 修改部门
     *
     * <AUTHOR>
     * @Date 2018/12/23 5:00 PM
     */
    @Transactional(rollbackFor = Exception.class)
    public void editDept(Dept dept) {

        if (ToolUtil.isOneEmpty(dept, dept.getDeptId(), dept.getSimpleName(), dept.getFullName(), dept.getPid(), dept.getDescription())) {
            throw new ServiceException(BizExceptionEnum.REQUEST_NULL);
        }

        //完善pids,根据pid拿到pid的pids
        this.deptSetPids(dept);

        this.updateById(dept);
    }

    /**
     * 删除部门
     *
     * <AUTHOR>
     * @Date 2018/12/23 5:16 PM
     */
    @Transactional
    public void deleteDept(Long deptId) {
        Dept dept = deptMapper.selectById(deptId);

        //根据like查询删除所有级联的部门
        List<Dept> subDepts = deptMapper.likePids(dept.getDeptId());

        for (Dept temp : subDepts) {
            this.removeById(temp.getDeptId());
        }

        this.removeById(dept.getDeptId());
    }

    /**
     * 获取ztree的节点列表
     *
     * <AUTHOR>
     * @Date 2018/12/23 5:16 PM
     */
    public List<ZTreeNode> tree(String deptType, Long deptId) {
        return this.baseMapper.tree(deptType, deptId);
    }

    /**
     * 获取ztree的节点列表
     *
     * <AUTHOR>
     * @Date 2018/12/23 5:16 PM
     */
    public List<TreeviewNode> treeviewNodes() {
        return this.baseMapper.treeviewNodes();
    }

    /**
     * 获取所有部门列表
     *
     * <AUTHOR>
     * @Date 2018/12/23 5:16 PM
     */
    public Page<Map<String, Object>> list(String condition, Long deptId, String deptType) {
        Page page = LayuiPageFactory.defaultPage();
        return this.baseMapper.list(page, condition, deptId, deptType);
    }

    /**
     * 设置部门的父级ids
     *
     * <AUTHOR>
     * @Date 2018/12/23 4:58 PM
     */
    private void deptSetPids(Dept dept) {
        if (ToolUtil.isEmpty(dept.getPid()) || dept.getPid().equals(0L)) {
            dept.setPid(0L);
            dept.setPids("[0],");
        } else {
            Long pid = dept.getPid();
            Dept temp = this.getById(pid);
            String pids = temp.getPids();
            dept.setPid(pid);
            dept.setPids(pids + "[" + pid + "],");
        }
    }

    public static List<Long> getPids(Dept dept) {
        String pids = dept.getPids();
        pids = pids.replaceAll("\\[", "").replaceAll("]", "");
        String[] pidsStrs = pids.split(",");
        List<Long> dlist = new ArrayList<>();
        for (String pid : pidsStrs) {
            if (StringUtils.isNotBlank(pid)) {
                dlist.add(Long.parseLong(pid));
            }
        }
        dlist.add(dept.getDeptId());
        return dlist;
    }

    public List<Long> getAllChildsIds(long deptId) {
        QueryWrapper<Dept> objectQueryWrapper = new QueryWrapper();
        objectQueryWrapper.lambda().like(Dept::getPids, "%[" + deptId + "]%");
        List<Long> dlist = list(objectQueryWrapper).stream().map(Dept::getDeptId).collect(Collectors.toList());
        dlist.add(deptId);
        return dlist;
    }

    /**
     * @param dept
     * @param deptNameList 由父->子
     */
    public void getBusinessInfo(Dept dept, List<String> deptNameList) {

        if (dept.getPid() != 0) {
            Dept parDept = getById(dept.getPid());
            getBusinessInfo(parDept, deptNameList);
        }

        deptNameList.add(dept.getFullName());
    }


}
