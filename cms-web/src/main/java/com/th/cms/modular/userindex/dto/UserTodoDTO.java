package com.th.cms.modular.userindex.dto;

import com.th.cms.core.easyexcel.PageResult;
import lombok.Data;

import java.util.Date;

@Data
public class UserTodoDTO {
    /**
     * 分页结果
     */
    private PageResult<UserTodoDTO.DataDTO> pageResult;

    @Data

    public static class DataDTO {
        /**
         * 消息id
         */
        private Long id;
        /**
         * 发起人
         */
        private String name;
        /**
         * 部门名称
         */
        private String deptName;
        /**
         * 创建时间
         */
        private Date createTime;
        /**
         * 消息类型
         */
        private String msgType;
        /**
         * 紧急程度
         */
        private String emergency;
        /**
         * 标题
         **/
        private String title;
        /**
         * 内容
         */
        private String content;
    }

}
