package com.th.cms.modular.im.bizImAutoReply.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.th.cms.core.common.annotion.Permission;
import com.th.cms.modular.im.bizImAutoReply.model.BizImAutoReply;
import com.th.cms.modular.im.bizImAutoReply.model.enums.ReplyTypeEnum;
import com.th.cms.modular.im.bizImAutoReply.service.BizImAutoReplyExtService;
import com.th.cms.modular.im.bizImAutoReply.service.BizImAutoReplyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * bizImAutoReply控制器
 *
 * <AUTHOR>
 * @Date 2025-04-02 10:33:22
 */
@Controller
@RequestMapping("/bizImGuideReply")
public class BizImGuideController extends BaseController {

    private String PREFIX = "/modular/bizImGuideReply/";
    @Resource
    BizImAutoReplyExtService bizImAutoReplyExtService;
    @Resource
    private BizImAutoReplyService bizImAutoReplyService;


    @Permission
    @RequestMapping("")
    public String bizImAutoReplyUpdate() {
        return PREFIX + "bizImGuideReply_edit.html";
    }

    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail() {

        QueryWrapper<BizImAutoReply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("reply_type", ReplyTypeEnum.GUIDE.value);
        List<BizImAutoReply> guideReply = bizImAutoReplyService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(guideReply)) {
            return ResponseData.success(guideReply.get(0));
        }

        return ResponseData.success(null);
    }

    @RequestMapping(value = "/update")
    @ResponseBody
    public ResponseData update(BizImAutoReply bizImAutoReply) {

        if (bizImAutoReply.getId() == null) {
            //新增

            bizImAutoReply.setReplyType(ReplyTypeEnum.GUIDE.value);
            bizImAutoReply.setCreateTime(new Date());
            bizImAutoReply.setUpdateTime(new Date());
            bizImAutoReplyExtService.save(bizImAutoReply);
        } else {

            //更新
            if (StringUtils.isNotBlank(bizImAutoReply.getReplyContent())) {
                bizImAutoReply.setReplyType(ReplyTypeEnum.GUIDE.value);
                bizImAutoReply.setUpdateTime(new Date());
                bizImAutoReplyExtService.updateById(bizImAutoReply);
            } else {
                //内容为空，删除原数据
                QueryWrapper<BizImAutoReply> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("reply_type", ReplyTypeEnum.GUIDE.value);
                bizImAutoReplyService.remove(queryWrapper);
            }
        }
        return ResponseData.success();
    }

    @RequestMapping(value = "/delete")
    @ResponseBody
    public ResponseData delete() {

        //内容为空，删除原数据
        QueryWrapper<BizImAutoReply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("reply_type", ReplyTypeEnum.GUIDE.value);
        bizImAutoReplyService.remove(queryWrapper);

        return ResponseData.success();
    }

}
