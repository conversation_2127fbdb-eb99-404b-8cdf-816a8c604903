package com.th.cms.modular.userindex.dto;

import lombok.Data;

import java.util.List;

@Data
public class CompletePerPersonDTO {
    /**
     * 组名名称
     */
    private String groupName;

    /**
     * 提审审核人均情况
     */
    private CompletePerPersonDTO.DataDTO dataDTO;

    @Data
    public static class DataDTO {
        /**
         * 日期
         */

        private List<String> dateList;
        /**
         * 组
         */
        private List<CompletePerPersonDTO.GroupDTO> groups;

    }

    @Data
    public static class GroupDTO {
        /**
         * 组名
         */
        private String name;
        /**
         * 数字列表
         */
        private List<Long> count;

    }
}
