package com.th.cms.modular.settle.settleBatch.plat.pdd;

import com.th.cms.modular.settle.settleOrder.model.ColumnInfo;
import com.th.cms.modular.settle.settleTemplateExcel.model.reqparam.ExcelColumns;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class PddExcelParser {

    public static List<ExcelColumns> parseExcelHeader(File file, String jisuanFile) throws Exception {
        List<ExcelColumns> list = new ArrayList<>();
        try (InputStream is = new FileInputStream(file)) {

            Workbook workbook = null;
            if (jisuanFile.endsWith(".xls")) {
                workbook = new HSSFWorkbook(is);
            } else if (jisuanFile.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(file);
            }
            try {
                Sheet sheet = workbook.getSheetAt(0);
                Row headerRow = sheet.getRow(0);
                if (headerRow == null) {
                    throw new IllegalArgumentException("Excel文件首行为空");
                }

                Iterator<Cell> cellIterator = headerRow.cellIterator();
                while (cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    String name = getCellStringValue(headerRow, cell.getColumnIndex());
                    ExcelColumns excelColumns = new ExcelColumns();
                    excelColumns.setOriginalCode(cell.getColumnIndex() + "");
                    excelColumns.setOriginalName(name);
                    list.add(excelColumns);
                }
            } finally {
                if (workbook != null) {
                    workbook.close();
                }
            }
        }
        return list;
    }

    public static List<ColumnInfo> getHeaders(File file) throws Exception {
        List<ColumnInfo> list = new ArrayList<>();
        Workbook workbook = null;
        InputStream inputStream = null;

        try {
            inputStream = new FileInputStream(file);
            if (file.getName().endsWith(".xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else if (file.getName().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inputStream);
            }

            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);

            for (Cell cell : headerRow) {
                if (cell != null) {
                    int indx = cell.getColumnIndex();
                    String cellVal = getCellStringValue(headerRow, indx);
                    ColumnInfo columnInfo = new ColumnInfo(indx, cellVal);
                    list.add(columnInfo);
                }
            }
            return list;
        } finally {
            if (workbook != null) {
                workbook.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    public static List<CommonSettleFiledModel> parseExcel(InputStream inputStream, String jisuanFile, String darenId, String darenName, String jine) throws Exception {
        List<CommonSettleFiledModel> list = new ArrayList<>();
        Workbook workbook = null;

        if (jisuanFile.endsWith(".xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (jisuanFile.endsWith(".xlsx")) {
            workbook = new XSSFWorkbook(inputStream);
        }

        try {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel文件首行为空");
            }

            int roomIDIndex = getIndexByHeader(headerRow, darenId);
            int nicknameIndex = getIndexByHeader(headerRow, darenName);
            int rebateAmountIndex = getIndexByHeader(headerRow, jine);

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                CommonSettleFiledModel obj = new CommonSettleFiledModel();
                obj.setInfluencerId(getCellStringValue(row, roomIDIndex));
                obj.setInfluencerNickname(getCellStringValue(row, nicknameIndex));
                obj.setRebateAmount(getCellNumericValue(row, rebateAmountIndex));
                list.add(obj);
            }
        } finally {
            if (workbook != null) {
                workbook.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
        return list;
    }

    private static int getIndexByHeader(Row headerRow, String headerName) {
        for (Cell cell : headerRow) {
            if (cell != null
                    && cell.getCellType() == CellType.STRING
                    && headerName.equals(cell.getStringCellValue().trim())) {
                return cell.getColumnIndex();
            }
        }
        throw new IllegalArgumentException("未找到表头字段: " + headerName);
    }

    private static String getCellStringValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) return "";

        if (cell.getCellType() == CellType.NUMERIC) {
            return String.valueOf(cell.getNumericCellValue());
        } else {
            return cell.getStringCellValue().trim();
        }
    }

    private static BigDecimal getCellNumericValue(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) return BigDecimal.ZERO;

        CellType cellType = cell.getCellType();
        if (cellType == CellType.NUMERIC) {
            return BigDecimal.valueOf(cell.getNumericCellValue());
        } else if (cellType == CellType.STRING) {
            try {
                return new BigDecimal(cell.getStringCellValue().trim());
            } catch (NumberFormatException e) {
                return BigDecimal.ZERO;
            }
        } else {
            return BigDecimal.ZERO;
        }
    }
}