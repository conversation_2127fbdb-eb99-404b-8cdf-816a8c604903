<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.bizClue.mapper.BizClueMapper">
    <select id="takeList" resultType="com.th.cms.modular.bizClue.entity.BizClue" parameterType="java.lang.Integer">
        select
        bc.*
        from
        biz_clue bc
        left join biz_clue_receive bcr on
        bcr.clue_id = bc.id
        left join biz_clue_release bcre on
        bcre.clue_id = bc.id
        where
        bcr.clue_id is null
        and bcre.clue_id is null
        and bc.receive_num <![CDATA[ < ]]>  5
        and bc.leads_check_status = 1
        <if test="platform != null and platform != ''">
            <!-- CDATA 写法 -->
            and bc.platform = #{platform}
        </if>
        <if test="category1 != null and category1 != ''">
            and bc.cate1 = #{category1}
        </if>
        <if test="category2 != null and category2 != ''">
            and bc.cate2 = #{category2}
        </if>
        <if test="extPlatform != null and extPlatform != ''">
            and bc.extend_platform = #{extPlatform}
        </if>
        <if test="funsCountStart != null">
            and bc.fans_num <![CDATA[ >= ]]> #{funsCountStart}
        </if>
        <if test="funsCountEnd != null">
            and bc.fans_num <![CDATA[ < ]]> #{funsCountEnd}
        </if>
        order by
        bc.yet_platform_count asc,
        bc.fans_num desc,
        bc.works_num desc
        limit #{takeClueCount}
    </select>
    <select id="leftTakeCount" resultType="java.lang.Integer">
        select
            count(*)
        from
            biz_clue bc
                left join biz_clue_receive bcr on
                bcr.clue_id = bc.id
                left join biz_clue_release bcre on
                bcre.clue_id = bc.id
        where
            bcr.clue_id is null
          and bcre.clue_id is null
          and bc.receive_num <![CDATA[ < ]]>  5
          and bc.leads_check_status = 1
    </select>


    <update id="countUp">
        update biz_clue set receive_num = ifnull(receive_num,0) +1
        where id in
        <foreach collection="clueIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <update id="countDown">
        update biz_clue set receive_num = ifnull(receive_num,0) -1
        where id in
        <foreach collection="clueIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <!--<resultMap id="baseResultMap" type="com.th.cms.modular.bizClue.entity.BizClue">
        <result column="id" property="id"/>
        <result column="mobile" property="mobile"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
        <result column="nickname" property="nickname"/>
        <result column="account" property="account"/>
        <result column="fans_num" property="fansNum"/>
        <result column="cate1" property="cate1"/>
        <result column="cate2" property="cate2"/>
        <result column="works_num" property="worksNum"/>
        <result column="days_num_month" property="daysNumMonth"/>
        <result column="platform" property="platform"/>
        <result column="extend_platform" property="extendPlatform"/>
        <result column="extend_platform_id" property="extendPlatformId"/>
        <result column="wx_num" property="wxNum"/>
        <result column="account_id" property="accountId"/>
        <result column="release_time" property="releaseTime"/>
        <result column="receive_num" property="receiveNum"/>
        <result column="upload_staff_id" property="uploadStaffId"/>
        <result column="is_jianlian" property="isJianlian"/>
        <result column="is_touch" property="isTouch"/>
        <result column="is_cancel" property="isCancel"/>
        <result column="yet_platform" property="yetPlatform"/>
        <result column="is_cancel" property="isCancel"/>
        <result column="is_cancel" property="isCancel"/>
        <result column="is_cancel" property="isCancel"/>
        <result column="is_cancel" property="isCancel"/>
        <result column="is_cancel" property="isCancel"/>
        <result column="is_cancel" property="isCancel"/>
    </resultMap>-->
</mapper>
