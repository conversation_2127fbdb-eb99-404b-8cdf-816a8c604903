package com.th.cms.modular.settle.settleCore.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.th.cms.modular.settle.settleBatch.plat.pdd.CommonSettleFiledModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TaoBaoDaShangSettleExcel {
    @ExcelProperty(value = "主播昵称")
    private String name;

    @ExcelProperty(value = "收获代币")
    private BigDecimal amount;

    public CommonSettleFiledModel buildCommonSettleFiledModel() {
        CommonSettleFiledModel model = new CommonSettleFiledModel();
        model.setInfluencerNickname(name);
        model.setRebateAmount(amount);
        return model;
    }
}
