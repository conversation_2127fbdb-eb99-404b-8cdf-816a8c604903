package com.th.cms.modular.wf.wfStep.model;

public enum AssignType {
    role(1,"role", "根据角色选择"),
    user(2,"user", "根据人员选择"),
    dept(3,"dept", "根据部门选择"),
    ;

    public Integer code;
    public String value;
    public String name;

    AssignType(Integer code,String value, String name) {
        this.code = code;
        this.value = value;
        this.name = name;
    }

    public static AssignType getTypeByValue(String value) {
        AssignType[] DELFLAGList = AssignType.values();
        for (AssignType DELFLAG : DELFLAGList) {
            if (DELFLAG.value.equals(value)) {
                return DELFLAG;
            }
        }
        return null;
    }

    public static AssignType getType(Integer code) {
        AssignType[] delflagList = AssignType.values();
        for (AssignType assignType : delflagList) {
            if (assignType.code.equals(code)) {
                return assignType;
            }
        }
        return null;
    }
}
