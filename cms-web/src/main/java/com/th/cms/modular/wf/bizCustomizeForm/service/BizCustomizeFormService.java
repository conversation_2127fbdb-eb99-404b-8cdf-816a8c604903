package com.th.cms.modular.wf.bizCustomizeForm.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.wf.bizCustomizeForm.dao.BizCustomizeFormMapper;
import com.th.cms.modular.wf.bizCustomizeForm.model.BizCustomizeForm;
import com.th.cms.modular.wf.bizCustomizeForm.model.reqparam.BizCustomizeFormListParam;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizCustomizeFormService extends ServiceImpl<BizCustomizeFormMapper, BizCustomizeForm> implements IService<BizCustomizeForm> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizCustomizeFormListParam param) {
        QueryWrapper<BizCustomizeForm> objectQueryWrapper = new QueryWrapper<>();
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;

    }
}
