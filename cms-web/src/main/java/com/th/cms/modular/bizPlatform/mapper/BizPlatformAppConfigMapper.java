package com.th.cms.modular.bizPlatform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAppConfig;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformDTO;
import com.th.cms.modular.influcer.cooperationPlatform.dto.CooperationPlatformListParamDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *O
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface BizPlatformAppConfigMapper extends BaseMapper<BizPlatformAppConfig> {



    Page<AppPlatformDTO> listByPage(@Param("objectPage") Page<Object> objectPage, @Param("paramDTO") CooperationPlatformListParamDTO paramDTO);
}
