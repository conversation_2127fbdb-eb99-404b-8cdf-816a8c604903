<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.influcer.bizInflucerPlatform.dao.BizInflucerPlatformMapper">
    <select id = "auditList"
            parameterType="com.th.cms.modular.influcer.bizInflucerPlatform.model.reqparam.BizInflucerPlatformListParam"
            resultType="com.th.cms.modular.influcer.bizInflucerPlatform.model.InflucerPlatformAuditVO">
        SELECT
            a.id,
            b.nike_name,
            b.id as influcerId,
            b.influcer_id as influcerNo,
            b.login_tel,
            a.platform_id,
            a.platform_name,
            a.source_from,
            a.create_time,
            a.state,
            a.approve_user_id,
            a.approve_user_name,
            a.approve_time
        FROM
            biz_influcer_platform a
                LEFT JOIN biz_influcer b ON a.influcer_id = b.id
        where
            1=1
        <if test="param.keywords != null and param.keywords != '' ">
            and ( b.influcer_id like concat('%',#{param.keywords},'%') or b.nike_name like concat('%',#{param.keywords},'%'))
        </if>
        <if test="param.phone != null and param.phone !='' ">
            and b.login_tel like concat('%',#{param.phone},'%')
        </if>
        <if test="param.platformId != null">
            and a.platform_id = #{param.platformId}
        </if>
        <if test="param.sourceForm != null">
            and a.source_from= #{param.sourceForm}
        </if>
        <if test="param.approveUser != null">
            and a.approve_user_id = #{param.approveUser}
        </if>
        <if test="param.approveStatus != null and param.approveStatus != ''">
            and a.state= #{param.approveStatus}
        </if>
        <if test="param.approveStartTime != null">
            and a.approve_time &gt;= #{param.approveStartTime}
        </if>
        <if test="param.approveEndTime != null">
            and a.approve_time &lt;= #{param.approveStartTime}
        </if>

    </select>

    <select id = "auditListCount" parameterType="com.th.cms.modular.influcer.bizInflucerPlatform.model.reqparam.BizInflucerPlatformListParam" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
        biz_influcer_platform a
        LEFT JOIN biz_influcer b ON a.influcer_id = b.id
        where
        1=1
        <if test="param.keywords != null and param.keywords != '' ">
            and ( b.influcer_id like concat('%',#{param.keywords},'%') or b.nike_name like concat('%',#{param.keywords},'%'))
        </if>
        <if test="param.phone != null and param.phone !='' ">
            and b.login_tel like concat('%',#{param.phone},'%')
        </if>
        <if test="param.platformId != null">
            and a.platform_id = #{param.platformId}
        </if>
        <if test="param.sourceForm != null">
            and a.source_from= #{param.sourceForm}
        </if>
        <if test="param.approveUser != null">
            and a.approve_user_id = #{param.approveUser}
        </if>
        <if test="param.approveStatus != null and param.approveStatus != ''">
            and a.state= #{param.approveStatus}
        </if>
        <if test="param.approveStartTime != null">
            and a.approve_time &gt;= #{param.approveStartTime}
        </if>
        <if test="param.approveEndTime != null">
            and a.approve_time &lt;= #{param.approveStartTime}
        </if>

    </select>
    <select id="getProjectByBizId" resultType="com.th.cms.modular.bizClue.dto.MyInflucerProjectDTO"
            parameterType="java.lang.Long">
        select
            sp.id as projectId,
            sp.project_name as projectName,
            bip.influcer_id as influcerId
        from
            biz_influcer_platform bip inner join biz_influcer_project bip2 on bip.platform_id = bip2.platform_id and bip.influcer_id = bip2.influcer_id
            inner join settle_projects sp on sp.id = bip2.project_id
        where
            business_id = #{userId}
    </select>
    <select id="myInflucerList" resultType="com.th.cms.modular.bizClue.dto.MyInflucerListDTO"
            parameterType="com.th.cms.modular.bizClue.dto.MyInflucerListRequestDTO">

        select
        bip.influcer_id ,
        bi.nike_name,
        bi.facial_picture_url as icon,
        bi.login_tel ,
        GROUP_CONCAT(bip.platform_id ) platIdList,
        bi.auth_time ,
        bi.is_verification ,
        count(bip2.id) projectCount,
        GROUP_CONCAT(bip2.project_id ) projectIdList,
        sa.zong_amount as incomeAmount
        from biz_influcer_platform bip inner join settle_account sa on bip.influcer_id = user_id
        inner join biz_influcer_project bip2 on bip.influcer_id = bip2.influcer_id and bip.platform_id = bip.platform_id
        inner join biz_influcer bi on bi.id = bip.influcer_id and bi.id = bip2.influcer_id
        <where>
            <if test="userId != null">
                bip.business_id = #{userId}
            </if>
            <if test="self == false">
                and bip.business_id != #{userId}
            </if>
            <if test="userIdList != null and userIdList.size > 0">
                and business_id in
                <foreach collection="userIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="incomeStart != null">
                and sa.zong_amount <![CDATA[ > ]]> #{dto.incomeStart}
            </if>
            <if test="incomeEnd != null">
                and sa.zong_amount <![CDATA[ < ]]> #{dto.incomeEnd}
            </if>
            <if test="cooperationStartTime != null">
                and bip.create_time <![CDATA[ >=]]>  #{dto.cooperationStartTime}
            </if>
            <if test="cooperationEndtime != null">
                and bip.create_time <![CDATA[ <=]]>  #{dto.cooperationEndtime}
            </if>
            <if test="isVerification != null">
                and bi.is_verification = #{dto.isVerification}
            </if>
            <if test="projectId != null">
                and bip2.project_id = #{dto.projectId}
            </if>
            <if test="cooperationPlatform != null">
                and bip.platform_id =#{dto.cooperationPlatform}
            </if>
        </where>
        group by bip.influcer_id
    </select>
    <select id="getPlatformByInflucerIdAndPlatformIds" resultType="com.th.cms.modular.bizClue.dto.MyInflucerPlatformAuthDTO">
        select
        bip.influcer_id as influcerId,
        bp.id as platformId,
        bp.plat_name as platformName,
        bp.icon_pic as icon
        from
        biz_influcer_platform bip
        inner join biz_platform bp on bp.id = bip.platform_id
        <where>
        <if test="influcerId != null">
            bip.influcer_id = #{influcerId}
        </if>
        <if test="platformIds != null and platformIds.size > 0">
            and bp.id in
            <foreach collection="platformIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        </where>
    </select>
    <select id="myInflucerNums" resultType="com.th.cms.modular.bizClue.dto.MyInflucerListNumDTO"
            parameterType="com.th.cms.modular.bizClue.dto.MyInflucerListRequestDTO">
        select count(*) influcerNum,SUM(zong_amount) totalIncome from (
        select sa.zong_amount ,bip.influcer_id from biz_influcer_platform bip  inner join settle_account sa  on bip.influcer_id = user_id
        inner join biz_influcer_project bip2 on bip.influcer_id = bip2.influcer_id and bip.platform_id = bip.platform_id
        inner join biz_influcer bi on bi.id = bip.influcer_id and bi.id = bip2.influcer_id
        <where>
            <if test="userId != null">
                business_id = #{userId}
            </if>
            <if test="userIdList != null and userIdList.size() >0">
                and business_id in
                <foreach collection="userIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="incomeStart != null">
                and sa.zong_amount <![CDATA[ > ]]> #{incomeStart}
            </if>
            <if test="incomeEnd != null">
                and sa.zong_amount <![CDATA[ < ]]> #{incomeEnd}
            </if>
            <if test="cooperationStartTime != null">
                and bip.create_time <![CDATA[ >=]]>  #{cooperationStartTime}
            </if>
            <if test="cooperationEndtime != null">
                and bip.create_time <![CDATA[ <=]]>  #{cooperationEndtime}
            </if>
            <if test="isVerification != null">
                and bi.is_verification = #{isVerification}
            </if>
            <if test="projectId != null">
                and bip2.project_id = #{projectId}
            </if>
            <if test="cooperationPlatform != null">
                and bip.platform_id =#{cooperationPlatform}
            </if>
        </where>
        group by bip.influcer_id) t;
    </select>
    <select id="myInflucerExportList" resultType="com.th.cms.modular.bizClue.dto.MyInflucerListDTO"
            parameterType="com.th.cms.modular.bizClue.dto.MyInflucerListRequestDTO">
        select
        bip.influcer_id ,
        bi.nike_name,
        bi.facial_picture_url as icon,
        bi.login_tel ,
        GROUP_CONCAT(bip.platform_id ) platIdList,
        bi.auth_time ,
        bi.is_verification ,
        count(bip2.id) projectCount,
        GROUP_CONCAT(sp.platform ) platNameList,
        GROUP_CONCAT(sp.project_name ) projectNameList,
        GROUP_CONCAT(bip2.project_id ) projectIdList,
        sa.zong_amount as incomeAmount
        from biz_influcer_platform bip inner join settle_account sa on bip.influcer_id = user_id
        inner join biz_influcer_project bip2 on bip.influcer_id = bip2.influcer_id and bip.platform_id = bip.platform_id
        inner join biz_influcer bi on bi.id = bip.influcer_id and bi.id = bip2.influcer_id
        inner join settle_projects sp on sp.id = bip2.project_id
        <where>
            <if test="userId != null">
                business_id = #{dto.userId}
            </if>
            <if test="userIdList != null and userIdList.size > 0">
                and business_id in
                <foreach collection="userIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="incomeStart != null">
                and sa.zong_amount <![CDATA[ > ]]> #{dto.incomeStart}
            </if>
            <if test="incomeEnd != null">
                and sa.zong_amount <![CDATA[ < ]]> #{dto.incomeEnd}
            </if>
            <if test="cooperationStartTime != null">
                and bip.create_time <![CDATA[ >=]]>  #{dto.cooperationStartTime}
            </if>
            <if test="cooperationEndtime != null">
                and bip.create_time <![CDATA[ <=]]>  #{dto.cooperationEndtime}
            </if>
            <if test="isVerification != null">
                and bi.is_verification = #{dto.isVerification}
            </if>
            <if test="projectId != null">
                and bip2.project_id = #{dto.projectId}
            </if>
            <if test="cooperationPlatform != null">
                and bip.platform_id =#{dto.cooperationPlatform}
            </if>
        </where>
        group by bip.influcer_id
    </select>
</mapper>