package com.th.cms.modular.workorder.bizWorkorder.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.customer.bizCustomer.model.BizCustomer;
import com.th.cms.modular.customer.bizCustomer.service.BizCustomerService;
import com.th.cms.modular.workorder.bizWorkorder.dao.BizWorkorderMapper;
import com.th.cms.modular.workorder.bizWorkorder.model.BizWorkorder;
import com.th.cms.modular.workorder.bizWorkorder.model.WorkSourceType;
import com.th.cms.modular.workorder.bizWorkorder.model.WorkStatus;
import com.th.cms.modular.workorder.bizWorkorder.model.reqparam.BizWorkorderListParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizWorkorderService extends ServiceImpl<BizWorkorderMapper, BizWorkorder> implements IService<BizWorkorder> {
    @Autowired
    BizCustomerService bizCustomerService;

    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizWorkorderListParam param) {
        QueryWrapper<BizWorkorder> objectQueryWrapper = new QueryWrapper<>();

        LambdaQueryWrapper<BizWorkorder> lambdaQueryWrapper = objectQueryWrapper.lambda();
        if (StringUtils.isNotBlank(param.getDaren())) {
            lambdaQueryWrapper.like(BizWorkorder::getInfucerName, "%" + param.getDaren() + "%");
        }
        if (StringUtils.isNotBlank(param.getBiaoti())) {
            lambdaQueryWrapper.like(BizWorkorder::getTitle, "%" + param.getBiaoti() + "%");
        }
        if (StringUtils.isNotBlank(param.getCustomerName())) {
            lambdaQueryWrapper.like(BizWorkorder::getCustomerName, "%" + param.getCustomerName() + "%");
        }
        if (StringUtils.isNotBlank(param.getQuestStatusname())) {
            lambdaQueryWrapper.eq(BizWorkorder::getQuestStatusname, param.getQuestStatusname());
        }
        if (StringUtils.isNotBlank(param.getImportance())) {
            lambdaQueryWrapper.eq(BizWorkorder::getImportance, param.getImportance());
        }
        if (StringUtils.isNotBlank(param.getUrgency())) {
            lambdaQueryWrapper.eq(BizWorkorder::getUrgency, param.getUrgency());
        }
        if (StringUtils.isNotBlank(param.getQuestTypeName()) && !StringUtils.equalsAnyIgnoreCase("请选择", param.getQuestTypeName())) {
            lambdaQueryWrapper.eq(BizWorkorder::getQuestTypeName, param.getQuestTypeName());
        }
        Long cuserId = ShiroKit.getUser().getId();

        BizCustomer bizCustomer = bizCustomerService.queryByUserId(cuserId + "");
        if (StringUtils.isNotBlank(param.getTp()) && StringUtils.equalsAnyIgnoreCase("create", param.getTp())) {
            lambdaQueryWrapper.eq(BizWorkorder::getQuestStatus, WorkStatus.daichuli.value);
            lambdaQueryWrapper.eq(BizWorkorder::getCreateId, cuserId);
            lambdaQueryWrapper.eq(BizWorkorder::getWorkerSource, WorkSourceType.kefu.value);
        } else if (StringUtils.isNotBlank(param.getTp()) && StringUtils.equalsAnyIgnoreCase("receive", param.getTp())) {
            lambdaQueryWrapper.eq(BizWorkorder::getCustomerId, bizCustomer.getId());
        }
        lambdaQueryWrapper.orderByDesc(BizWorkorder::getCreateTime);
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;

    }
}
