package com.th.cms.modular.sysmesage.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.th.cms.core.util.Objects;
import com.th.cms.modular.influcer.bizInflucer.dao.BizInflucerMapper;
import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import com.th.cms.modular.sysmesage.consts.ReceiverType;
import com.th.cms.modular.sysmesage.consts.SendType;
import com.th.cms.modular.sysmesage.convert.SysMessageConvert;
import com.th.cms.modular.sysmesage.dto.MessageRequestDTO;
import com.th.cms.modular.sysmesage.dto.SysMessageTemplateDTO;
import com.th.cms.modular.sysmesage.entity.SysMessage;
import com.th.cms.modular.sysmesage.entity.SysMessageTemplate;
import com.th.cms.modular.sysmesage.entity.SysMessageTemplateRel;
import com.th.cms.modular.sysmesage.mapper.SysMessageMapper;
import com.th.cms.modular.sysmesage.mapper.SysMessageTemplateMapper;
import com.th.cms.modular.sysmesage.strategy.send.IMessageHandler;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MessageSendHelper {
    @Autowired
    private SysMessageMapper messageMapper;
    @Autowired
    private SysMessageTemplateMapper templateMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private BizInflucerMapper bizInflucerMapper;
    @Autowired
    private MessageTemplateUtil messageTemplateUtil;
    @Autowired
    private SysMessageConvert convert;

    /**
     * 发送消息
     *
     * @param interfaceCode  接口编码
     * @param params         参数
     * @param currentUser    当前用户
     * @param influcerIdList 达人ID
     */
    public void sendMessage(String interfaceCode, Map<String, Object> params, Long currentUser, List<Long> influcerIdList) {
        SysMessage message = queryMessage(interfaceCode);
        if (message == null) {
            log.error("消息模板不存在 interfaceCode={}", interfaceCode);
            return;
        }
        List<SysMessageTemplateDTO> sysMessageTemplateDTOS = templeFromMessage(message, params);
        for (SysMessageTemplateDTO sysMessageTemplateDTO : sysMessageTemplateDTOS) {
            if (sysMessageTemplateDTO == null || StringUtils.isBlank(sysMessageTemplateDTO.getRealContent())) {
                log.error("模板渲染内容为空 interfaceCode={}", interfaceCode);
                return;
            }
            doSend(message, sysMessageTemplateDTO, currentUser, influcerIdList);
        }

    }

    /**
     * 发送消息
     *
     * @param message        接口消息
     * @param templateDTO    模板信息
     * @param currentUser    当前用户
     * @param influcerIdList
     */
    private void doSend(SysMessage message, SysMessageTemplateDTO templateDTO, Long currentUser, List<Long> influcerIdList) {
        Integer messageType = templateDTO.getSysMessageTemplate().getMsgType();
        SendType sendType = SendType.getSendType(messageType);
        if (sendType == null) {
            log.error("消息类型不存在 messageType={}", messageType);
            return;
        }
        Class<? extends IMessageHandler> sendHandler = sendType.getSendHandler();
        if (sendHandler == null) {
            log.error("消息发送处理类不存在 messageType={}", messageType);
            return;
        }
        MessageRequestDTO requestDTO = new MessageRequestDTO();
        requestDTO.setMessage(message);
        requestDTO.setTemplateDTO(templateDTO);
        if (Objects.equals(sendType.getCode(), SendType.APP.getCode())) {
            if (CollectionUtil.isEmpty(influcerIdList)) {
                log.error("APP 消息 发送时 达人列表为空");
                return;
            }
            requestDTO.setInflucers(queryInflucers(influcerIdList));
        } else {

            User currentUserEntity = queryCurrentUser(currentUser);
            requestDTO.setCurrentUser(currentUserEntity);
            List<User> users = queryUsers(message, currentUserEntity);
            requestDTO.setUsers(users);
        }

        SpringUtil.getBean(sendHandler).sendMessage(requestDTO);
    }

    private List<BizInflucer> queryInflucers(List<Long> influcerIdList) {
        return bizInflucerMapper.selectList(new LambdaQueryWrapper<BizInflucer>().in(BizInflucer::getId, influcerIdList));
    }

    private User queryCurrentUser(Long currentUser) {
        if (currentUser != null) {
            return userMapper.selectById(currentUser);
        }
        return null;

    }

    private List<User> queryUsers(SysMessage message, User currentUser) {
        Integer receiverType = message.getReceiverType();
        ReceiverType receiveType = ReceiverType.getReceiveType(receiverType);
        if (receiveType == null) {
            return null;
        }
        return SpringUtil.getBean(receiveType.getSendHandler()).getReceiverUsers(message, currentUser);
    }

    /**
     * 渲染模板
     *
     * @param message 接口消息
     * @param params  参数
     * @return 模板信息
     */
    private List<SysMessageTemplateDTO> templeFromMessage(SysMessage message, Map<String, Object> params) {
        List<SysMessageTemplateRel> sysMessageReceiverConfigs = convert.querySendTypes(message.getId());
        return sysMessageReceiverConfigs.stream().map(sysMessageReceiverConfig -> {
            Long templateId = sysMessageReceiverConfig.getTemplateId();
            SysMessageTemplate template = queryTemplate(templateId);
            if (template == null) {
                log.error("消息模板不存在 interfaceCode={} templateId = {}", message.getInterfaceCode(), templateId);
                return null;
            }
            String content = template.getContent();
            return SysMessageTemplateDTO.builder().params(params).sysMessageTemplate(template).realContent(messageTemplateUtil.formatMessage(content, params)).build();

        }).collect(Collectors.toList());
    }

    private SysMessageTemplate queryTemplate(Long templateId) {
        if (templateId == null) {
            return null;
        }
        return templateMapper.selectById(templateId);
    }

    private SysMessage queryMessage(String interfaceCode) {
        LambdaQueryWrapper<SysMessage> qw = Wrappers.lambdaQuery();
        qw.eq(SysMessage::getInterfaceCode, interfaceCode);
        List<SysMessage> sysMessages = messageMapper.selectList(qw);
        if (CollectionUtil.isEmpty(sysMessages)) {
            return null;
        }
        return sysMessages.get(0);
    }

}
