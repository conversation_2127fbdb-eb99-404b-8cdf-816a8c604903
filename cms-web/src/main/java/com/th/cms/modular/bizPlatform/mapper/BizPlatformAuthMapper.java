package com.th.cms.modular.bizPlatform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuth;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformDTO;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformRequestDTO;
import com.th.cms.modular.influcer.cooperationPlatform.dto.BasePlatformRequestDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface BizPlatformAuthMapper extends BaseMapper<BizPlatformAuth> {
    Page<AppPlatformDTO> listByPage(@Param("objectPage") Page<AppPlatformDTO> objectPage, @Param("record") AppPlatformRequestDTO paramDTO,
                                    @Param("userId") Long userId);
    Page<BizPlatformAuth> basePlatformList(@Param("page") IPage<BizPlatformAuth> page, @Param("record") BasePlatformRequestDTO paramDTO, @Param("userId") Long userId);
}
