package com.th.cms.modular.wf.wfInflucerSubmitApprove.model;

import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 达人提审记录表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="WfInflucerSubmitApprove对象", description="达人提审记录表")
public class WfInflucerSubmitApprove implements Serializable {

    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private Long projectId;

    @ApiModelProperty(value = "项目名称")
    @TableField("project_name")
    private String projectName;

    @ApiModelProperty(value = "达人ID")
    @TableField("influcer_id")
//    @ExcelField(title = "input68456")
    private String influcerId;

    @ApiModelProperty(value = "达人数据id")
    @TableField("influ_id")
    private Long influId;

    @ApiModelProperty(value = "达人昵称")
    @TableField("influcer_name")
    @ExcelField(title = "input54603")
    private String influcerName;

    @ApiModelProperty(value = "平台ID")
    @TableField("platform_id")
    private Long platformId;

    @ApiModelProperty(value = "平台名称")
    @TableField("platform_name")
    private String platformName;

    @ApiModelProperty(value = "达人类型")
    @TableField("influcer_type")
    @ExcelField(title = "select29667")
    private Integer influcerType;

    @TableField(exist = false)
    private String influcerTypeName;

    @ApiModelProperty(value = "外站头像")
    @TableField("out_avatar")
    private String outAvatar;

    @ApiModelProperty(value = "外站平台")
    @TableField("out_platform_name")
    @ExcelField(title = "select79418")
    private String outPlatformName;

    @ApiModelProperty(value = "外站ID")
    @TableField("out_id")
    @ExcelField(title = "input68456")
    private String outId;

    @ApiModelProperty(value = "外站name")
    @TableField("out_name")
    private String outName;

    @ApiModelProperty(value = "外站链接")
    @TableField("out_link")
    @ExcelField(title = "input39515")
    private String outLink;

    @ApiModelProperty(value = "手机号")
    @TableField("phone")
    @ExcelField(title = "inputphone70616")
    private String phone;

    @ApiModelProperty(value = "分成比")
    @TableField("split_ratio")
    @ExcelField(title = "number102282")
    private String splitRatio;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    @ExcelField(title = "textarea31921")
    private String remark;

    @ApiModelProperty(value = "阶段id")
    @TableField("step_id")
    private Long stepId;

    @ApiModelProperty(value = "节点状态")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "状态名")
    @TableField("status_name")
    private String statusName;

    @ApiModelProperty(value = "状态颜色")
    @TableField(exist = false)
    private String statusColor;

    @ApiModelProperty(value = "企微状态")
    @TableField("wechat_status")
    private Integer wechatStatus;

    @ApiModelProperty(value = "创建人id-商务")
    @TableField("create_id")
    private Long createId;

    @ApiModelProperty(value = "创建人id-商务")
    @TableField("bussi_source")
    private String bussiSource;

    @ApiModelProperty(value = "创建人id-商务")
    @TableField("bussi_name")
    private String bussiName;


    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "审批单")
    @TableField("request_id")
    private Long requestId;

    @ApiModelProperty(value = "列表流程权限")
    @TableField(exist = false)
    private Integer operateType;//0不可以操作  1邀约  2绑定  3运营绑定

    @ApiModelProperty(value = "列表按钮权限")
    @TableField(exist = false)
    private List<String> operatePerm;//ALL、MOD、DEL、READ

    @ApiModelProperty(value = "是否完成")
    @TableField("complete")
    private Integer complete;
}
