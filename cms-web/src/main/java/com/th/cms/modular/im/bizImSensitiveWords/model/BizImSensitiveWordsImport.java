package com.th.cms.modular.im.bizImSensitiveWords.model;

import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value="敏感词导入对象", description="")
public class BizImSensitiveWordsImport implements Serializable {


    @ExcelField(title="敏感词",dictType="", align=2, sort=0)
    @ApiModelProperty(value = "敏感词")
    private String sensitiveWords;

}
