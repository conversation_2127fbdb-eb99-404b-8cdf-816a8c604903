package com.th.cms.modular.influcer.bizPlatform.mapper;

import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.th.cms.modular.influcer.cooperationPlatform.dto.CooperationPlatformListParamDTO;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface BizPlatformMapper extends BaseMapper<BizPlatform> {

    /**
     * 查询合作平台列表
     */
    List<BizPlatform> queryCooperationPlatformList(CooperationPlatformListParamDTO paramDTO);

    /**
     * 查询合作平台count
     */
    Long queryCooperationPlatformCount(CooperationPlatformListParamDTO paramDTO);
}
