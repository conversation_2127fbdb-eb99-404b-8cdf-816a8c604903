package com.th.cms.modular.settle.settleAccount.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;


/**
 * <p>
 * 达人结算订单表
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="SettlePlatAccount对象", description="达人结算订单表")
public class SettlePlatAccount implements Serializable {


    @ApiModelProperty(value = "主键ID（自增）")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "达人ID")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "达人昵称")
    @TableField("nick_name")
    private String nickName;

    @ApiModelProperty(value = "达人手机号")
    @TableField("user_tel")
    private String userTel;

    @ApiModelProperty(value = "达人姓名")
    @TableField("user_name")
    private String userName;

    @ApiModelProperty(value = "账户号")
    @TableField("account_no")
    private String accountNo;

    @ApiModelProperty(value = "总收益")
    @TableField("zong_amount")
    private BigDecimal zongAmount;

    @ApiModelProperty(value = "提现金额")
    @TableField("tixian_amount")
    private BigDecimal tixianAmount;

    @ApiModelProperty(value = "可用余额")
    @TableField("amount")
    private BigDecimal amount;

    @ApiModelProperty(value = "垫付收益")
    @TableField("dianfu_amount")
    private BigDecimal dianfuAmount;

    @ApiModelProperty(value = "账户状态")
    @TableField("account_status")
    private Integer accountStatus;

    @ApiModelProperty(value = "账户状态")
    @TableField("account_status_name")
    private String accountStatusName;

    @ApiModelProperty(value = "平台")
    @TableField("plat_id")
    private Integer platId;

    @ApiModelProperty(value = "平台")
    @TableField("plat_name")
    private String platName;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_id")
    private String createId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private String deptId;

    @ApiModelProperty(value = "部门")
    @TableField("dept_name")
    private String deptName;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ExcelField(title="主键ID（自增）",dictType="", align=2, sort=0)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @ExcelField(title="达人ID",dictType="", align=2, sort=1)
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @ExcelField(title="达人昵称",dictType="", align=2, sort=2)
    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
    @ExcelField(title="达人手机号",dictType="", align=2, sort=3)
    public String getUserTel() {
        return userTel;
    }

    public void setUserTel(String userTel) {
        this.userTel = userTel;
    }
    @ExcelField(title="达人姓名",dictType="", align=2, sort=4)
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    @ExcelField(title="账户号",dictType="", align=2, sort=5)
    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
    @ExcelField(title="总收益",dictType="", align=2, sort=6)
    public BigDecimal getZongAmount() {
        return zongAmount;
    }

    public void setZongAmount(BigDecimal zongAmount) {
        this.zongAmount = zongAmount;
    }
    @ExcelField(title="提现金额",dictType="", align=2, sort=7)
    public BigDecimal getTixianAmount() {
        return tixianAmount;
    }

    public void setTixianAmount(BigDecimal tixianAmount) {
        this.tixianAmount = tixianAmount;
    }
    @ExcelField(title="可用余额",dictType="", align=2, sort=8)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    @ExcelField(title="垫付收益",dictType="", align=2, sort=9)
    public BigDecimal getDianfuAmount() {
        return dianfuAmount;
    }

    public void setDianfuAmount(BigDecimal dianfuAmount) {
        this.dianfuAmount = dianfuAmount;
    }
    @ExcelField(title="账户状态",dictType="", align=2, sort=10)
    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }
    @ExcelField(title="账户状态",dictType="", align=2, sort=11)
    public String getAccountStatusName() {
        return accountStatusName;
    }

    public void setAccountStatusName(String accountStatusName) {
        this.accountStatusName = accountStatusName;
    }
    @ExcelField(title="平台",dictType="", align=2, sort=12)
    public Integer getPlatId() {
        return platId;
    }

    public void setPlatId(Integer platId) {
        this.platId = platId;
    }
    @ExcelField(title="平台",dictType="", align=2, sort=13)
    public String getPlatName() {
        return platName;
    }

    public void setPlatName(String platName) {
        this.platName = platName;
    }
    @ExcelField(title="创建人ID",dictType="", align=2, sort=14)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
    @ExcelField(title="创建人",dictType="", align=2, sort=15)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    @ExcelField(title="部门ID",dictType="", align=2, sort=16)
    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
    @ExcelField(title="部门",dictType="", align=2, sort=17)
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    @ExcelField(title="公司ID",dictType="", align=2, sort=18)
    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
    @ExcelField(title="公司名称",dictType="", align=2, sort=19)
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=20)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=21)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "SettlePlatAccount{" +
        "id=" + id +
        ", userId=" + userId +
        ", nickName=" + nickName +
        ", userTel=" + userTel +
        ", userName=" + userName +
        ", accountNo=" + accountNo +
        ", zongAmount=" + zongAmount +
        ", tixianAmount=" + tixianAmount +
        ", amount=" + amount +
        ", dianfuAmount=" + dianfuAmount +
        ", accountStatus=" + accountStatus +
        ", accountStatusName=" + accountStatusName +
        ", platId=" + platId +
        ", platName=" + platName +
        ", createId=" + createId +
        ", createName=" + createName +
        ", deptId=" + deptId +
        ", deptName=" + deptName +
        ", companyId=" + companyId +
        ", companyName=" + companyName +
        ", updateTime=" + updateTime +
        ", createTime=" + createTime +
        "}";
    }
}
