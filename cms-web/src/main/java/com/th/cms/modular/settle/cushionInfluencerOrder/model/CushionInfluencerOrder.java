package com.th.cms.modular.settle.cushionInfluencerOrder.model;

import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <p>
 * 达人垫付订单表
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="CushionInfluencerOrder对象", description="达人垫付订单表")
public class CushionInfluencerOrder implements Serializable {


    @ApiModelProperty(value = "主键ID（自增）")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户昵称")
    @TableField("influencer_name")
    private String influencerName;

    @ApiModelProperty(value = "达人唯一标识（如明月号ID）")
    @TableField("influencer_id")
    private String influencerId;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private Integer projectId;

    @ApiModelProperty(value = "项目简称")
    @TableField("project_name")
    private String projectName;

    @ApiModelProperty(value = "垫付订单ID")
    @TableField("cushion_order_id")
    private Long cushionOrderId;

    @ApiModelProperty(value = "用户注册手机号")
    @TableField("phone_number")
    private String phoneNumber;

    @ApiModelProperty(value = "公司与达人分成比例（示例：70%/30%）")
    @TableField("company_talent_ratio")
    private String companyTalentRatio;

    @ApiModelProperty(value = "垫付批次号（格式：YYYYMMDD+序号）")
    @TableField("cushionment_batch")
    private String cushionmentBatch;

    @ApiModelProperty(value = "收益来源平台昵称（如抖音、快手）")
    @TableField("platform_nickname")
    private String platformNickname;

    @ApiModelProperty(value = "收益来源平台账号ID")
    @TableField("platform_id")
    private String platformId;

    @ApiModelProperty(value = "订单状态")
    @TableField("infl_status")
    private Integer inflStatus;

    @ApiModelProperty(value = "订单转台")
    @TableField("infl_status_name")
    private String inflStatusName;

    @ApiModelProperty(value = "总收益金额（单位：元）")
    @TableField("earnings_amount")
    private BigDecimal earningsAmount;

    @ApiModelProperty(value = "应发放金额（税后）")
    @TableField("payable_amount")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "已提现金额")
    @TableField("withdrawn_amount")
    private BigDecimal withdrawnAmount;

    @ApiModelProperty(value = "待提现金额（计算字段）")
    @TableField("unwithdrawn_amount")
    private BigDecimal unwithdrawnAmount;

    @ApiModelProperty(value = "是否已到账")
    @TableField("is_arrived")
    private Integer isArrived;

    @ApiModelProperty(value = "未到账原因（如银行审核失败、金额不足）")
    @TableField("arrival_issue")
    private String arrivalIssue;

    @ApiModelProperty(value = "所属工区/部门")
    @TableField("work_area")
    private String workArea;

    @ApiModelProperty(value = "周期开始时间")
    @TableField("zhouqi_start")
    private Date zhouqiStart;

    @ApiModelProperty(value = "周期结束时间")
    @TableField("zhouqi_end")
    private Date zhouqiEnd;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "非标单")
    @TableField("feibiao_flg")
    private Integer feibiaoFlg;

    @ApiModelProperty(value = "运营小组名称")
    @TableField("group_name")
    private String groupName;

    @ApiModelProperty(value = "对接商务姓名")
    @TableField("business_contact")
    private String businessContact;

    @ApiModelProperty(value = "状态变更流水（JSON格式记录操作日志）")
    @TableField("status_log")
    private String statusLog;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_id")
    private String createId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private String deptId;

    @ApiModelProperty(value = "部门")
    @TableField("dept_name")
    private String deptName;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;
    @ApiModelProperty(value = "收到收益时间")
    @TableField("income_time")
    private Date incomeTime;

    public Date getIncomeTime() {
        return incomeTime;
    }

    public void setIncomeTime(Date incomeTime) {
        this.incomeTime = incomeTime;
    }

    @ExcelField(title="主键ID（自增）",dictType="", align=2, sort=0)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @ExcelField(title="用户昵称",dictType="", align=2, sort=1)
    public String getInfluencerName() {
        return influencerName;
    }

    public void setInfluencerName(String influencerName) {
        this.influencerName = influencerName;
    }
    @ExcelField(title="达人唯一标识（如明月号ID）",dictType="", align=2, sort=2)
    public String getInfluencerId() {
        return influencerId;
    }

    public void setInfluencerId(String influencerId) {
        this.influencerId = influencerId;
    }
    @ExcelField(title="项目ID",dictType="", align=2, sort=3)
    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }
    @ExcelField(title="项目简称",dictType="", align=2, sort=4)
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    @ExcelField(title="用户注册手机号",dictType="", align=2, sort=5)
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    @ExcelField(title="公司与达人分成比例（示例：70%/30%）",dictType="", align=2, sort=6)
    public String getCompanyTalentRatio() {
        return companyTalentRatio;
    }

    public void setCompanyTalentRatio(String companyTalentRatio) {
        this.companyTalentRatio = companyTalentRatio;
    }
    @ExcelField(title="垫付批次号（格式：YYYYMMDD+序号）",dictType="", align=2, sort=7)
    public String getCushionmentBatch() {
        return cushionmentBatch;
    }

    public void setCushionmentBatch(String cushionmentBatch) {
        this.cushionmentBatch = cushionmentBatch;
    }
    @ExcelField(title="收益来源平台昵称（如抖音、快手）",dictType="", align=2, sort=8)
    public String getPlatformNickname() {
        return platformNickname;
    }

    public void setPlatformNickname(String platformNickname) {
        this.platformNickname = platformNickname;
    }
    @ExcelField(title="收益来源平台账号ID",dictType="", align=2, sort=9)
    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }
    @ExcelField(title="订单状态",dictType="", align=2, sort=10)
    public Integer getInflStatus() {
        return inflStatus;
    }

    public void setInflStatus(Integer inflStatus) {
        this.inflStatus = inflStatus;
    }
    @ExcelField(title="订单转台",dictType="", align=2, sort=11)
    public String getInflStatusName() {
        return inflStatusName;
    }

    public void setInflStatusName(String inflStatusName) {
        this.inflStatusName = inflStatusName;
    }
    @ExcelField(title="总收益金额（单位：元）",dictType="", align=2, sort=12)
    public BigDecimal getEarningsAmount() {
        return earningsAmount;
    }

    public void setEarningsAmount(BigDecimal earningsAmount) {
        this.earningsAmount = earningsAmount;
    }
    @ExcelField(title="应发放金额（税后）",dictType="", align=2, sort=13)
    public BigDecimal getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(BigDecimal payableAmount) {
        this.payableAmount = payableAmount;
    }
    @ExcelField(title="已提现金额",dictType="", align=2, sort=14)
    public BigDecimal getWithdrawnAmount() {
        return withdrawnAmount;
    }

    public void setWithdrawnAmount(BigDecimal withdrawnAmount) {
        this.withdrawnAmount = withdrawnAmount;
    }
    @ExcelField(title="待提现金额（计算字段）",dictType="", align=2, sort=15)
    public BigDecimal getUnwithdrawnAmount() {
        return unwithdrawnAmount;
    }

    public void setUnwithdrawnAmount(BigDecimal unwithdrawnAmount) {
        this.unwithdrawnAmount = unwithdrawnAmount;
    }
    @ExcelField(title="是否已到账",dictType="", align=2, sort=16)
    public Integer getIsArrived() {
        return isArrived;
    }

    public void setIsArrived(Integer isArrived) {
        this.isArrived = isArrived;
    }
    @ExcelField(title="未到账原因（如银行审核失败、金额不足）",dictType="", align=2, sort=17)
    public String getArrivalIssue() {
        return arrivalIssue;
    }

    public void setArrivalIssue(String arrivalIssue) {
        this.arrivalIssue = arrivalIssue;
    }
    @ExcelField(title="所属工区/部门",dictType="", align=2, sort=18)
    public String getWorkArea() {
        return workArea;
    }

    public void setWorkArea(String workArea) {
        this.workArea = workArea;
    }
    @ExcelField(title="周期开始时间",dictType="", align=2, sort=19)
    public Date getZhouqiStart() {
        return zhouqiStart;
    }

    public void setZhouqiStart(Date zhouqiStart) {
        this.zhouqiStart = zhouqiStart;
    }
    @ExcelField(title="周期结束时间",dictType="", align=2, sort=20)
    public Date getZhouqiEnd() {
        return zhouqiEnd;
    }

    public void setZhouqiEnd(Date zhouqiEnd) {
        this.zhouqiEnd = zhouqiEnd;
    }
    @ExcelField(title="备注",dictType="", align=2, sort=21)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    @ExcelField(title="非标单",dictType="", align=2, sort=22)
    public Integer getFeibiaoFlg() {
        return feibiaoFlg;
    }

    public void setFeibiaoFlg(Integer feibiaoFlg) {
        this.feibiaoFlg = feibiaoFlg;
    }
    @ExcelField(title="运营小组名称",dictType="", align=2, sort=23)
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    @ExcelField(title="对接商务姓名",dictType="", align=2, sort=24)
    public String getBusinessContact() {
        return businessContact;
    }

    public void setBusinessContact(String businessContact) {
        this.businessContact = businessContact;
    }
    @ExcelField(title="状态变更流水（JSON格式记录操作日志）",dictType="", align=2, sort=25)
    public String getStatusLog() {
        return statusLog;
    }

    public void setStatusLog(String statusLog) {
        this.statusLog = statusLog;
    }
    @ExcelField(title="创建人ID",dictType="", align=2, sort=26)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
    @ExcelField(title="创建人",dictType="", align=2, sort=27)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    @ExcelField(title="部门ID",dictType="", align=2, sort=28)
    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
    @ExcelField(title="部门",dictType="", align=2, sort=29)
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    @ExcelField(title="公司ID",dictType="", align=2, sort=30)
    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
    @ExcelField(title="公司名称",dictType="", align=2, sort=31)
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=32)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=33)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCushionOrderId() {
        return cushionOrderId;
    }

    public void setCushionOrderId(Long cushionOrderId) {
        this.cushionOrderId = cushionOrderId;
    }

    @Override
    public String toString() {
        return "CushionInfluencerOrder{" +
                "id=" + id +
                ", influencerName='" + influencerName + '\'' +
                ", influencerId='" + influencerId + '\'' +
                ", projectId=" + projectId +
                ", projectName='" + projectName + '\'' +
                ", cushionOrderId=" + cushionOrderId +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", companyTalentRatio='" + companyTalentRatio + '\'' +
                ", cushionmentBatch='" + cushionmentBatch + '\'' +
                ", platformNickname='" + platformNickname + '\'' +
                ", platformId='" + platformId + '\'' +
                ", inflStatus=" + inflStatus +
                ", inflStatusName='" + inflStatusName + '\'' +
                ", earningsAmount=" + earningsAmount +
                ", payableAmount=" + payableAmount +
                ", withdrawnAmount=" + withdrawnAmount +
                ", unwithdrawnAmount=" + unwithdrawnAmount +
                ", isArrived=" + isArrived +
                ", arrivalIssue='" + arrivalIssue + '\'' +
                ", workArea='" + workArea + '\'' +
                ", zhouqiStart=" + zhouqiStart +
                ", zhouqiEnd=" + zhouqiEnd +
                ", remark='" + remark + '\'' +
                ", feibiaoFlg=" + feibiaoFlg +
                ", groupName='" + groupName + '\'' +
                ", businessContact='" + businessContact + '\'' +
                ", statusLog='" + statusLog + '\'' +
                ", createId='" + createId + '\'' +
                ", createName='" + createName + '\'' +
                ", deptId='" + deptId + '\'' +
                ", deptName='" + deptName + '\'' +
                ", companyId='" + companyId + '\'' +
                ", companyName='" + companyName + '\'' +
                ", updateTime=" + updateTime +
                ", createTime=" + createTime +
                '}';
    }
}
