package com.th.cms.modular.im.bizImSensitiveWords.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.th.cms.core.common.annotion.Permission;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.modular.customer.bizCustomerIm.dao.BizCustomerImMapper;
import com.th.cms.modular.im.bizImSensitiveWords.model.BizImSensitiveWords;
import com.th.cms.modular.im.bizImSensitiveWords.model.reqparam.BizImSensitiveWordsListParam;
import com.th.cms.modular.im.bizImSensitiveWords.service.BizImSensitiveWordsExtService;
import com.th.cms.modular.im.bizImSensitiveWords.service.BizImSensitiveWordsService;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * bizImSensitiveWords控制器
 *
 * <AUTHOR>
 * @Date 2025-04-01 14:35:01
 */
@CrossOrigin(origins = "*")
@Controller
@RequestMapping("/bizImSensitiveWords")
public class BizImSensitiveWordsController extends BaseController {

    private String PREFIX = "/modular/bizImSensitiveWords/";
    @Autowired
    BizImSensitiveWordsExtService bizImSensitiveWordsExtService;
    @Autowired
    private BizImSensitiveWordsService bizImSensitiveWordsService;
    @Autowired
    private BizCustomerImMapper bizCustomerImMapper;

    /**
     * 跳转到bizImSensitiveWords首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizImSensitiveWords.html";
    }

    /**
     * 跳转到添加bizImSensitiveWords
     */
    @Permission
    @RequestMapping("/bizImSensitiveWords_add")
    public String bizImSensitiveWordsAdd() {
        return PREFIX + "bizImSensitiveWords_add.html";
    }

    /**
     * 跳转到修改bizImSensitiveWords
     */
    @Permission
    @RequestMapping("/bizImSensitiveWords_update")
    public String bizImSensitiveWordsUpdate(@RequestParam Integer bizImSensitiveWordsId, Model model) {
        BizImSensitiveWords bizImSensitiveWords = bizImSensitiveWordsExtService.queryById(bizImSensitiveWordsId);
        model.addAttribute("item", bizImSensitiveWords);
        LogObjectHolder.me().set(bizImSensitiveWords);
        return PREFIX + "bizImSensitiveWords_edit.html";
    }

    @RequestMapping("/bizImSensitiveWords_detail")
    public String bizImSensitiveWordsDetail(@RequestParam Integer bizImSensitiveWordsId, Model model) {
        BizImSensitiveWords bizImSensitiveWords = bizImSensitiveWordsExtService.queryById(bizImSensitiveWordsId);
        model.addAttribute("item", bizImSensitiveWords);
        LogObjectHolder.me().set(bizImSensitiveWords);
        return PREFIX + "bizImSensitiveWords_detail.html";
    }

    /**
     * 获取bizImSensitiveWords列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(BizImSensitiveWordsListParam bizImSensitiveWordsParam) {
        return bizImSensitiveWordsService.findPageBySpec(bizImSensitiveWordsParam);
    }

    @RequestMapping(value = "/cms/list")
    @ResponseBody
    public ResponseData list2() {
        return ResponseData.success(bizImSensitiveWordsService.list(new QueryWrapper<>()));
    }

    /**
     * 新增bizImSensitiveWords
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/bizImSensitiveWords/bizImSensitiveWords_add")
    @ResponseBody
    public ResponseData add(BizImSensitiveWords bizImSensitiveWords) {

        String[] split = bizImSensitiveWords.getSensitiveWords().split(",");

        List<String> sensitiveWords = Arrays.stream(split).filter(f -> !StringUtils.isEmpty(f)).collect(Collectors.toList());

        List<String> existWords = bizImSensitiveWordsExtService.existWords(sensitiveWords);

        List<BizImSensitiveWords> words = new ArrayList<>();
        for (String word : split) {
            if (!existWords.contains(word)) {
                words.add(new BizImSensitiveWords().setSensitiveWords(word).setCreateTime(new Date()));
            }
        }
        bizImSensitiveWordsExtService.bachSave(words);

        return ResponseData.success();
    }

    /**
     * 删除bizImSensitiveWords
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer bizImSensitiveWordsId) {
        QueryWrapper<BizImSensitiveWords> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("id", bizImSensitiveWordsId);
        bizImSensitiveWordsService.remove(objectQueryWrapper);
        return ResponseData.success();
    }

    /**
     * 修改bizImSensitiveWords
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizImSensitiveWords/bizImSensitiveWords_update")
    @ResponseBody
    public ResponseData update(BizImSensitiveWords bizImSensitiveWords) {
        bizImSensitiveWordsExtService.updateById(bizImSensitiveWords);
        return ResponseData.success();
    }

    /**
     * bizImSensitiveWords详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer bizImSensitiveWordsId) {
        BizImSensitiveWords detail = bizImSensitiveWordsExtService.queryById(bizImSensitiveWordsId);
        return ResponseData.success(detail);
    }


    @RequestMapping(value = "/clear")
    @ResponseBody
    public ResponseData clear() {
        QueryWrapper<BizImSensitiveWords> objectQueryWrapper = new QueryWrapper<>();
        bizImSensitiveWordsService.remove(objectQueryWrapper);
        return ResponseData.success();
    }

    @PostMapping(value = "/import")
    @ResponseBody
    public ResponseData importSensitiveWords(@RequestPart("file") MultipartFile file)
            throws IOException, InvalidFormatException, InstantiationException, IllegalAccessException {
        Integer count = bizImSensitiveWordsExtService.importSensitiveWords(file);
        return ResponseData.success(count);
    }


    @PostMapping(value = "/btnBatchDel")
    @ResponseBody
    public ResponseData btnBatchDel(@RequestParam("ids") String ids) {
        bizImSensitiveWordsExtService.btnBatchDel(ids);
        return ResponseData.success();
    }


}
