package com.th.cms.modular.bizNomalEnums.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.bizNomalEnums.model.BizNomalEnums;
import com.th.cms.modular.bizNomalEnums.service.BizNomalEnumsService;
import com.th.cms.modular.bizNomalEnums.service.BizNomalEnumsExtService;
import com.th.cms.modular.bizNomalEnums.model.reqparam.BizNomalEnumsListParam;

/**
 * 类型设置控制器
 *
 * <AUTHOR>
 * @Date 2025-03-31 10:57:55
 */
@Controller
@RequestMapping("/bizNomalEnums")
public class BizNomalEnumsController extends BaseController {

    private String PREFIX = "/modular/bizNomalEnums/bizNomalEnums/";
    @Autowired
    BizNomalEnumsExtService bizNomalEnumsExtService;
    @Autowired
    private BizNomalEnumsService bizNomalEnumsService;

    /**
     * 跳转到类型设置首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizNomalEnums.html";
    }

    /**
     * 跳转到添加类型设置
     */
    @Permission
    @RequestMapping("/bizNomalEnums_add")
    public String bizNomalEnumsAdd() {
        return PREFIX + "bizNomalEnums_add.html";
    }

    /**
     * 跳转到修改类型设置
     */
    @Permission
    @RequestMapping("/bizNomalEnums_update")
    public String bizNomalEnumsUpdate(@RequestParam  Integer bizNomalEnumsId, Model model) {
        BizNomalEnums bizNomalEnums = bizNomalEnumsExtService.queryById(bizNomalEnumsId);
        model.addAttribute("item",bizNomalEnums);
        LogObjectHolder.me().set(bizNomalEnums);
        return PREFIX + "bizNomalEnums_edit.html";
    }
    @RequestMapping("/bizNomalEnums_detail")
    public String bizNomalEnumsDetail(@RequestParam Integer bizNomalEnumsId, Model model) {
        BizNomalEnums bizNomalEnums = bizNomalEnumsExtService.queryById(bizNomalEnumsId);
        model.addAttribute("item",bizNomalEnums);
        LogObjectHolder.me().set(bizNomalEnums);
        return PREFIX + "bizNomalEnums_detail.html";
    }
    /**
     * 获取类型设置列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(BizNomalEnumsListParam bizNomalEnumsParam) {
        return bizNomalEnumsService.findPageBySpec(bizNomalEnumsParam);
    }

    /**
     * 新增类型设置
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/bizNomalEnums/bizNomalEnums_add")
    @ResponseBody
    public ResponseData add(BizNomalEnums bizNomalEnums) {
         bizNomalEnumsExtService.save(bizNomalEnums);
         return ResponseData.success();
    }

    /**
     * 删除类型设置
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  bizNomalEnumsId) {
        bizNomalEnumsExtService.removeById(bizNomalEnumsId);
         return ResponseData.success();
    }

    /**
     * 修改类型设置
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizNomalEnums/bizNomalEnums_update")
    @ResponseBody
    public ResponseData update(BizNomalEnums bizNomalEnums) {
        bizNomalEnumsExtService.updateById(bizNomalEnums);
        return ResponseData.success();
    }

    /**
     * 类型设置详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  bizNomalEnumsId) {
       BizNomalEnums detail = bizNomalEnumsExtService.queryById(bizNomalEnumsId);
       return ResponseData.success(detail);
    }
}
