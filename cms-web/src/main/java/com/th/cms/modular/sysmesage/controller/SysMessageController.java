package com.th.cms.modular.sysmesage.controller;


import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.modular.sysmesage.dto.*;
import com.th.cms.modular.sysmesage.service.ISysMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 系统消息主表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@RestController
@RequestMapping("/sysMessage")
public class SysMessageController {
    @Autowired
    private ISysMessageService sysMessageService;
    /**
     * 获取所有枚举
     *
     * @return 结果
     */
    @RequestMapping(value = "/enums")
    public ResponseData<MessageEnumDTO> enums() {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.enums(user));
    }
    /**
     * 新增
     *
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping(value = "/add")
    public ResponseData<Long> add(@RequestBody @Valid SysMessageAddRequestDTO requestDTO) throws Exception {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.add(requestDTO, user));
    }

    /**
     * 添加接收人
     *
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping(value = "/addUser")
    public ResponseData<Long> addUser(@RequestBody SysMessageAddUserRequestDTO requestDTO) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }

        return ResponseData.success(sysMessageService.addUser(requestDTO, user));
    }

    /**
     * 删除接收人
     *
     * @param id 参数
     * @return 结果
     */
    @RequestMapping(value = "/deleteUser")
    public ResponseData<Long> deleteUser(@RequestParam("id") Long id) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.deleteUser(id));
    }

    /**
     * 添加接收部门
     *
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping(value = "/addDept")
    public ResponseData<Long> addDept(@RequestBody SysMessageAddDeptRequestDTO requestDTO) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.addDept(requestDTO, user));
    }

    /**
     * 删除接收部门
     *
     * @param id 参数
     * @return 结果
     */
    @RequestMapping(value = "/deleteDept")
    public ResponseData<Long> deleteDept(@RequestParam("id") Long id) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }

        return ResponseData.success(sysMessageService.deleteDept(id, user));
    }

    /**
     * 添加接收角色
     *
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping(value = "/addRole")
    public ResponseData<Long> addRole(@RequestBody SysMessageAddRoleRequestDTO requestDTO) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.addRole(requestDTO,user));
    }

    /**
     * 删除接收角色
     *
     * @param id 参数
     * @return 结果
     */
    @RequestMapping(value = "/deleteRole")
    public ResponseData<Long> deleteRole(@RequestParam("id") Long id) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }


        return ResponseData.success(sysMessageService.deleteRole(id,user));
    }

    /**
     * 删除
     *
     * @param messageId 主键
     * @return 删除结果
     */
    @RequestMapping(value = "/delete")
    public ResponseData<Boolean> delete(@RequestParam("messageId") Long messageId) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.deleteMessage(messageId,user));
    }

    /**
     * 启用关闭
     *
     * @param messageId 主键
     * @param status       状态 1 启用 0 关闭
     * @return 修改结果
     */
    @RequestMapping(value = "/toggle")
    public ResponseData<Boolean> toggle(@RequestParam("messageId") Long messageId, @RequestParam("status") Integer status) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.toggleStatus(messageId, status,user));
    }



    /**
     * 查询
     */
    @RequestMapping(value = "/list")
    public LayuiPageInfo<SysMessageListDTO> list(@RequestBody SysMessageListRequestDTO requestDTO) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return sysMessageService.listByPage(requestDTO,user);
    }

    /**
     * 详情
     */
    @RequestMapping(value = "/detail")
    public ResponseData<SysMessageDetailDTO> detail(@RequestParam("messageId") Long messageId) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.detail(messageId,user));
    }

    /**
     * 测试模板
     */
    @RequestMapping(value = "/testTpl")
    public ResponseData<String> testTpl(@RequestBody TestTplRequestDTO requestDTO) throws Exception {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageService.testTpl(requestDTO));
    }
}
