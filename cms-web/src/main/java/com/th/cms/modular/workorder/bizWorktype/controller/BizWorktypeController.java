package com.th.cms.modular.workorder.bizWorktype.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.workorder.bizWorktype.model.BizWorktype;
import com.th.cms.modular.workorder.bizWorktype.service.BizWorktypeService;
import com.th.cms.modular.workorder.bizWorktype.service.BizWorktypeExtService;
import com.th.cms.modular.workorder.bizWorktype.model.reqparam.BizWorktypeListParam;

/**
 * 工单分类控制器
 *
 * <AUTHOR>
 * @Date 2025-03-19 19:57:18
 */
@Controller
@RequestMapping("/bizWorktype")
public class BizWorktypeController extends BaseController {

    private String PREFIX = "/modular/bizWorktype/bizWorktype/";
    @Autowired
    BizWorktypeExtService bizWorktypeExtService;
    @Autowired
    private BizWorktypeService bizWorktypeService;

    /**
     * 跳转到工单分类首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizWorktype.html";
    }

    /**
     * 跳转到添加工单分类
     */
    @Permission
    @RequestMapping("/bizWorktype_add")
    public String bizWorktypeAdd() {
        return PREFIX + "bizWorktype_add.html";
    }

    /**
     * 跳转到修改工单分类
     */
    @Permission
    @RequestMapping("/bizWorktype_update")
    public String bizWorktypeUpdate(@RequestParam  Integer bizWorktypeId, Model model) {
        BizWorktype bizWorktype = bizWorktypeExtService.queryById(bizWorktypeId);
        model.addAttribute("item",bizWorktype);
        LogObjectHolder.me().set(bizWorktype);
        return PREFIX + "bizWorktype_edit.html";
    }
    @RequestMapping("/bizWorktype_detail")
    public String bizWorktypeDetail(@RequestParam Integer bizWorktypeId, Model model) {
        BizWorktype bizWorktype = bizWorktypeExtService.queryById(bizWorktypeId);
        model.addAttribute("item",bizWorktype);
        LogObjectHolder.me().set(bizWorktype);
        return PREFIX + "bizWorktype_detail.html";
    }
    /**
     * 获取工单分类列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(BizWorktypeListParam bizWorktypeParam) {
        return bizWorktypeService.findPageBySpec(bizWorktypeParam);
    }

    /**
     * 新增工单分类
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/bizWorktype/bizWorktype_add")
    @ResponseBody
    public ResponseData add(BizWorktype bizWorktype) {
         bizWorktypeExtService.save(bizWorktype);
         return ResponseData.success();
    }

    /**
     * 删除工单分类
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  bizWorktypeId) {
        bizWorktypeExtService.removeById(bizWorktypeId);
         return ResponseData.success();
    }

    /**
     * 修改工单分类
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizWorktype/bizWorktype_update")
    @ResponseBody
    public ResponseData update(BizWorktype bizWorktype) {
        bizWorktypeExtService.updateById(bizWorktype);
        return ResponseData.success();
    }

    /**
     * 工单分类详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  bizWorktypeId) {
       BizWorktype detail = bizWorktypeExtService.queryById(bizWorktypeId);
       return ResponseData.success(detail);
    }
}
