package com.th.cms.modular.sysmesage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
/**
 * <p>
 * 系统消息主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息名称
     */
    private String messageName;

    /**
     * 接口标识符，如 order.create
     */
    private String interfaceCode;

    /**
     * 消息类型（通知、告警等）
     */
    private Integer messageType;

    /**
     * 紧急程度 high/medium/low
     */
    private Integer urgencyLevel;



    /**
     * 状态 1 启用 0 不可用
     */
    private Integer status;



    /**
     * 推送类型 1 immediate/2 timed
     */
    private Integer pushType;

    /**
     * 定时发送周几
     */
    private Integer dayOfWeek;
    /**
     * 推送时间
     */
    private Date pushTime;
    /**
     * 接收者类型 0 无、1 user/2 dept/3 role
     */
    private Integer receiverType;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新人ID
     */
    private Long updateId;

    /**
     * 最后更新人姓名
     */
    private String updateName;

    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 删除标识 0 正常 1 删除
     */
    private Integer deleted;

}
