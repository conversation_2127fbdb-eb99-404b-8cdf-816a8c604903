package com.th.cms.modular.customer.bizCustomerIm.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.customer.bizCustomerIm.dao.BizCustomerImMapper;
import com.th.cms.modular.customer.bizCustomerIm.model.BizCustomerIm;
import com.th.cms.modular.customer.bizCustomerIm.model.ImFriendDTO;
import com.th.cms.modular.customer.bizCustomerIm.model.reqparam.BizCustomerImListParam;
import com.th.cms.modular.im.YxImService;
import com.th.cms.modular.oss.util.OssConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizCustomerImService extends ServiceImpl<BizCustomerImMapper, BizCustomerIm> implements IService<BizCustomerIm> {

    @Resource
    private YxImService yxImService;

    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizCustomerImListParam param) {
        QueryWrapper<BizCustomerIm> objectQueryWrapper = new QueryWrapper<>();
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;

    }


    public BizCustomerIm getByByImToken(String imToken) {
        QueryWrapper<BizCustomerIm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("im_token", imToken);
        return baseMapper.selectOne(queryWrapper);
    }

    public BizCustomerIm getByByUserId(Long userId) {
        QueryWrapper<BizCustomerIm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return baseMapper.selectOne(queryWrapper);
    }

    public List<BizCustomerIm> queryByAvts(BizCustomerImListParam param) {
        QueryWrapper<BizCustomerIm> objectQueryWrapper = new QueryWrapper<>();
        List<BizCustomerIm> imList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCustomerAccids())) {
            List<String> dlist = Arrays.asList(param.getCustomerAccids().split(","));
            objectQueryWrapper.lambda().in(BizCustomerIm::getCustomerAccid, dlist);
            imList = list(objectQueryWrapper);
            for (BizCustomerIm bizCustomerIm : imList) {
                String imIconPic = bizCustomerIm.getImIconPic();
                bizCustomerIm.setImIconPic(OssConfig.hostPre + "" + imIconPic);
            }
            //https://mingyuehao.oss-cn-beijing.aliyuncs.com/
        }
        return imList;

    }

    public void addEveryFriends(BizCustomerIm imi) {

        QueryWrapper<BizCustomerIm> queryWrapper = new QueryWrapper<>();
        List<BizCustomerIm> bizCustomerIms = baseMapper.selectList(queryWrapper);

        bizCustomerIms.forEach(imj -> {

            JSONObject jsonObject = yxImService.addFriends(ImFriendDTO.builder()
                    .account_id(imi.getCustomerAccid())
                    .friend_account_id(imj.getCustomerAccid())
                    .build());
        });
    }


    public BizCustomerIm initFriends() {

        QueryWrapper<BizCustomerIm> queryWrapper = new QueryWrapper<>();
        List<BizCustomerIm> bizCustomerIms = baseMapper.selectList(queryWrapper);

        for (int i = 0; i < bizCustomerIms.size(); i++) {

            BizCustomerIm imi = bizCustomerIms.get(i);

            int startIdx = i + 1;
            for (int j = startIdx; j < bizCustomerIms.size(); j++) {

                BizCustomerIm imj = bizCustomerIms.get(j);

                JSONObject jsonObject = yxImService.addFriends(ImFriendDTO.builder()
                        .account_id(imi.getCustomerAccid())
                        .friend_account_id(imj.getCustomerAccid())
                        .build());

//                JSONObject jsonObject = yxImService.removeFriends(ImFriendDTO.builder()
//                        .account_id(imi.getCustomerAccid())
//                        .friend_account_id(imj.getCustomerAccid())
//                        .build());
            }
        }

        return new BizCustomerIm();
    }

    public void removeEveryFriends(Integer bizCustomerImId) {

        BizCustomerIm imi = baseMapper.selectById(bizCustomerImId);

        QueryWrapper<BizCustomerIm> queryWrapper = new QueryWrapper<>();
        List<BizCustomerIm> bizCustomerIms = baseMapper.selectList(queryWrapper);
        bizCustomerIms.forEach(imj -> {

            JSONObject jsonObject = yxImService.removeFriends(ImFriendDTO.builder()
                    .account_id(imi.getCustomerAccid())
                    .friend_account_id(imj.getCustomerAccid())
                    .build());

        });


    }


}
