package com.th.cms.modular.wf.wfApprovalRequest.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.wf.wfApprovalRequest.model.WfApprovalRequest;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestService;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestExtService;
import com.th.cms.modular.wf.wfApprovalRequest.model.reqparam.WfApprovalRequestListParam;

/**
 * 审批请求控制器
 *
 * <AUTHOR>
 * @Date 2025-03-27 15:32:40
 */
@Controller
@RequestMapping("/wfApprovalRequest")
public class WfApprovalRequestController extends BaseController {

    private String PREFIX = "/modular/wfApprovalRequest/wfApprovalRequest/";
    @Autowired
    WfApprovalRequestExtService wfApprovalRequestExtService;
    @Autowired
    private WfApprovalRequestService wfApprovalRequestService;

    /**
     * 跳转到审批请求首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "wfApprovalRequest.html";
    }

    /**
     * 跳转到添加审批请求
     */
    @Permission
    @RequestMapping("/wfApprovalRequest_add")
    public String wfApprovalRequestAdd() {
        return PREFIX + "wfApprovalRequest_add.html";
    }

    /**
     * 跳转到修改审批请求
     */
    @Permission
    @RequestMapping("/wfApprovalRequest_update")
    public String wfApprovalRequestUpdate(@RequestParam  Integer wfApprovalRequestId, Model model) {
        WfApprovalRequest wfApprovalRequest = wfApprovalRequestExtService.queryById(wfApprovalRequestId);
        model.addAttribute("item",wfApprovalRequest);
        LogObjectHolder.me().set(wfApprovalRequest);
        return PREFIX + "wfApprovalRequest_edit.html";
    }
    @RequestMapping("/wfApprovalRequest_detail")
    public String wfApprovalRequestDetail(@RequestParam Integer wfApprovalRequestId, Model model) {
        WfApprovalRequest wfApprovalRequest = wfApprovalRequestExtService.queryById(wfApprovalRequestId);
        model.addAttribute("item",wfApprovalRequest);
        LogObjectHolder.me().set(wfApprovalRequest);
        return PREFIX + "wfApprovalRequest_detail.html";
    }
    /**
     * 获取审批请求列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(WfApprovalRequestListParam wfApprovalRequestParam) {
        return wfApprovalRequestService.findPageBySpec(wfApprovalRequestParam);
    }

    /**
     * 新增审批请求
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/wfApprovalRequest/wfApprovalRequest_add")
    @ResponseBody
    public ResponseData add(WfApprovalRequest wfApprovalRequest) {
         wfApprovalRequestExtService.save(wfApprovalRequest);
         return ResponseData.success();
    }

    /**
     * 删除审批请求
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  wfApprovalRequestId) {
        wfApprovalRequestExtService.removeById(wfApprovalRequestId);
         return ResponseData.success();
    }

    /**
     * 修改审批请求
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/wfApprovalRequest/wfApprovalRequest_update")
    @ResponseBody
    public ResponseData update(WfApprovalRequest wfApprovalRequest) {
        wfApprovalRequestExtService.updateById(wfApprovalRequest);
        return ResponseData.success();
    }

    /**
     * 审批请求详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  wfApprovalRequestId) {
       WfApprovalRequest detail = wfApprovalRequestExtService.queryById(wfApprovalRequestId);
       return ResponseData.success(detail);
    }
}
