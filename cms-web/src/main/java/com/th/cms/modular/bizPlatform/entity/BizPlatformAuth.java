package com.th.cms.modular.bizPlatform.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizPlatformAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台名称
     */
    private String platName;

    /**
     * 平台图标
     */
    private String iconPic;

    /**
     * 简称
     */
    private String jiancheng;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 状态
     */
    private Integer status;

}
