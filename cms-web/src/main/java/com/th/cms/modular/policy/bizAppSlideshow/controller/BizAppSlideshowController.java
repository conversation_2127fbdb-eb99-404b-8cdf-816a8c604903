package com.th.cms.modular.policy.bizAppSlideshow.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.policy.bizAppSlideshow.model.BizAppSlideshow;
import com.th.cms.modular.policy.bizAppSlideshow.service.BizAppSlideshowService;
import com.th.cms.modular.policy.bizAppSlideshow.service.BizAppSlideshowExtService;
import com.th.cms.modular.policy.bizAppSlideshow.model.reqparam.BizAppSlideshowListParam;

/**
 * 轮播图管理控制器
 *
 * <AUTHOR>
 * @Date 2025-03-21 18:33:17
 */
@Controller
@RequestMapping("/bizAppSlideshow")
public class BizAppSlideshowController extends BaseController {

    private String PREFIX = "/modular/bizAppSlideshow/bizAppSlideshow/";
    @Autowired
    BizAppSlideshowExtService bizAppSlideshowExtService;
    @Autowired
    private BizAppSlideshowService bizAppSlideshowService;

    /**
     * 跳转到轮播图管理首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizAppSlideshow.html";
    }

    /**
     * 跳转到添加轮播图管理
     */
    @Permission
    @RequestMapping("/bizAppSlideshow_add")
    public String bizAppSlideshowAdd() {
        return PREFIX + "bizAppSlideshow_add.html";
    }

    /**
     * 跳转到修改轮播图管理
     */
    @Permission
    @RequestMapping("/bizAppSlideshow_update")
    public String bizAppSlideshowUpdate(@RequestParam  Integer bizAppSlideshowId, Model model) {
        BizAppSlideshow bizAppSlideshow = bizAppSlideshowExtService.queryById(bizAppSlideshowId);
        model.addAttribute("item",bizAppSlideshow);
        LogObjectHolder.me().set(bizAppSlideshow);
        return PREFIX + "bizAppSlideshow_edit.html";
    }
    @RequestMapping("/bizAppSlideshow_detail")
    public String bizAppSlideshowDetail(@RequestParam Integer bizAppSlideshowId, Model model) {
        BizAppSlideshow bizAppSlideshow = bizAppSlideshowExtService.queryById(bizAppSlideshowId);
        model.addAttribute("item",bizAppSlideshow);
        LogObjectHolder.me().set(bizAppSlideshow);
        return PREFIX + "bizAppSlideshow_detail.html";
    }
    /**
     * 获取轮播图管理列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(BizAppSlideshowListParam bizAppSlideshowParam) {
        return bizAppSlideshowService.findPageBySpec(bizAppSlideshowParam);
    }

    /**
     * 新增轮播图管理
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/bizAppSlideshow/bizAppSlideshow_add")
    @ResponseBody
    public ResponseData add(BizAppSlideshow bizAppSlideshow) {
         bizAppSlideshowExtService.save(bizAppSlideshow);
         return ResponseData.success();
    }

    /**
     * 删除轮播图管理
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  bizAppSlideshowId) {
        bizAppSlideshowExtService.removeById(bizAppSlideshowId);
         return ResponseData.success();
    }

    /**
     * 修改轮播图管理
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizAppSlideshow/bizAppSlideshow_update")
    @ResponseBody
    public ResponseData update(BizAppSlideshow bizAppSlideshow) {
        bizAppSlideshowExtService.updateById(bizAppSlideshow);
        return ResponseData.success();
    }

    /**
     * 轮播图管理详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  bizAppSlideshowId) {
       BizAppSlideshow detail = bizAppSlideshowExtService.queryById(bizAppSlideshowId);
       return ResponseData.success(detail);
    }
}
