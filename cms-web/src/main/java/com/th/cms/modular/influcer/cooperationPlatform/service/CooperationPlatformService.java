package com.th.cms.modular.influcer.cooperationPlatform.service;

import cn.hutool.core.bean.BeanUtil;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.core.dto.FrontPageResult;
import com.th.cms.core.exceptions.BusiException;
import com.th.cms.core.util.CollectionUtils;
import com.th.cms.core.util.OptionalUtil;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.bizPlatform.convert.AppPlatformConvert;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAppConfig;
import com.th.cms.modular.bizPlatform.mapper.BizPlatformAppConfigMapper;
import com.th.cms.modular.influcer.bizPlatform.consts.BizPlatLevel;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatformIntroduction;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatformRelatedAnnouncements;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatformTrackStandard;
import com.th.cms.modular.influcer.bizPlatform.mapper.BizPlatformIntroductionMapper;
import com.th.cms.modular.influcer.bizPlatform.mapper.BizPlatformMapper;
import com.th.cms.modular.influcer.bizPlatform.mapper.BizPlatformRelatedAnnouncementsMapper;
import com.th.cms.modular.influcer.bizPlatform.mapper.BizPlatformTrackStandardMapper;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformService;
import com.th.cms.modular.influcer.cooperationPlatform.dto.*;
import com.th.cms.modular.settle.settleAccount.consts.TopDataSource;
import com.th.cms.modular.settle.settleAccount.model.BizInflucerTop;
import com.th.cms.modular.settle.settleAccount.service.BizInflucerTopService;
import com.th.cms.util.UserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 合作平台Service
 *
 * <AUTHOR>
 */

@Service
public class CooperationPlatformService {

    @Autowired
    BizPlatformService bizPlatformService;
    @Autowired
    BizPlatformMapper bizPlatformMapper;
    @Autowired
    BizInflucerTopService bizInflucerTopService;
    @Autowired
    BizPlatformIntroductionMapper bizPlatformIntroductionMapper;
    @Autowired
    BizPlatformTrackStandardMapper bizPlatformTrackStandardMapper;
    @Autowired
    BizPlatformRelatedAnnouncementsMapper bizPlatformRelatedAnnouncementsMapper;
    @Autowired
    private BizPlatformAppConfigMapper appConfigMapper;

    public FrontPageResult<AppPlatformDTO> list(CooperationPlatformListParamDTO paramDTO) {
        Page<AppPlatformDTO> result = appConfigMapper.listByPage(new Page<>(paramDTO.getPage(), paramDTO.getLimit()), paramDTO);
        return FrontPageResult.convertPage(result, AppPlatformConvert::convertAppPlatformDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<BizInflucerTop> createTop(CooperationPlatformTopInfoDTO topInfoDTO) {
        BizPlatform bizPlatform = queryBizPlatform(topInfoDTO.getPlatId());
        BizInflucerTop bizInflucerTop = BeanUtil.toBean(topInfoDTO, BizInflucerTop.class);
        ShiroUtils.setAddAuthInfo(bizInflucerTop);
        bizInflucerTop.setPlatName(bizPlatform.getPlatName());
        bizInflucerTop.setDataSource(TopDataSource.BY_HAND.getCode());
        bizInflucerTopService.save(bizInflucerTop);
        return ResponseData.success(bizInflucerTop);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<BizInflucerTop> updateTop(CooperationPlatformTopInfoDTO topInfoDTO) {
        if (topInfoDTO.getId() == null) {
            throw new BusiException("榜单ID不能为空");
        }
        BizInflucerTop bizInflucerTop = BeanUtil.toBean(topInfoDTO, BizInflucerTop.class);
        ShiroUtils.setUpdateAuthInfo(bizInflucerTop);
        bizInflucerTopService.updateById(bizInflucerTop);
        return ResponseData.success(bizInflucerTop);
    }

    public ResponseData<BizInflucerTop> topList(CooperationPlatformTopParamDTO topParamDTO) {
        List<BizInflucerTop> bizInflucerTopList = bizInflucerTopService.lambdaQuery()
                .eq(BizInflucerTop::getPlatId, topParamDTO.getPlatId())
                .eq(topParamDTO.getTopType() != null, BizInflucerTop::getTopType, topParamDTO.getTopType())
                .list();
        return ResponseData.success(bizInflucerTopList);
    }

    private BizPlatform queryBizPlatform(Integer id) {
        if (id == null) {
            throw new BusiException("平台ID不能为空");
        }
        BizPlatform bizPlatform = bizPlatformService.lambdaQuery().eq(BizPlatform::getId, id).one();
        if (bizPlatform == null) {
            throw new BusiException("平台信息不存在");
        }
        return bizPlatform;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<Boolean> setIntroduction(CooperationPlatformIntroductionInfoDTO introductionInfoDTO) {
        LambdaQueryWrapper<BizPlatformIntroduction> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizPlatformIntroduction::getPlatformId, introductionInfoDTO.getPlatformId());
        BizPlatformIntroduction bizPlatformIntroduction = bizPlatformIntroductionMapper.selectOne(wrapper);
        if (bizPlatformIntroduction == null) {
            bizPlatformIntroduction = new BizPlatformIntroduction();
            bizPlatformIntroduction.setPlatformId(introductionInfoDTO.getPlatformId());
            bizPlatformIntroduction.setIntroduction(introductionInfoDTO.getIntroduction());
            bizPlatformIntroduction.setStatus(1);
            ShiroUtils.setAddAuthInfo(bizPlatformIntroduction);
            bizPlatformIntroductionMapper.insert(bizPlatformIntroduction);
        } else {
            BizPlatformIntroduction updateBean = new BizPlatformIntroduction();
            updateBean.setId(bizPlatformIntroduction.getId());
            updateBean.setIntroduction(introductionInfoDTO.getIntroduction());
            ShiroUtils.setUpdateAuthInfo(updateBean);
            bizPlatformIntroductionMapper.updateById(updateBean);
        }
        return ResponseData.success(true);
    }

    public ResponseData<BizPlatformIntroduction> getIntroduction(Long platformId) {
        LambdaQueryWrapper<BizPlatformIntroduction> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizPlatformIntroduction::getPlatformId, platformId);
        List<BizPlatformIntroduction> platformIntroductionList = bizPlatformIntroductionMapper.selectList(wrapper);
        BizPlatformIntroduction bizPlatformIntroduction = null;
        if (CollectionUtils.isNotEmpty(platformIntroductionList)) {
            bizPlatformIntroduction = platformIntroductionList.get(0);
        }
        return ResponseData.success(bizPlatformIntroduction);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<BizPlatformTrackStandard> createTrack(CooperationPlatformTrackInfoDTO trackInfoDTO) {
        BizPlatformTrackStandard bizPlatformTrackStandard = BeanUtil.toBean(trackInfoDTO, BizPlatformTrackStandard.class);
        ShiroUtils.setAddAuthInfo(bizPlatformTrackStandard);
        bizPlatformTrackStandard.setStatus(1);
        bizPlatformTrackStandardMapper.insert(bizPlatformTrackStandard);
        return ResponseData.success(bizPlatformTrackStandard);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<BizPlatformTrackStandard> updateTrack(CooperationPlatformTrackInfoDTO trackInfoDTO) {
        if (trackInfoDTO.getId() == null) {
            throw new BusiException("赛道ID不能为空");
        }
        BizPlatformTrackStandard bizPlatformTrackStandard = BeanUtil.toBean(trackInfoDTO, BizPlatformTrackStandard.class);
        ShiroUtils.setUpdateAuthInfo(bizPlatformTrackStandard);
        bizPlatformTrackStandardMapper.updateById(bizPlatformTrackStandard);
        return ResponseData.success(bizPlatformTrackStandard);
    }

    public ResponseData<List<BizPlatformTrackStandard>> trackList(Long platformId) {
        LambdaQueryWrapper<BizPlatformTrackStandard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizPlatformTrackStandard::getPlatformId, platformId);
        List<BizPlatformTrackStandard> bizPlatformTrackStandardList = bizPlatformTrackStandardMapper.selectList(wrapper);
        return ResponseData.success(bizPlatformTrackStandardList);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<Void> deleteTrack(Long id) {
        if (id == null) {
            throw new BusiException("赛道ID不能为空");
        }
        bizPlatformTrackStandardMapper.deleteById(id);
        return ResponseData.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<BizPlatformRelatedAnnouncements> createAnnouncement(CooperationPlatformAnnouncementInfoDTO announcementInfoDTO) {
        BizPlatformRelatedAnnouncements bizPlatformRelatedAnnouncements = BeanUtil.toBean(announcementInfoDTO, BizPlatformRelatedAnnouncements.class);
        ShiroUtils.setAddAuthInfo(bizPlatformRelatedAnnouncements);
        bizPlatformRelatedAnnouncements.setStatus(1);
        bizPlatformRelatedAnnouncementsMapper.insert(bizPlatformRelatedAnnouncements);
        return ResponseData.success(bizPlatformRelatedAnnouncements);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<BizPlatformRelatedAnnouncements> updateAnnouncement(CooperationPlatformAnnouncementInfoDTO announcementInfoDTO) {
        if (announcementInfoDTO.getId() == null) {
            throw new BusiException("公告ID不能为空");
        }
        BizPlatformRelatedAnnouncements bizPlatformRelatedAnnouncements = BeanUtil.toBean(announcementInfoDTO, BizPlatformRelatedAnnouncements.class);
        ShiroUtils.setUpdateAuthInfo(bizPlatformRelatedAnnouncements);
        bizPlatformRelatedAnnouncementsMapper.updateById(bizPlatformRelatedAnnouncements);
        return ResponseData.success(bizPlatformRelatedAnnouncements);
    }

    public ResponseData<List<BizPlatformRelatedAnnouncements>> announcementList(Long platformId) {
        LambdaQueryWrapper<BizPlatformRelatedAnnouncements> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizPlatformRelatedAnnouncements::getPlatformId, platformId);
        List<BizPlatformRelatedAnnouncements> bizPlatformRelatedAnnouncementsList = bizPlatformRelatedAnnouncementsMapper.selectList(wrapper);
        return ResponseData.success(bizPlatformRelatedAnnouncementsList);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData<Void> deleteAnnouncement(Long id) {
        if (id == null) {
            throw new BusiException("公告ID不能为空");
        }
        bizPlatformRelatedAnnouncementsMapper.deleteById(id);
        return ResponseData.success();
    }

    public Boolean toggle(Long platId, Integer status, UserDTO userNotNull) {
        if (platId == null) {
            throw new BusiException("ID不能为空");
        }
        BizPlatformAppConfig appConfig1 = queryByPlatId(platId);
        if (appConfig1 == null) {
            addPlatform(platId, userNotNull,status);
        }else{
            BizPlatformAppConfig appConfig = new BizPlatformAppConfig();
            appConfig.setId(appConfig1.getId());
            appConfig.setStatus(status);
            appConfig.setUpdateTime(new Date());
            appConfigMapper.updateById(appConfig);
        }

        return true;
    }

    public Boolean deleteAppPlatform(Long id) {
        appConfigMapper.deleteById(id);
        return true;
    }

    public List<BizPlatform> listOneLevelPlatforms() {
        return bizPlatformMapper.selectList(new LambdaQueryWrapper<BizPlatform>()
                .eq(BizPlatform::getLevels, BizPlatLevel.ONE.getCode()));
    }

    public List<BizPlatform> listTwoLevelPlatforms(Long platId, UserDTO userNotNull) {

        return bizPlatformMapper.selectList(new LambdaQueryWrapper<BizPlatform>()
                .eq(BizPlatform::getParentId, platId)
                .eq(BizPlatform::getLevels, BizPlatLevel.TWO.getCode()));
    }

    public boolean exists(Long platId) {
        return appConfigMapper.selectCount(new LambdaQueryWrapper<BizPlatformAppConfig>().eq(BizPlatformAppConfig::getPlatId, platId)) > 0;
    }
    public BizPlatformAppConfig queryByPlatId(Long platId) {
        return OptionalUtil.defaultList(appConfigMapper.selectList(new LambdaQueryWrapper<BizPlatformAppConfig>().eq(BizPlatformAppConfig::getPlatId, platId))).stream().findFirst().orElse(null);
    }
    public Boolean addPlatform(Long platId, UserDTO userNotNull, Integer status) {
        if (exists(platId)) {
            return true;
        }
        BizPlatformAppConfig appConfig = new BizPlatformAppConfig();
        appConfig.setPlatId(platId);
        appConfig.setStatus(status);
        appConfig.setCreateId(userNotNull.getUserId());
        appConfig.setCreateName(userNotNull.getName());
        appConfig.setCreateTime(new Date());
        appConfig.setUpdateId(userNotNull.getUserId());
        appConfig.setUpdateName(userNotNull.getName());
        appConfig.setUpdateTime(new Date());
        appConfigMapper.insert(appConfig);
        return true;
    }


}
