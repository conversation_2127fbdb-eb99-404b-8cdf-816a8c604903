package com.th.cms.modular.settle.settleProjects.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjectsField;
import com.th.cms.modular.settle.settleProjects.mapper.SettleProjectsFieldMapper;
import com.th.cms.modular.settle.settleProjects.service.ISettleProjectsFieldService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 项目灵活字段存储表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
@Primary
public class SettleProjectsFieldServiceImpl extends ServiceImpl<SettleProjectsFieldMapper, SettleProjectsField> implements ISettleProjectsFieldService {

}
