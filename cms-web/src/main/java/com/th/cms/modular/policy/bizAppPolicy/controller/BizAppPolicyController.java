package com.th.cms.modular.policy.bizAppPolicy.controller;

import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.shiro.ShiroUser;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.policy.bizAppPolicy.model.BizAppPolicy;
import com.th.cms.modular.policy.bizAppPolicy.service.BizAppPolicyService;
import com.th.cms.modular.policy.bizAppPolicy.service.BizAppPolicyExtService;
import com.th.cms.modular.policy.bizAppPolicy.model.reqparam.BizAppPolicyListParam;

/**
 * 政策管理控制器
 *
 * <AUTHOR>
 * @Date 2025-03-21 10:58:16
 */
@Controller
@RequestMapping("/bizAppPolicy")
public class BizAppPolicyController extends BaseController {

    private String PREFIX = "/modular/bizAppPolicy/bizAppPolicy/";
    @Autowired
    BizAppPolicyExtService bizAppPolicyExtService;
    @Autowired
    private BizAppPolicyService bizAppPolicyService;

    /**
     * 跳转到政策管理首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizAppPolicy.html";
    }

    /**
     * 跳转到添加政策管理
     */
    @Permission
    @RequestMapping("/bizAppPolicy_add")
    public String bizAppPolicyAdd() {
        return PREFIX + "bizAppPolicy_add.html";
    }

    /**
     * 跳转到修改政策管理
     */
    @Permission
    @RequestMapping("/bizAppPolicy_update")
    public String bizAppPolicyUpdate(@RequestParam Integer bizAppPolicyId, Model model) {
        BizAppPolicy bizAppPolicy = bizAppPolicyExtService.queryById(bizAppPolicyId);
        model.addAttribute("item", bizAppPolicy);
        LogObjectHolder.me().set(bizAppPolicy);
        return PREFIX + "bizAppPolicy_edit.html";
    }

    @RequestMapping("/bizAppPolicy_detail")
    public String bizAppPolicyDetail(@RequestParam Integer bizAppPolicyId, Model model) {
        BizAppPolicy bizAppPolicy = bizAppPolicyExtService.queryById(bizAppPolicyId);
        model.addAttribute("item", bizAppPolicy);
        LogObjectHolder.me().set(bizAppPolicy);
        return PREFIX + "bizAppPolicy_detail.html";
    }

    /**
     * 获取政策管理列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(BizAppPolicyListParam bizAppPolicyParam) {
        return bizAppPolicyService.findPageBySpec(bizAppPolicyParam);
    }

    /**
     * 新增政策管理
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/bizAppPolicy/bizAppPolicy_add")
    @ResponseBody
    public ResponseData add(BizAppPolicy bizAppPolicy) {

        ShiroUser user = ShiroKit.getUser();
        bizAppPolicy.setCreateId(user.getId());
        bizAppPolicy.setCreateName(user.getName());
        bizAppPolicyExtService.save(bizAppPolicy);
        return ResponseData.success();
    }

    /**
     * 删除政策管理
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer bizAppPolicyId) {
        bizAppPolicyService.removeById(bizAppPolicyId);
        return ResponseData.success();
    }

    /**
     * 修改政策管理
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizAppPolicy/bizAppPolicy_update")
    @ResponseBody
    public ResponseData update(BizAppPolicy bizAppPolicy) {
        bizAppPolicyExtService.updateById(bizAppPolicy);
        return ResponseData.success();
    }

    /**
     * 政策管理详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer bizAppPolicyId) {
        BizAppPolicy detail = bizAppPolicyExtService.queryById(bizAppPolicyId);
        return ResponseData.success(detail);
    }
}
