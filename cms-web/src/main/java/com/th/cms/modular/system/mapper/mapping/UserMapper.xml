<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.system.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.th.cms.modular.system.entity.User">
        <id column="user_id" property="userId" />
        <result column="avatar" property="avatar" />
        <result column="account" property="account" />
        <result column="password" property="password" />
        <result column="salt" property="salt" />
        <result column="name" property="name" />
        <result column="birthday" property="birthday" />
        <result column="sex" property="sex" />
        <result column="email" property="email" />
        <result column="phone" property="phone" />
        <result column="role_id" property="roleId" />
        <result column="dept_id" property="deptId" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id AS "userId", avatar AS "avatar", account AS "account", salt AS "salt", name AS "name", birthday AS "birthday", sex AS "sex", email AS "email", phone AS "phone", role_id AS "roleId", dept_id AS "deptId", status AS "status", create_time AS "createTime", create_user AS "createUser", update_time AS "updateTime", update_user AS "updateUser", version AS "version"
    </sql>

    <sql id="Base_Column_List_With_Pwd">
		 user_id AS "userId", avatar AS "avatar", account AS "account", salt AS "salt", password AS "password", name AS "name", birthday AS "birthday", sex AS "sex", email AS "email", phone AS "phone", role_id AS "roleId", dept_id AS "deptId", status AS "status", create_time AS "createTime", create_user AS "createUser", update_time AS "updateTime", update_user AS "updateUser", version AS "version"
	</sql>

    <select id="selectUsers" resultType="map">
        select
        <include refid="Base_Column_List"/>
        from sys_user
        where status != 'DELETED'
        <if test="name != null and name != ''">
            and (phone like CONCAT('%',#{name},'%')
            or account like CONCAT('%',#{name},'%')
            or name like CONCAT('%',#{name},'%'))
        </if>
        <if test="deptId != null and deptId != 0">
            and (dept_id = #{deptId} or dept_id in ( select dept_id from sys_dept where pids like CONCAT('%$[', #{deptId}, '$]%') escape '$' ))
        </if>
        <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
            and (create_time between CONCAT(#{beginTime},' 00:00:00') and CONCAT(#{endTime},' 23:59:59'))
        </if>
    </select>


    <select id="selectUsersNotCus" resultType="map">
        select
        <include refid="Base_Column_List"/>
        from sys_user
        where status != 'DELETED' and user_id not in (select user_id from biz_customer)
        <if test="name != null and name != ''">
            and (phone like CONCAT('%',#{name},'%')
            or account like CONCAT('%',#{name},'%')
            or name like CONCAT('%',#{name},'%'))
        </if>
        <if test="deptId != null and deptId != 0">
            and (dept_id = #{deptId} or dept_id in ( select dept_id from sys_dept where pids like CONCAT('%$[', #{deptId}, '$]%') escape '$' ))
        </if>
        <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
            and (create_time between CONCAT(#{beginTime},' 00:00:00') and CONCAT(#{endTime},' 23:59:59'))
        </if>
    </select>

    <update id="setStatus">
		update sys_user set status = #{status} where user_id = #{userId}
	</update>

    <update id="changePwd">
		update sys_user set password = #{pwd} where user_id = #{userId}
	</update>

    <update id="setRoles">
		update sys_user set role_id = #{roleIds} where user_id =
		#{userId}
	</update>

    <select id="getByAccount" resultType="com.th.cms.modular.system.entity.User">
        select
        <include refid="Base_Column_List_With_Pwd"/>
        from sys_user where account = #{account} and status != 'DELETED'
    </select>

</mapper>
