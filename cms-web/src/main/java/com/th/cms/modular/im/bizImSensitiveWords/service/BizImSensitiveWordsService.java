package com.th.cms.modular.im.bizImSensitiveWords.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.im.bizImSensitiveWords.dao.BizImSensitiveWordsMapper;
import com.th.cms.modular.im.bizImSensitiveWords.model.BizImSensitiveWords;
import com.th.cms.modular.im.bizImSensitiveWords.model.reqparam.BizImSensitiveWordsListParam;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizImSensitiveWordsService extends ServiceImpl<BizImSensitiveWordsMapper, BizImSensitiveWords> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizImSensitiveWordsListParam param) {

        QueryWrapper<BizImSensitiveWords> objectQueryWrapper = new QueryWrapper<>();

        if (!StringUtils.isEmpty(param.getBizImSensitiveWords())) {
            objectQueryWrapper.lambda().like(BizImSensitiveWords::getSensitiveWords, "%" + param.getBizImSensitiveWords() + "%");
        }
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;

    }
}
