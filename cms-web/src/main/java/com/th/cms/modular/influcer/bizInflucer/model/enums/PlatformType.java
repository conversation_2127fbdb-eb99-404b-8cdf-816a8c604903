package com.th.cms.modular.influcer.bizInflucer.model.enums;

public enum PlatformType {

    DY(1L, "抖音", "dy", true, false,
            "http://mingyuehao.oss-cn-beijing.aliyuncs.com/img/dy.png"),
    KS(2L, "快手", "ks", true, false,
            "http://mingyuehao.oss-cn-beijing.aliyuncs.com/img/ks.png"),
    XHS(3L, "小红书", "xhs", true, false,
            "http://mingyuehao.oss-cn-beijing.aliyuncs.com/img/xhs.png"),
    BLBL(4L, "B站", "bl", true, false,
            "http://mingyuehao.oss-cn-beijing.aliyuncs.com/img/bili.png"),
    SPH(5L, "视频号", "sph", false, true,
            "http://mingyuehao.oss-cn-beijing.aliyuncs.com/img/sph.png")
    ;

    public Long id;
    public String name;
    public String shortName;
    public Boolean affirmEnable;
    public Boolean videoEnable;
    public String image;

    PlatformType(Long id, String name, String shortName, Boolean affirmEnable, Boolean videoEnable, String image) {
        this.id = id;
        this.name = name;
        this.shortName = shortName;
        this.affirmEnable = affirmEnable;
        this.videoEnable = videoEnable;
        this.image = image;
    }

    public static PlatformType getType(Long id) {

        PlatformType[] platformTypes = PlatformType.values();

        for (PlatformType pt : platformTypes) {
            if (pt.id.equals(id)) {
                return pt;
            }
        }

        return null;
    }

    public static void main(String[] args) {
        for (PlatformType value : values()) {
            String sql = String.format("INSERT INTO mingyuehao.biz_platform_auth(plat_name, icon_pic, jiancheng,  status)VALUES( '%s', '%s', '%s', %s);",value.id,  value.image, value.shortName,1);
            System.out.println(sql);
        }
    }
}
