package com.th.cms.modular.bizPlatform.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizPlatform implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 平台名称
     */
    private String platName;

    /**
     * 平台图标
     */
    private String iconPic;

    /**
     * 父级ID
     */
    private Integer parentId;

    /**
     * 层级
     */
    private Integer levels;

    /**
     * 简称
     */
    private String jiancheng;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
