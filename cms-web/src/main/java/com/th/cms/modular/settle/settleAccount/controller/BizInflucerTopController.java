package com.th.cms.modular.settle.settleAccount.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.settle.settleAccount.model.BizInflucerTop;
import com.th.cms.modular.settle.settleAccount.service.BizInflucerTopService;
import com.th.cms.modular.settle.settleAccount.service.BizInflucerTopExtService;
import com.th.cms.modular.settle.settleAccount.model.reqparam.BizInflucerTopListParam;

/**
 * 达人排行控制器
 *
 * <AUTHOR>
 * @Date 2025-04-15 19:19:34
 */
@Controller
@RequestMapping("/bizInflucerTop")
public class BizInflucerTopController extends BaseController {

    private String PREFIX = "/modular/settleAccount/bizInflucerTop/";
    @Autowired
    BizInflucerTopExtService bizInflucerTopExtService;
    @Autowired
    private BizInflucerTopService bizInflucerTopService;

    /**
     * 跳转到达人排行首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizInflucerTop.html";
    }

    /**
     * 跳转到添加达人排行
     */
    @Permission
    @RequestMapping("/bizInflucerTop_add")
    public String bizInflucerTopAdd() {
        return PREFIX + "bizInflucerTop_add.html";
    }

    /**
     * 跳转到修改达人排行
     */
    @Permission
    @RequestMapping("/bizInflucerTop_update")
    public String bizInflucerTopUpdate(@RequestParam  Integer bizInflucerTopId, Model model) {
        BizInflucerTop bizInflucerTop = bizInflucerTopExtService.queryById(bizInflucerTopId);
        model.addAttribute("item",bizInflucerTop);
        LogObjectHolder.me().set(bizInflucerTop);
        return PREFIX + "bizInflucerTop_edit.html";
    }
    @RequestMapping("/bizInflucerTop_detail")
    public String bizInflucerTopDetail(@RequestParam Integer bizInflucerTopId, Model model) {
        BizInflucerTop bizInflucerTop = bizInflucerTopExtService.queryById(bizInflucerTopId);
        model.addAttribute("item",bizInflucerTop);
        LogObjectHolder.me().set(bizInflucerTop);
        return PREFIX + "bizInflucerTop_detail.html";
    }
    /**
     * 获取达人排行列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(BizInflucerTopListParam bizInflucerTopParam) {
        return bizInflucerTopService.findPageBySpec(bizInflucerTopParam);
    }

    /**
     * 新增达人排行
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/bizInflucerTop/bizInflucerTop_add")
    @ResponseBody
    public ResponseData add(BizInflucerTop bizInflucerTop) {
         bizInflucerTopExtService.save(bizInflucerTop);
         return ResponseData.success();
    }

    /**
     * 删除达人排行
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  bizInflucerTopId) {
        bizInflucerTopExtService.removeById(bizInflucerTopId);
         return ResponseData.success();
    }

    /**
     * 修改达人排行
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizInflucerTop/bizInflucerTop_update")
    @ResponseBody
    public ResponseData update(BizInflucerTop bizInflucerTop) {
        bizInflucerTopExtService.updateById(bizInflucerTop);
        return ResponseData.success();
    }

    /**
     * 达人排行详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  bizInflucerTopId) {
       BizInflucerTop detail = bizInflucerTopExtService.queryById(bizInflucerTopId);
       return ResponseData.success(detail);
    }
}
