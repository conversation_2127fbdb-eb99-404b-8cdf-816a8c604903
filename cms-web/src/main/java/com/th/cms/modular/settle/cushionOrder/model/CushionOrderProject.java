package com.th.cms.modular.settle.cushionOrder.model;

import com.th.cms.modular.settle.cushionBatch.model.CushionBatch;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.wf.wfApprovalRecord.model.WfApprovalRecord;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年04月03日 16:13
 */
@Data
public class CushionOrderProject {
    private CushionOrder cushionOrder;
    private SettleProjects settleProjects;
    private CushionBatch cushionBatch;

    private String cushionBatchStatusName;

    private Integer showLb = 2;
    private Integer showCw = 2;
    private List<WfApprovalRecord> wfApprovalRecordList;//审批信息

    //业务可以二次结算审批
    private boolean businessConfirmApproval;
    //财务可以二次结算审批
    private boolean financeConfirmApproval;
}
