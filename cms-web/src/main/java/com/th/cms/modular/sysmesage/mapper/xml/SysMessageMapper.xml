<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.sysmesage.mapper.SysMessageMapper">

    <select id="listByPage" resultType="com.th.cms.modular.sysmesage.entity.SysMessage">
        SELECT id, message_name, interface_code, message_type, urgency_level, push_time, status, push_type,
       receiver_type, create_id, create_name, create_time, update_id,
        update_name, update_time, deleted,day_of_week
        FROM mingyuehao.sys_message t
        where 1=1 and deleted = 0
        <if test="record.messageName!=null and record.messageName!=''">
            t.message_name like concat('%',#{record.messageName},'%')
        </if>
        <if test="record.receiveMethod!=null">
            t.id in(select message_id from sys_message_receiver_config smrc where smrc.receive_method
            =#{record.receiveMethod} and status=1)
        </if>
        <if test="record.tagId!=null">
            t.id in(select message_id from sys_message_tag_rel smtr where smtr.tag_id =#{record.tagId} and status=1)
        </if>
        <if test="record.status!=null">
            and t.status = #{record.status}
        </if>
        order by id desc
    </select>
</mapper>
