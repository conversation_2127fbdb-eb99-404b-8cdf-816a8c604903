package com.th.cms.modular.customer.bizCustomerIm.model;

import java.util.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="BizCustomerIm对象", description="")
public class BizCustomerIm implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    @TableField("id")
    private Long id;

    @ApiModelProperty(value = "客服ID")
    @TableField("customer_id")
    private Integer customerId;

    @ApiModelProperty(value = "名称")
    @TableField("customer_name")
    private String customerName;

    @ApiModelProperty(value = "accid")
    @TableField("customer_accid")
    private String customerAccid;

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "token")
    @TableField("im_token")
    private String imToken;

    @ApiModelProperty(value = "头像")
    @TableField("im_icon_pic")
    private String imIconPic;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ExcelField(title="id",dictType="", align=2, sort=0)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @ExcelField(title="客服ID",dictType="", align=2, sort=1)
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }
    @ExcelField(title="名称",dictType="", align=2, sort=2)
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    @ExcelField(title="accid",dictType="", align=2, sort=3)
    public String getCustomerAccid() {
        return customerAccid;
    }

    public void setCustomerAccid(String customerAccid) {
        this.customerAccid = customerAccid;
    }
    @ExcelField(title="用户ID",dictType="", align=2, sort=4)
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @ExcelField(title="token",dictType="", align=2, sort=5)
    public String getImToken() {
        return imToken;
    }

    public void setImToken(String imToken) {
        this.imToken = imToken;
    }
    @ExcelField(title="头像",dictType="", align=2, sort=6)
    public String getImIconPic() {
        return imIconPic;
    }

    public void setImIconPic(String imIconPic) {
        this.imIconPic = imIconPic;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=7)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=8)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BizCustomerIm{" +
        "id=" + id +
        ", customerId=" + customerId +
        ", customerName=" + customerName +
        ", customerAccid=" + customerAccid +
        ", userId=" + userId +
        ", imToken=" + imToken +
        ", imIconPic=" + imIconPic +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
