package com.th.cms.modular.settle.settleCore.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.th.cms.modular.settle.settleBatch.plat.pdd.CommonSettleFiledModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TaoBaoZhiBoDianFuSettleExcel {
    @ExcelProperty(value = "主播ID")
    private String id;
    
    @ExcelProperty(value = "主播昵称")
    private String name;

    @ExcelProperty(value = "预估奖励金额（元）")
    private BigDecimal amount;

    public CommonSettleFiledModel buildCommonSettleFiledModel() {
        CommonSettleFiledModel model = new CommonSettleFiledModel();
        model.setInfluencerId(id);
        model.setInfluencerNickname(name);
        model.setRebateAmount(amount);
        return model;
    }
}
