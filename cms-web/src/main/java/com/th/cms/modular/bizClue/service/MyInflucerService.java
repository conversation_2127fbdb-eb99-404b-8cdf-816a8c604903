package com.th.cms.modular.bizClue.service;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.th.cms.core.easyexcel.PageResult;
import com.th.cms.modular.bizClue.consts.MyInflucerIncomeRange;
import com.th.cms.modular.bizClue.dto.*;
import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import com.th.cms.modular.influcer.bizInflucer.model.enums.PlatformType;
import com.th.cms.modular.influcer.bizInflucer.service.BizInflucerService;
import com.th.cms.modular.influcer.bizInflucerPlatform.dao.BizInflucerPlatformMapper;
import com.th.cms.modular.influcer.bizInflucerPlatform.model.BizInflucerPlatform;
import com.th.cms.modular.influcer.bizInflucerProject.service.BizInflucerProjectService;
import com.th.cms.modular.settle.settleAccount.model.SettleAccount;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountLogService;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountService;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.service.UserService;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums.InflucerType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MyInflucerService {

    @Autowired
    private BizInflucerPlatformMapper bizInflucerPlatformMapper;

    @Autowired
    private BizInflucerProjectService bizInflucerProjectService;

    @Autowired
    private UserService userService;

    @Autowired
    private BizInflucerService bizInflucerService;

    @Autowired
    private SettleAccountService settleAccountService;

    @Autowired
    private SettleAccountLogService settleAccountLogService;

    public MyInflucerEnumDTO influcerEnums(MyInflucerListRequestDTO requestDTO) {
        MyInflucerEnumDTO influcerEnumDTO = new MyInflucerEnumDTO();
        List<MyInflucerEnumDTO.EnumDTO> platformTypeList = new ArrayList<>();
        for (PlatformType type : PlatformType.values()){
            MyInflucerEnumDTO.EnumDTO dto = new MyInflucerEnumDTO.EnumDTO();
            dto.setCode(type.id);
            dto.setName(type.name);
            platformTypeList.add(dto);
        }
        influcerEnumDTO.setCooperationPlatformList(platformTypeList);
        List<MyInflucerEnumDTO.EnumDTO> incomeRangeList = new ArrayList<>();
        for (MyInflucerIncomeRange range : MyInflucerIncomeRange.values()){
            MyInflucerEnumDTO.EnumDTO dto = new MyInflucerEnumDTO.EnumDTO();
            dto.setCode(Long.valueOf(range.getCode()));
            dto.setName(range.getName());
            incomeRangeList.add(dto);
        }
        influcerEnumDTO.setIncomeRangeList(incomeRangeList);
        User user = userService.getUserByContext();
        List<MyInflucerProjectDTO> projectList = bizInflucerPlatformMapper.getProjectByBizId(user.getUserId());
        List<MyInflucerEnumDTO.EnumDTO> projectEnumList = projectList.stream().map(x -> {
            MyInflucerEnumDTO.EnumDTO dto = new MyInflucerEnumDTO.EnumDTO();
            dto.setCode(x.getProjectId());
            dto.setName(x.getProjectName());
            return dto;
        }).collect(Collectors.toList());
        influcerEnumDTO.setCooperationProjectList(projectEnumList);
        List<MyInflucerEnumDTO.EnumDTO> influcerTypeList = new ArrayList<>();
        for (InflucerType type : InflucerType.values()){
            MyInflucerEnumDTO.EnumDTO dto = new MyInflucerEnumDTO.EnumDTO();
            dto.setCode(type.value.longValue());
            dto.setName(type.name);
            influcerTypeList.add(dto);
        }
        influcerEnumDTO.setInfluencerTypeList(influcerTypeList);
        List<MyInflucerEnumDTO.EnumDTO> businessPersonList = new ArrayList<>();
        List<User> userList = userService.getUsersByDeptId(user.getDeptId());
        userList.forEach(x -> {
            MyInflucerEnumDTO.EnumDTO dto = new MyInflucerEnumDTO.EnumDTO();
            dto.setCode(x.getUserId());
            dto.setName(x.getName());
            businessPersonList.add(dto);
        });
        influcerEnumDTO.setBusinessPersonList(businessPersonList);
        return influcerEnumDTO;
    }

    public PageResult<MyInflucerListDTO> list(MyInflucerListRequestDTO requestDTO) {
        requestDTO.setIncomeRange(requestDTO.getIncomeRange());
        Long userId = userService.getUserByContext().getUserId();
        Assert.notNull(userId, "用户未登录");
        requestDTO.setUserId(userId);
        PageHelper.startPage(requestDTO.getPage(), requestDTO.getLimit());
        List<MyInflucerListDTO> list = bizInflucerPlatformMapper.myInflucerList(requestDTO);
        PageInfo<MyInflucerListDTO> pageInfo = new PageInfo<>(list);
        list.forEach(x->{
            String projectIdListStr = x.getProjectIdList();
            if (StringUtils.isNotBlank(projectIdListStr)){
                String[] projectIdArr = projectIdListStr.split(",");
                List<Integer> ids = Arrays.stream(projectIdArr).map(Integer::parseInt).collect(Collectors.toList());
                List<MyInflucerProjectDTO> projectList = bizInflucerProjectService.getProjectByInflucerIdAndProjectIds(ids,x.getInflucerId());
                x.setProjectDTOList(projectList);

            }
            String platIdList = x.getPlatIdList();
            if (StringUtils.isNotBlank(platIdList)){
                String[] platIdArr = platIdList.split(",");
                List<Long> ids = Arrays.stream(platIdArr).map(Long::parseLong).collect(Collectors.toList());
                List<MyInflucerPlatformAuthDTO> platformList = bizInflucerPlatformMapper.getPlatformByInflucerIdAndPlatformIds(ids,x.getInflucerId());
                x.setPlatformAuthDTOList(platformList);
            }
        });

        PageResult<MyInflucerListDTO> pageResult =new PageResult<>();
        pageResult.setPageNum(requestDTO.getPage());
        pageResult.setPageSize(requestDTO.getLimit());
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setList(list);
        return pageResult;
    }

    public PageResult<MyInflucerListDTO> underList(MyInflucerListRequestDTO requestDTO) {
        List<Long> userIdList = userService.getUserIdsByDeptId(userService.getUserByContext().getDeptId());
        requestDTO.setUserIdList(userIdList);
        return list(requestDTO);
    }

    public MyInflucerListNumDTO nums(MyInflucerListRequestDTO requestDTO) {
        if (requestDTO.getSelf()){
            requestDTO.setUserId(userService.getUserByContext().getUserId());
        }else {
            List<Long> userIds = userService.getUserIdsByDeptId(userService.getUserByContext().getDeptId());
            requestDTO.setUserIdList(userIds);
        }
        return bizInflucerPlatformMapper.myInflucerNums(requestDTO);
    }

    public MyInflucerDetailDTO myInflucerDetail(MyInflucerDetailRequestDTO requestDTO) {
        BizInflucer influcer = bizInflucerService.lambdaQuery().eq(BizInflucer::getId, requestDTO.getInfluencerId()).one();
        MyInflucerDetailDTO dto = new MyInflucerDetailDTO();
        dto.setInflucerId(influcer.getId());
        dto.setNikeName(influcer.getNikeName());
        dto.setIcon(influcer.getIcon());
//        dto.setInflucerType(influcer.getInflucerType());
        dto.setLoginTel(influcer.getLoginTel());
        dto.setIsVerification(influcer.getIsVerification());
        SettleAccount account = settleAccountService.lambdaQuery().eq(SettleAccount::getUserId, influcer.getId()).one();
        dto.setIncomeAmount(account.getZongAmount());
        QueryWrapper<BizInflucerPlatform> bizInflucerQueryWrapper = new QueryWrapper<>();
        bizInflucerQueryWrapper.lambda().eq(BizInflucerPlatform::getInflucerId, influcer.getId());
        List<MyInflucerPlatformAuthDTO> platformByInflucerIdAndPlatformIds = bizInflucerPlatformMapper.getPlatformByInflucerIdAndPlatformIds(null, influcer.getId());
        dto.setPlatformAuthDTOList(platformByInflucerIdAndPlatformIds);
        List<MyInflucerCooperationPlatformDTO> bizInflucerPlatformMapper = bizInflucerProjectService.getPlatfromByInflucerId(influcer.getId());
        dto.setCooperationPlatformDTOList(bizInflucerPlatformMapper);
        dto.setTotalProject(platformByInflucerIdAndPlatformIds.size());
        return dto;
    }

    public PageResult<MyInflucerDetailIncomeDTO> incomes(MyInflucerDetailIncomeRequestDTO requestDTO) {
        PageHelper.startPage(requestDTO.getPage(), requestDTO.getLimit());
        List<MyInflucerDetailIncomeDTO> list = settleAccountLogService.myInflucerIncomePage(requestDTO);
        PageResult<MyInflucerDetailIncomeDTO> pageResult =new PageResult<>();
        pageResult.setPageNum(requestDTO.getPage());
        pageResult.setPageSize(requestDTO.getLimit());
        pageResult.setTotal(new PageInfo<>(list).getTotal());
        pageResult.setList(list);
        return pageResult;
    }

    public List<MyInflucerDetailIncomeDTO> incomesExport(MyInflucerDetailIncomeRequestDTO requestDTO) {
        return settleAccountLogService.myInflucerIncomePage(requestDTO);
    }

    public PageResult<MyInflucerDetailProjectDTO> projects(MyInflucerDetailProjectRequestDTO requestDTO) {
        PageHelper.startPage(requestDTO.getPage(), requestDTO.getLimit());
        List<MyInflucerDetailProjectDTO> list = bizInflucerProjectService.myInflucerDetailProjectsPage(requestDTO);
        PageResult<MyInflucerDetailProjectDTO> pageResult =new PageResult<>();
        pageResult.setTotal(new PageInfo<>(list).getTotal());
        pageResult.setPageNum(requestDTO.getPage());
        pageResult.setPageSize(requestDTO.getLimit());
        pageResult.setList(list);
        return pageResult;
    }


    public List<MyInflucerDetailProjectDTO> projectsExport(MyInflucerDetailProjectRequestDTO requestDTO) {
        return bizInflucerProjectService.myInflucerDetailProjectsPage(requestDTO);
    }

    public List<MyInflucerListDTO> listExport(MyInflucerListRequestDTO requestDTO) {
        return bizInflucerPlatformMapper.myInflucerExportList(requestDTO);
    }
}
