<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.system.mapper.RelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.th.cms.modular.system.entity.Relation">
        <id column="relation_id" property="relationId" />
        <result column="menu_id" property="menuId" />
        <result column="role_id" property="roleId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        relation_id AS "relationId", menu_id AS "menuId", role_id AS "roleId"
    </sql>
    <insert id="insertBatch">
        insert into sys_relation(relation_id,menu_id,role_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.relationId},#{item.menuId},#{item.roleId})
        </foreach>
    </insert>

</mapper>
