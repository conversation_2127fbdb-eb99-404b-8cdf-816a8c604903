package com.th.cms.modular.bizOperateLog.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.annotion.Permission;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.modular.bizOperateLog.entity.BizOperateLog;
import com.th.cms.modular.bizOperateLog.model.reqparam.BizOperateLogListParam;
import com.th.cms.modular.bizOperateLog.service.BizOperateLogExtService;
import com.th.cms.modular.bizOperateLog.service.BizOperateLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 操作日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Slf4j
@Controller
@RequestMapping("/bizOperateLog")
@RequiredArgsConstructor
@Api(tags = "操作日志管理")
public class BizOperateLogController extends BaseController {

    private static final String PREFIX = "/modular/bizOperateLog/bizOperateLog/";

    private final BizOperateLogService bizOperateLogService;
    private final BizOperateLogExtService bizOperateLogExtService;

    /**
     * 跳转到操作日志首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizOperateLog.html";
    }

    /**
     * 跳转到操作日志详情页
     */
    @RequestMapping("/bizOperateLog_detail")
    public String bizOperateLogDetail(@RequestParam Long bizOperateLogId, Model model) {
        BizOperateLog bizOperateLog = bizOperateLogExtService.queryById(bizOperateLogId);
        model.addAttribute("item", bizOperateLog);
        LogObjectHolder.me().set(bizOperateLog);
        return PREFIX + "bizOperateLog_detail.html";
    }

    /**
     * 获取操作日志列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    @ApiOperation(value = "获取操作日志列表", notes = "分页查询操作日志数据")
    public LayuiPageInfo list(BizOperateLogListParam bizOperateLogParam) {
        return bizOperateLogService.findPageBySpec(bizOperateLogParam);
    }
}
