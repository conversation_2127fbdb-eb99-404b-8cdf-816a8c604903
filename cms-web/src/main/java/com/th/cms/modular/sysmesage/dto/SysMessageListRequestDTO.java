package com.th.cms.modular.sysmesage.dto;

import lombok.Data;

@Data
public class SysMessageListRequestDTO {
    /**
     * 页码
     */
    private Integer page = 1;
    /**
     * 每页数量
     */
    private Integer limit = 10;
    /**
     * 消息名称
     */
    private String messageName;
    /**
     * 接收方式 sms/inapp/email
     */
    private Integer receiveMethod;
    /**
     * 接收方式
     */
    private Long tagId;
    /**
     * 状态 1 启用 0 关闭
     */
    private Integer status;
}
