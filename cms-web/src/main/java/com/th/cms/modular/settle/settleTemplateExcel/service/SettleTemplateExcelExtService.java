package com.th.cms.modular.settle.settleTemplateExcel.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Pair;
import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.exceptions.BusiException;
import com.th.cms.core.shiro.DataAuthService;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.enums.SettleTemplateNameEnum;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformService;
import com.th.cms.modular.oss.service.AliUploadOssImg;
import com.th.cms.modular.settle.settleBatch.plat.pdd.PddExcelParser;
import com.th.cms.modular.settle.settleOrder.model.ColumnInfo;
import com.th.cms.modular.settle.settleOrder.service.CsvParserService;
import com.th.cms.modular.settle.settleTemplateExcel.model.SettleTemplateExcel;
import com.th.cms.modular.settle.settleTemplateExcel.model.reqparam.ExcelColumns;
import com.th.cms.modular.settle.settleTemplateExcel.model.reqparam.TemplateReq;
import com.th.cms.modular.settle.settleTemplateField.model.SettleTemplateField;
import com.th.cms.modular.settle.settleTemplateField.service.SettleTemplateFieldService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SettleTemplateExcelExtService {
    @Autowired
    SettleTemplateExcelService settleTemplateExcelService;
    @Autowired
    SettleTemplateFieldService settleTemplateFieldService;
    @Autowired
    BizPlatformService bizPlatformService;

    @Transactional
    public void save(TemplateReq templateReq) {

        List<ExcelColumns> excelColumns = templateReq.getColumns();

        List<SettleTemplateExcel> settleTemplateExcels = settleTemplateExcelService.lambdaQuery().eq(SettleTemplateExcel::getTemplateName, templateReq.getTemplateName()).list();


        if (settleTemplateExcels.size() > 0) {
            //同名称不能保存
        } else {
            SettleTemplateExcel settleTemplateExcel = new SettleTemplateExcel();
            settleTemplateExcel.setTemplateName(templateReq.getTemplateName());
            settleTemplateExcel.setCreatedTime(new Date());
            settleTemplateExcel.setUpdateTime(new Date());
            settleTemplateExcel.setDuiyingField(templateReq.getDuiyingField());
            settleTemplateExcel.setExcelSrc(templateReq.getExcelUplUrlSc());

            BizPlatform bizPlatform = bizPlatformService.getById(templateReq.getPlatId());
            settleTemplateExcel.setPlatId(bizPlatform.getId());
            settleTemplateExcel.setPlatName(bizPlatform.getPlatName());

            ShiroUtils.setAddAuthInfo(settleTemplateExcel);
            settleTemplateExcelService.save(settleTemplateExcel);

            List<SettleTemplateField> settleTemplateFields = new ArrayList<>();
            excelColumns.forEach(excelColumns1 -> {
                SettleTemplateField settleTemplateField = new SettleTemplateField();
                settleTemplateField.setTemplateId(settleTemplateExcel.getId());
                settleTemplateField.setOriginalCode(excelColumns1.getOriginalCode());
                settleTemplateField.setOriginalName(excelColumns1.getOriginalName());
                settleTemplateField.setTargetName(excelColumns1.getTargetName());
                settleTemplateField.setUpdateTime(new Date());
                ShiroUtils.setAddAuthInfo(settleTemplateField);
                settleTemplateFields.add(settleTemplateField);
            });
            //保存行信息
            settleTemplateFieldService.saveBatch(settleTemplateFields);
        }
    }


    public void updateById(SettleTemplateExcel settleTemplateExcel) {
        setEnumsName(settleTemplateExcel);
        DataAuthService.checkPermision(settleTemplateExcelService.getById(settleTemplateExcel.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(settleTemplateExcel);
        SettleTemplateExcel settleTemplateExcelDb = queryById(settleTemplateExcel.getId());

        settleTemplateExcelService.updateById(settleTemplateExcel);
    }

    public SettleTemplateExcel queryById(Serializable id) {
        SettleTemplateExcel settleTemplateExcel = settleTemplateExcelService.getById(id);
        DataAuthService.checkPermision(settleTemplateExcel, DataPermisionTypeEnum.ByComp);

        return settleTemplateExcel;
    }

    public void removeById(Serializable id) {
        settleTemplateExcelService.removeById(id);
        settleTemplateFieldService.lambdaQuery().eq(SettleTemplateField::getTemplateId, id).list().forEach(settleTemplateField -> {
            settleTemplateFieldService.removeById(settleTemplateField.getId());
        });
    }

    private void setEnumsName(SettleTemplateExcel settleTemplateExcel) {
    }


    /**
     *
     * @return
     */
    public Pair<SettleTemplateExcel,List<SettleTemplateField>> findTmpFlds(String ossExcelPath){
        SettleTemplateExcel settleTemplateExcel = matchTmp(ossExcelPath);
        List<SettleTemplateField> templateFieldList = new ArrayList<>();
        if(settleTemplateExcel!=null){
            templateFieldList = settleTemplateFieldService.lambdaQuery().eq(SettleTemplateField::getTemplateId,settleTemplateExcel.getId()).isNotNull(SettleTemplateField::getTargetName).list();
        }
        Pair<SettleTemplateExcel,List<SettleTemplateField>> tmpari = new Pair<SettleTemplateExcel,List<SettleTemplateField>>(settleTemplateExcel,templateFieldList);

        return tmpari;
    }

    public SettleTemplateField findTField(List<SettleTemplateField> settleTemplateFields,SettleTemplateNameEnum settleTemplateNameEnum){
        for(SettleTemplateField settleTemplateField : settleTemplateFields){
            String targetName = settleTemplateField.getTargetName();
            SettleTemplateNameEnum settleTemplateNameEnumDs = SettleTemplateNameEnum.fromCode(targetName);
            if(settleTemplateNameEnumDs==settleTemplateNameEnum){
                return settleTemplateField;
            }
        }
        return null;
    }


    public SettleTemplateExcel matchTmp(String ossExcelPath) {
        //
//        File tmpFile = AliUploadOssImg.saveTolotmp(ossExcelPath, AliUploadOssImg.downloadFile(ossExcelPath, true));
        File tmpFile = null;
        try {
            tmpFile = AliUploadOssImg.saveToTmpFullPath(new File("").getAbsolutePath()+File.separator+ AliUploadOssImg.getFileNameFromUrl(ossExcelPath), AliUploadOssImg.downloadFile(ossExcelPath, true));
        } catch (Exception e) {
            log.error("下载文件失败",e);
            FileUtil.del(tmpFile);
            throw new BusiException("下载文件失败");
        }
        try {
            SettleTemplateExcel settleTemplateExcel = null;
            if (ossExcelPath.endsWith("csv")) {
                List<ColumnInfo> columnInfos = CsvParserService.parseCsvColumns(tmpFile);

                List<ExcelColumns> excelColumnsLst = columnInfos.stream().map(t->{
                    ExcelColumns excelColumns = new ExcelColumns();
                    excelColumns.setOriginalCode(t.getCol()+"");
                    excelColumns.setOriginalName(t.getTitle());
                    return excelColumns;
                }).collect(Collectors.toList());

                settleTemplateExcel = matchTemplate(excelColumnsLst);
                return settleTemplateExcel;
            } else if (ossExcelPath.endsWith("xls") || ossExcelPath.endsWith("xlsx")) {
                try {
                    List<ExcelColumns>  excelColumns = PddExcelParser.parseExcelHeader(tmpFile,ossExcelPath);
                    settleTemplateExcel = matchTemplate(excelColumns);
                    return settleTemplateExcel;
                }catch (Exception e){
                    throw new BusiException("文件解析错误",e);
                }
            }
            return settleTemplateExcel;
        }finally {
            FileUtil.del(tmpFile);
        }
    }

    public SettleTemplateExcel matchTemplate(List<ExcelColumns> excelColumns) {
        // 获取所有模板字段
        List<SettleTemplateField> allFields = settleTemplateFieldService.list();

        // 计算匹配度
        Map<Long, Integer> scoreMap = new HashMap<>();
        excelColumns.forEach(input -> {
            allFields.stream()
                    .filter(db -> db.getOriginalName().equals(input.getOriginalName()))
                    .forEach(db -> scoreMap.merge(db.getTemplateId(), 1, Integer::sum));
        });

        // 获取最佳匹配
        return scoreMap.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(entry -> settleTemplateExcelService.getById(entry.getKey()))
                .orElse(null);
    }
}
