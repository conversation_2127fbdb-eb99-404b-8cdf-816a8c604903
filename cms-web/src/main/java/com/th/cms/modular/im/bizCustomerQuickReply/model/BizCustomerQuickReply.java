package com.th.cms.modular.im.bizCustomerQuickReply.model;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="BizCustomerQuickReply对象", description="")
public class BizCustomerQuickReply implements Serializable {


    @ApiModelProperty(value = "id")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "客服id")
    @TableField("customer_id")
    private Long customerId;

    @TableField(exist = false)
    private String customerAccId;

    @ApiModelProperty(value = "回复")
    @TableField("reply")
    private String reply;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private String sort;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ExcelField(title="id",dictType="", align=2, sort=0)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @ExcelField(title="客服id",dictType="", align=2, sort=1)
    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }
    @ExcelField(title="回复",dictType="", align=2, sort=2)
    public String getReply() {
        return reply;
    }

    public void setReply(String reply) {
        this.reply = reply;
    }
    @ExcelField(title="排序",dictType="", align=2, sort=3)
    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=4)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=5)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCustomerAccId() {
        return customerAccId;
    }

    public void setCustomerAccId(String customerAccId) {
        this.customerAccId = customerAccId;
    }

    @Override
    public String toString() {
        return "BizCustomerQuickReply{" +
        "id=" + id +
        ", customerId=" + customerId +
        ", userId=" + userId +
        ", reply=" + reply +
        ", sort=" + sort +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
