package com.th.cms.modular.settle.settleOrder.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;


/**
 * <p>
 * 达人结算订单表
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="SettleOrder对象", description="达人结算订单表")
public class SettleOrder implements Serializable {


    @ApiModelProperty(value = "主键ID（自增）")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private Integer projectId;

    @ApiModelProperty(value = "项目编号")
    @TableField("project_no")
    private String projectNo;

    @ApiModelProperty(value = "项目简称")
    @TableField("project_name")
    private String projectName;

    @ApiModelProperty(value = "公司与达人分成比例（示例：70%/30%）")
    @TableField("company_talent_ratio")
    private String companyTalentRatio;

    @ApiModelProperty(value = "参与结算人数")
    @TableField("settle_pnum")
    private Integer settlePnum;

    @ApiModelProperty(value = "流水金额")
    @TableField("liushui_amount")
    private BigDecimal liushuiAmount;

    @ApiModelProperty(value = "应发放权益")
    @TableField("yingfa_amount")
    private BigDecimal yingfaAmount;

    @ApiModelProperty(value = "已发放权益")
    @TableField("yifa_amount")
    private BigDecimal yifaAmount;

    @ApiModelProperty(value = "已提现金额")
    @TableField("withdrawn_amount")
    private BigDecimal withdrawnAmount;

    @ApiModelProperty(value = "待提现金额（计算字段）")
    @TableField("unwithdrawn_amount")
    private BigDecimal unwithdrawnAmount;

    @ApiModelProperty(value = "已到账金额")
    @TableField("arrive_amount")
    private BigDecimal arriveAmount;

    @ApiModelProperty(value = "未到账金额")
    @TableField("noarrive_amount")
    private BigDecimal noarriveAmount;

    @ApiModelProperty(value = "非标结算人数")
    @TableField("feibiao_pnum")
    private Integer feibiaoPnum;

    @ApiModelProperty(value = "非标人数比例")
    @TableField("feibiao_rate")
    private Double feibiaoRate;

    @ApiModelProperty(value = "非标金额")
    @TableField("feibiao_amount")
    private BigDecimal feibiaoAmount;

    @ApiModelProperty(value = "非标金额比例")
    @TableField("feibiao_amount_rate")
    private BigDecimal feibiaoAmountRate;

    @ApiModelProperty(value = "异常人数")
    @TableField("settle_yichang_num")
    private Integer settleYichangNum;

    @ApiModelProperty(value = "异常比例")
    @TableField("settle_yichang_rate")
    private BigDecimal settleYichangRate;

    @ApiModelProperty(value = "批次状态")
    @TableField("settle_status")
    private Integer settleStatus;

    @ApiModelProperty(value = "批次状态")
    @TableField("settle_status_name")
    private String settleStatusName;

    @ApiModelProperty(value = "结算单")
    @TableField("jisuan_file")
    private String jisuanFile;

    @ApiModelProperty(value = "附件")
    @TableField("fujian_files")
    private String fujianFiles;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "运营小组名称")
    @TableField("group_name")
    private String groupName;

    @ApiModelProperty(value = "对接商务姓名")
    @TableField("business_contact")
    private String businessContact;

    @ApiModelProperty(value = "机构收益")
    @TableField("agency_revenue")
    private Integer agencyRevenue;

    @ApiModelProperty(value = "状态变更流水（JSON格式记录操作日志）")
    @TableField("status_log")
    private String statusLog;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_id")
    private String createId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private String deptId;

    @ApiModelProperty(value = "部门")
    @TableField("dept_name")
    private String deptName;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ExcelField(title="主键ID（自增）",dictType="", align=2, sort=0)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @ExcelField(title="项目ID",dictType="", align=2, sort=1)
    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }
    @ExcelField(title="项目编号",dictType="", align=2, sort=2)
    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }
    @ExcelField(title="项目简称",dictType="", align=2, sort=3)
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    @ExcelField(title="公司与达人分成比例（示例：70%/30%）",dictType="", align=2, sort=4)
    public String getCompanyTalentRatio() {
        return companyTalentRatio;
    }

    public void setCompanyTalentRatio(String companyTalentRatio) {
        this.companyTalentRatio = companyTalentRatio;
    }
    @ExcelField(title="参与结算人数",dictType="", align=2, sort=5)
    public Integer getSettlePnum() {
        return settlePnum;
    }

    public void setSettlePnum(Integer settlePnum) {
        this.settlePnum = settlePnum;
    }
    @ExcelField(title="流水金额",dictType="", align=2, sort=6)
    public BigDecimal getLiushuiAmount() {
        return liushuiAmount;
    }

    public void setLiushuiAmount(BigDecimal liushuiAmount) {
        this.liushuiAmount = liushuiAmount;
    }
    @ExcelField(title="应发放权益",dictType="", align=2, sort=7)
    public BigDecimal getYingfaAmount() {
        return yingfaAmount;
    }

    public void setYingfaAmount(BigDecimal yingfaAmount) {
        this.yingfaAmount = yingfaAmount;
    }
    @ExcelField(title="已发放权益",dictType="", align=2, sort=8)
    public BigDecimal getYifaAmount() {
        return yifaAmount;
    }

    public void setYifaAmount(BigDecimal yifaAmount) {
        this.yifaAmount = yifaAmount;
    }
    @ExcelField(title="已提现金额",dictType="", align=2, sort=9)
    public BigDecimal getWithdrawnAmount() {
        return withdrawnAmount;
    }

    public void setWithdrawnAmount(BigDecimal withdrawnAmount) {
        this.withdrawnAmount = withdrawnAmount;
    }
    @ExcelField(title="待提现金额（计算字段）",dictType="", align=2, sort=10)
    public BigDecimal getUnwithdrawnAmount() {
        return unwithdrawnAmount;
    }

    public void setUnwithdrawnAmount(BigDecimal unwithdrawnAmount) {
        this.unwithdrawnAmount = unwithdrawnAmount;
    }
    @ExcelField(title="已到账金额",dictType="", align=2, sort=11)
    public BigDecimal getArriveAmount() {
        return arriveAmount;
    }

    public void setArriveAmount(BigDecimal arriveAmount) {
        this.arriveAmount = arriveAmount;
    }
    @ExcelField(title="未到账金额",dictType="", align=2, sort=12)
    public BigDecimal getNoarriveAmount() {
        return noarriveAmount;
    }

    public void setNoarriveAmount(BigDecimal noarriveAmount) {
        this.noarriveAmount = noarriveAmount;
    }
    @ExcelField(title="非标结算人数",dictType="", align=2, sort=13)
    public Integer getFeibiaoPnum() {
        return feibiaoPnum;
    }

    public void setFeibiaoPnum(Integer feibiaoPnum) {
        this.feibiaoPnum = feibiaoPnum;
    }
    @ExcelField(title="非标人数比例",dictType="", align=2, sort=14)
    public Double getFeibiaoRate() {
        return feibiaoRate;
    }

    public void setFeibiaoRate(Double feibiaoRate) {
        this.feibiaoRate = feibiaoRate;
    }
    @ExcelField(title="非标金额",dictType="", align=2, sort=15)
    public BigDecimal getFeibiaoAmount() {
        return feibiaoAmount;
    }

    public void setFeibiaoAmount(BigDecimal feibiaoAmount) {
        this.feibiaoAmount = feibiaoAmount;
    }
    @ExcelField(title="非标金额比例",dictType="", align=2, sort=16)
    public BigDecimal getFeibiaoAmountRate() {
        return feibiaoAmountRate;
    }

    public void setFeibiaoAmountRate(BigDecimal feibiaoAmountRate) {
        this.feibiaoAmountRate = feibiaoAmountRate;
    }
    @ExcelField(title="异常人数",dictType="", align=2, sort=17)
    public Integer getSettleYichangNum() {
        return settleYichangNum;
    }

    public void setSettleYichangNum(Integer settleYichangNum) {
        this.settleYichangNum = settleYichangNum;
    }
    @ExcelField(title="异常比例",dictType="", align=2, sort=18)
    public BigDecimal getSettleYichangRate() {
        return settleYichangRate;
    }

    public void setSettleYichangRate(BigDecimal settleYichangRate) {
        this.settleYichangRate = settleYichangRate;
    }
    @ExcelField(title="批次状态",dictType="", align=2, sort=19)
    public Integer getSettleStatus() {
        return settleStatus;
    }

    public void setSettleStatus(Integer settleStatus) {
        this.settleStatus = settleStatus;
    }
    @ExcelField(title="批次状态",dictType="", align=2, sort=20)
    public String getSettleStatusName() {
        return settleStatusName;
    }

    public void setSettleStatusName(String settleStatusName) {
        this.settleStatusName = settleStatusName;
    }
    @ExcelField(title="结算单",dictType="", align=2, sort=21)
    public String getJisuanFile() {
        return jisuanFile;
    }

    public void setJisuanFile(String jisuanFile) {
        this.jisuanFile = jisuanFile;
    }
    @ExcelField(title="附件",dictType="", align=2, sort=22)
    public String getFujianFiles() {
        return fujianFiles;
    }

    public void setFujianFiles(String fujianFiles) {
        this.fujianFiles = fujianFiles;
    }
    @ExcelField(title="备注",dictType="", align=2, sort=23)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    @ExcelField(title="运营小组名称",dictType="", align=2, sort=24)
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    @ExcelField(title="对接商务姓名",dictType="", align=2, sort=25)
    public String getBusinessContact() {
        return businessContact;
    }

    public void setBusinessContact(String businessContact) {
        this.businessContact = businessContact;
    }
    @ExcelField(title="机构收益",dictType="", align=2, sort=26)
    public Integer getAgencyRevenue() {
        return agencyRevenue;
    }

    public void setAgencyRevenue(Integer agencyRevenue) {
        this.agencyRevenue = agencyRevenue;
    }
    @ExcelField(title="状态变更流水（JSON格式记录操作日志）",dictType="", align=2, sort=27)
    public String getStatusLog() {
        return statusLog;
    }

    public void setStatusLog(String statusLog) {
        this.statusLog = statusLog;
    }
    @ExcelField(title="创建人ID",dictType="", align=2, sort=28)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
    @ExcelField(title="创建人",dictType="", align=2, sort=29)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    @ExcelField(title="部门ID",dictType="", align=2, sort=30)
    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
    @ExcelField(title="部门",dictType="", align=2, sort=31)
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    @ExcelField(title="公司ID",dictType="", align=2, sort=32)
    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
    @ExcelField(title="公司名称",dictType="", align=2, sort=33)
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=34)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=35)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "SettleOrder{" +
        "id=" + id +
        ", projectId=" + projectId +
        ", projectNo=" + projectNo +
        ", projectName=" + projectName +
        ", companyTalentRatio=" + companyTalentRatio +
        ", settlePnum=" + settlePnum +
        ", liushuiAmount=" + liushuiAmount +
        ", yingfaAmount=" + yingfaAmount +
        ", yifaAmount=" + yifaAmount +
        ", withdrawnAmount=" + withdrawnAmount +
        ", unwithdrawnAmount=" + unwithdrawnAmount +
        ", arriveAmount=" + arriveAmount +
        ", noarriveAmount=" + noarriveAmount +
        ", feibiaoPnum=" + feibiaoPnum +
        ", feibiaoRate=" + feibiaoRate +
        ", feibiaoAmount=" + feibiaoAmount +
        ", feibiaoAmountRate=" + feibiaoAmountRate +
        ", settleYichangNum=" + settleYichangNum +
        ", settleYichangRate=" + settleYichangRate +
        ", settleStatus=" + settleStatus +
        ", settleStatusName=" + settleStatusName +
        ", jisuanFile=" + jisuanFile +
        ", fujianFiles=" + fujianFiles +
        ", remark=" + remark +
        ", groupName=" + groupName +
        ", businessContact=" + businessContact +
        ", agencyRevenue=" + agencyRevenue +
        ", statusLog=" + statusLog +
        ", createId=" + createId +
        ", createName=" + createName +
        ", deptId=" + deptId +
        ", deptName=" + deptName +
        ", companyId=" + companyId +
        ", companyName=" + companyName +
        ", updateTime=" + updateTime +
        ", createTime=" + createTime +
        "}";
    }
}
