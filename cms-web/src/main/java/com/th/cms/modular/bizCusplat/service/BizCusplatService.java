package com.th.cms.modular.bizCusplat.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.bizCusplat.dao.BizCusplatMapper;
import com.th.cms.modular.bizCusplat.model.BizCusplat;
import com.th.cms.modular.bizCusplat.model.reqparam.BizCusplatListParam;
import org.springframework.stereotype.Service;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizCusplatService extends ServiceImpl<BizCusplatMapper, BizCusplat> implements IService<BizCusplat> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizCusplatListParam param) {
        QueryWrapper<BizCusplat> objectQueryWrapper = new QueryWrapper<>();
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);
        return layuiPageInfo;

    }
}
