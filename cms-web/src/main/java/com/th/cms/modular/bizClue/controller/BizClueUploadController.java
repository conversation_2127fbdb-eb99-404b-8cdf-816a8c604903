package com.th.cms.modular.bizClue.controller;


import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.bizClue.dto.BizClueUploadDto;
import com.th.cms.modular.bizClue.dto.BizClueUploadVo;
import com.th.cms.modular.bizClue.dto.BizClueUplodListParam;
import com.th.cms.modular.bizClue.dto.PageDto;
import com.th.cms.modular.bizClue.entity.BizClueUpload;
import com.th.cms.modular.bizClue.service.BizClueUploadService;
import com.th.cms.modular.system.entity.User;
import com.th.cms.util.RedissonLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <p>
 * 达人质检测表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@RestController
@RequestMapping("/user/bizClueUpload/")
public class BizClueUploadController extends BaseController {

    @Autowired
    private BizClueUploadService bizClueUploadService;


    /**
     * 上传质检
     * @param dto
     * @return
     */
    @PostMapping("upload")
    public ResponseData upload(@RequestBody BizClueUploadDto dto){
        bizClueUploadService.upload(dto);
        return ResponseData.success();
    }

    /**
     * 领取质检
     * @param takeCount
     * @return
     */
    @GetMapping("take/{takeCount}")
    public ResponseData take(@PathVariable Integer takeCount){
        boolean lock = RedissonLockUtil.tryLock("takeUpload", 5, 60);
        if (!lock) {
            return ResponseData.error("系统繁忙，请稍后重试");
        }
        try {
            bizClueUploadService.take(takeCount);
        }catch (IllegalArgumentException e){
            return ResponseData.error(e.getMessage());
        }catch (Exception e){
            log.error("系统异常，请稍后再试",e);
            return ResponseData.error("系统异常，请稍后再试");
        }finally {
            RedissonLockUtil.unlock("takeUpload");
        }
        return ResponseData.success();
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @GetMapping("info/{id}")
    public ResponseData<BizClueUploadVo> info(@PathVariable @NotNull Integer id){
        return ResponseData.success(bizClueUploadService.info(id));
    }

    /**
     * 释放质检
     * @param ownerStaffId
     * @return
     */
    @PutMapping("release/{ownerStaffId}")
    public ResponseData release(@PathVariable @NotNull Long ownerStaffId){
        bizClueUploadService.release(ownerStaffId);
        return ResponseData.success();
    }

    /**
     * 删除
     * @param id
     * @return
     */

    @DeleteMapping("delete/{id}")
    public ResponseData delete(@PathVariable @NotNull Integer id){
        bizClueUploadService.delete(id);
        return ResponseData.success();
    }

    /**
     * 我的质检列表
     * @param param
     * @return
     */
    @PostMapping("list")
    public ResponseData<Page<BizClueUpload>> list(@RequestBody BizClueUplodListParam param){
        return ResponseData.success(bizClueUploadService.list(param));
    }

    /**
     * 修改
     * @param entity
     * @return
     */
    @PostMapping("update")
    public ResponseData update(@RequestBody BizClueUpload entity){
        bizClueUploadService.edit(entity);
        return ResponseData.success();
    }

    /**
     * 审批通过
     * @param id
     * @return
     */
    @PutMapping("approve/{id}")
    public ResponseData approve(@PathVariable @NotNull Integer id){
        bizClueUploadService.approve(id);
        return ResponseData.success();
    }

    /**
     * 拒绝
     * @param dto
     * @return
     */
    @PostMapping("reject")
    public ResponseData reject(@RequestBody BizClueUploadDto dto){
        bizClueUploadService.reject(dto);
        return ResponseData.success();
    }

    /**
     * 剩余可领取数量
     * @return
     */
    @GetMapping("leftTakeCount")
    public ResponseData<Integer> leftTakeCount(){
        return ResponseData.success(bizClueUploadService.leftTakeCount());
    }

    /**
     * 当前所在 质检组 所有组员
     * @return
     */
    @GetMapping("qcList")
    public ResponseData<List<User>> qcList(){
        return ResponseData.success(bizClueUploadService.qcList());
    }

}
