package com.th.cms.modular.wf.bizCustomizeForm.service;


import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.wf.bizCustomizeForm.model.BizCustomizeForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;

@Service
public class BizCustomizeFormExtService {
    @Autowired
    BizCustomizeFormService bizCustomizeFormService;

    public void save(BizCustomizeForm bizCustomizeForm) {
        setEnumsName(bizCustomizeForm);
        ShiroUtils.setAddAuthInfo(bizCustomizeForm);
        bizCustomizeFormService.save(bizCustomizeForm);
    }

    public void updateById(BizCustomizeForm bizCustomizeForm) {
        setEnumsName(bizCustomizeForm);
        DataAuthService.checkPermision(bizCustomizeFormService.getById(bizCustomizeForm.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(bizCustomizeForm);
        BizCustomizeForm bizCustomizeFormDb = queryById(bizCustomizeForm.getId());

        bizCustomizeFormService.updateById(bizCustomizeForm);
    }

    public BizCustomizeForm queryById(Serializable id) {
        BizCustomizeForm bizCustomizeForm = bizCustomizeFormService.getById(id);
        DataAuthService.checkPermision(bizCustomizeForm, DataPermisionTypeEnum.ByComp);

        return bizCustomizeForm;
    }

    public void removeById(Serializable id) {
        BizCustomizeForm bizCustomizeForm = bizCustomizeFormService.getById(id);
        DataAuthService.checkPermision(bizCustomizeForm, DataPermisionTypeEnum.ByComp);
        BizCustomizeForm bizCustomizeFormRecord = new BizCustomizeForm();
        bizCustomizeFormRecord.setId(bizCustomizeForm.getId());

        bizCustomizeFormService.updateById(bizCustomizeFormRecord);
    }

    private void setEnumsName(BizCustomizeForm bizCustomizeForm) {
    }
}
