package com.th.cms.modular.bizSettleFencheng.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.bizSettleFencheng.model.BizSettleFencheng;
import com.th.cms.modular.bizSettleFencheng.service.BizSettleFenchengService;
import com.th.cms.modular.bizSettleFencheng.service.BizSettleFenchengExtService;
import com.th.cms.modular.bizSettleFencheng.model.reqparam.BizSettleFenchengListParam;

/**
 * 结算比例控制器
 *
 * <AUTHOR>
 * @Date 2025-03-31 11:31:07
 */
@Controller
@RequestMapping("/bizSettleFencheng")
public class BizSettleFenchengController extends BaseController {

    private String PREFIX = "/modular/bizSettleFencheng/bizSettleFencheng/";
    @Autowired
    BizSettleFenchengExtService bizSettleFenchengExtService;
    @Autowired
    private BizSettleFenchengService bizSettleFenchengService;

    /**
     * 跳转到结算比例首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizSettleFencheng.html";
    }

    /**
     * 跳转到添加结算比例
     */
    @Permission
    @RequestMapping("/bizSettleFencheng_add")
    public String bizSettleFenchengAdd() {
        return PREFIX + "bizSettleFencheng_add.html";
    }

    /**
     * 跳转到修改结算比例
     */
    @Permission
    @RequestMapping("/bizSettleFencheng_update")
    public String bizSettleFenchengUpdate(@RequestParam  Integer bizSettleFenchengId, Model model) {
        BizSettleFencheng bizSettleFencheng = bizSettleFenchengExtService.queryById(bizSettleFenchengId);
        model.addAttribute("item",bizSettleFencheng);
        LogObjectHolder.me().set(bizSettleFencheng);
        return PREFIX + "bizSettleFencheng_edit.html";
    }
    @RequestMapping("/bizSettleFencheng_detail")
    public String bizSettleFenchengDetail(@RequestParam Integer bizSettleFenchengId, Model model) {
        BizSettleFencheng bizSettleFencheng = bizSettleFenchengExtService.queryById(bizSettleFenchengId);
        model.addAttribute("item",bizSettleFencheng);
        LogObjectHolder.me().set(bizSettleFencheng);
        return PREFIX + "bizSettleFencheng_detail.html";
    }
    /**
     * 获取结算比例列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(BizSettleFenchengListParam bizSettleFenchengParam) {
        return bizSettleFenchengService.findPageBySpec(bizSettleFenchengParam);
    }

    /**
     * 新增结算比例
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/bizSettleFencheng/bizSettleFencheng_add")
    @ResponseBody
    public ResponseData add(BizSettleFencheng bizSettleFencheng) {
         bizSettleFenchengExtService.save(bizSettleFencheng);
         return ResponseData.success();
    }

    /**
     * 删除结算比例
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  bizSettleFenchengId) {
        bizSettleFenchengExtService.removeById(bizSettleFenchengId);
         return ResponseData.success();
    }

    /**
     * 修改结算比例
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizSettleFencheng/bizSettleFencheng_update")
    @ResponseBody
    public ResponseData update(BizSettleFencheng bizSettleFencheng) {
        bizSettleFenchengExtService.updateById(bizSettleFencheng);
        return ResponseData.success();
    }

    /**
     * 结算比例详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  bizSettleFenchengId) {
       BizSettleFencheng detail = bizSettleFenchengExtService.queryById(bizSettleFenchengId);
       return ResponseData.success(detail);
    }
}
