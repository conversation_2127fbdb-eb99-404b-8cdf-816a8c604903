package com.th.cms.modular.userindex.service;

import com.google.common.collect.Lists;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.core.common.constant.RoleConsts;
import com.th.cms.core.easyexcel.PageResult;
import com.th.cms.core.util.*;
import com.th.cms.modular.influcer.bizPlatformInvite.service.BizPlatformInviteService;
import com.th.cms.modular.system.entity.Dept;
import com.th.cms.modular.system.entity.Notice;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.mapper.DeptMapper;
import com.th.cms.modular.system.mapper.NoticeMapper;
import com.th.cms.modular.system.mapper.UserMapper;
import com.th.cms.modular.userindex.convert.UserIndexConvert;
import com.th.cms.modular.userindex.dataobject.AuthenticationStatusPerPersonDO;
import com.th.cms.modular.userindex.dataobject.CompletePerPersonDO;
import com.th.cms.modular.userindex.dto.*;
import com.th.cms.modular.userindex.mapper.UserIndexMapper;
import com.th.cms.util.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserIndexService {
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private JedisClient jedisClient;
    @Autowired
    private UploadUtil uploadUtil;
    @Autowired
    private SmsUtil smsUtil;
    @Autowired
    private UserIndexMapper userIndexMapper;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private NoticeMapper noticeMapper;
    @Autowired
    private RoleConsts roleConsts;
    @Resource
    private BizPlatformInviteService bizPlatformInviteService;
    private static final String USER_VALID_KEY = "USER_VALID_KEY";

    /**
     * 修改用户密码
     *
     * @param password    密码
     * @param userNotNull user
     * @return 结果
     */
    public Boolean updateUserPassword(String password, UserDTO userNotNull) {
        try {
            User user = new User();
            user.setUserId(userNotNull.getUserId());
            user.setPassword(password);
            user.setUpdateUser(userNotNull.getUserId());
            user.setUpdateTime(new Date());
            userMapper.updateById(user);
            log.info("修改密码成功 for user ===={}", user.getUserId());
            return true;
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return false;
        }
    }

    /**
     * 修改用户手机号
     *
     * @param phone 参数
     * @return 结果
     */
    public Boolean updateUserPhone(String phone, String code, UserDTO userNotNull) {
        validCode(phone, code);
        try {
            User user = new User();
            user.setUserId(userNotNull.getUserId());
            user.setPhone(phone);
            user.setUpdateUser(userNotNull.getUserId());
            user.setUpdateTime(new Date());
            userMapper.updateById(user);
            log.info("修改用户手机号 for user ===={}", user.getUserId());
            return true;
        } catch (Exception e) {
            log.error("修改修改用户手机号失败", e);
            return false;
        }
    }

    /**
     * 校验验证码
     *
     * @param phone 手机号
     * @param code  验证码
     */
    private void validCode(String phone, String code) {
        String s = jedisClient.get(USER_VALID_KEY + phone);
        if (StringUtils.isBlank(s) || !s.equals(code)) {
            throw new RuntimeException("验证码已过期");
        }
        jedisClient.del(USER_VALID_KEY + phone);
    }

    /**
     * 修改用户头像
     *
     * @param file        文件
     * @param userNotNull user
     * @return 结果
     */

    public Boolean updateUserAvatar(MultipartFile file, UserDTO userNotNull) {
        String s = uploadUtil.uploadFile(file, "avatar");
        try {
            User user = new User();
            user.setUserId(userNotNull.getUserId());
            user.setAvatar(s);
            user.setUpdateUser(userNotNull.getUserId());
            user.setUpdateTime(new Date());
            userMapper.updateById(user);
            log.info("修改用户头像成功 for user ===={}", user.getUserId());
            return true;
        } catch (Exception e) {
            log.error("修改用户头像失败", e);
            return false;
        }
    }

    /**
     * 发送验证码
     *
     * @param phone 参数
     * @return 结果
     */
    public Boolean sendValidCode(String phone) throws Exception {
        String code = smsUtil.getSendCode();
        smsUtil.sendSms(phone, code);
        jedisClient.set(USER_VALID_KEY + phone, code);
        jedisClient.setExpire(USER_VALID_KEY + phone, 60L * 5, code);
        return true;
    }

    public AuthenticationStatusPerPersonDTO authenticationStatusPerPerson(AuthenticationStatusPerPersonRequestDTO requestDTO, UserDTO userNotNull) {
        if (1 == 1) {
            return mockMe(requestDTO, userNotNull);
        }
        Boolean isAll = parseAllFlg(userNotNull);
        //获取当前用户所属工区
        Dept dept = queryWorkArea(userNotNull);
        List<Long> allDeptIds = queryAllDeptIds(userNotNull, dept);

        List<AuthenticationStatusPerPersonDO> list = userIndexMapper.authenticationStatusPerPerson(UserIndexConvert.convertAuthenticationStatusPerPersonDO(requestDTO, userNotNull, isAll, allDeptIds));

        return UserIndexConvert.convertAuthenticationStatusPerPersonDTO(list, isAll, dept);
    }

    private AuthenticationStatusPerPersonDTO mockMe(AuthenticationStatusPerPersonRequestDTO requestDTO, UserDTO userNotNull) {
        AuthenticationStatusPerPersonDTO authenticationStatusPerPersonDTO = new AuthenticationStatusPerPersonDTO();
        authenticationStatusPerPersonDTO.setGroupName("MOCK");
        List<AuthenticationStatusPerPersonDTO.DataDTO> list = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            AuthenticationStatusPerPersonDTO.DataDTO dto = new AuthenticationStatusPerPersonDTO.DataDTO();
            dto.setName("test" + i);
            dto.setCount(new BigDecimal(i * RandomUtils.nextInt()));
            list.add(dto);

        }
        authenticationStatusPerPersonDTO.setList(list);
        return authenticationStatusPerPersonDTO;


    }

    /**
     * 查询所有的工区部门
     *
     * @param userNotNull 当前用户
     * @param dept        当前用户所属工区
     * @return 所有的工区部门
     */
    private List<Long> queryAllDeptIds(UserDTO userNotNull, Dept dept) {
        if (dept == null) {
            return Lists.newArrayList();
        }
        //获取此工区下所有部门
        List<Dept> depts = deptMapper.likePids(dept.getDeptId());
        return OptionalUtil.defaultList(depts).stream().map(Dept::getDeptId).collect(Collectors.toList());

    }

    /**
     * 主管、总监、工区负责人、业务负责人、老板和其他人 返回成功
     *
     * @param userNotNull 当前用户
     * @return 是否成功
     */
    private Boolean parseAllFlg(UserDTO userNotNull) {
        List<Long> roleList = userNotNull.getRoleList();
        return roleConsts.getUserIndexBarOrLineRoleIds().stream().anyMatch(roleList::contains);
    }

    public CompletePerPersonDTO completePerPerson(CompletePerPersonRequestDTO requestDTO, UserDTO userNotNull) {
        if (1 == 1) {
            return mockcompletePerPerson(requestDTO, userNotNull);
        }


        Boolean isAll = parseAllFlg(userNotNull);
        //获取当前用户所属工区
        Dept dept = queryWorkArea(userNotNull);
        List<Long> allDeptIds = queryAllDeptIds(userNotNull, dept);
        List<CompletePerPersonDO> list = userIndexMapper.completePerPerson(UserIndexConvert.convertCompletePerPersonDO(requestDTO, userNotNull, isAll, allDeptIds));

        return UserIndexConvert.convertCompletePerPersonDTO(requestDTO, isAll, dept, list);

    }

    private CompletePerPersonDTO mockcompletePerPerson(CompletePerPersonRequestDTO requestDTO, UserDTO userNotNull) {
        //mock 一些数据
        CompletePerPersonDTO completePerPersonDTO = new CompletePerPersonDTO();
        completePerPersonDTO.setGroupName("MOCK");
        CompletePerPersonDTO.DataDTO dto = new CompletePerPersonDTO.DataDTO();
        List<String> dateList = new ArrayList<>();
        String d = "2025-06";
        for (int i = 0; i < 30; i++) {
            //30天
            String s = (i + 1) < 10 ? "0" : (i + 1) + "";
            dateList.add(d + "-" + s);
        }
        dto.setDateList(dateList);
        dto.setGroups(mockGroup(dateList));
        completePerPersonDTO.setDataDTO(dto);
        return completePerPersonDTO;
    }

    private List<CompletePerPersonDTO.GroupDTO> mockGroup(List<String> dateList) {
        List<CompletePerPersonDTO.GroupDTO> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            CompletePerPersonDTO.GroupDTO dto = new CompletePerPersonDTO.GroupDTO();
            dto.setName("group" + i);
            dto.setCount(Lists.newArrayList());

            for (int j = 0; j < dateList.size(); j++) {
                dto.getCount().add(RandomUtils.nextLong());
            }
            list.add(dto);
        }
        return list;


    }

    public UserInfoDTO userInfo(UserDTO userNotNull) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setName(userNotNull.getName());
        userInfoDTO.setPhone(userNotNull.getPhone());
        userInfoDTO.setDeptName(userNotNull.getDeptName());
        userInfoDTO.setAvatar(userNotNull.getAvatar());
        Dept dept = queryWorkArea(userNotNull);
        if (dept != null) {
            userInfoDTO.setRegionName(dept.getSimpleName());
        }
        userInfoDTO.setRoleName(String.join("、", OptionalUtil.defaultList(userNotNull.getRoleNames())));
        userInfoDTO.setInviteCode(bizPlatformInviteService.generateInvateCode(userNotNull.getUserId(), userNotNull.getName()));
        return userInfoDTO;


    }

    private Dept queryWorkArea(UserDTO userNotNull) {
        Dept dept = deptMapper.selectById(userNotNull.getDeptId());
        Long pid = dept.getPid();
        if (pid == null || pid == 0L) {
            return null;
        }
        return queryDeptById(pid);
    }

    private Dept queryDeptById(Long id) {
        Dept dept = deptMapper.selectById(id);
        Long pid = dept.getPid();
        if (pid == null || pid == 0L) {
            return null;
        }
        if (Objects.equals(dept.getDeptType(), "工区")) {
            return dept;
        } else {
            return queryDeptById(pid);
        }
    }

    public UserTodoDTO todo(UserTodoRequestDTO requestDTO, UserDTO userNotNull) {
        UserTodoDTO userTodoDTO = new UserTodoDTO();
        userTodoDTO.setPageResult(queryTodoByPage(requestDTO, userNotNull));
        return userTodoDTO;


    }

    private PageResult<UserTodoDTO.DataDTO> queryTodoByPage(UserTodoRequestDTO requestDTO, UserDTO userNotNull) {
        LambdaQueryWrapper<Notice> qw = Wrappers.lambdaQuery();
        IPage<Notice> noticeIPage = noticeMapper.selectPage(new Page<>(requestDTO.getPage(), requestDTO.getLimit()), qw);
        return PageResult.convertPage(noticeIPage, UserIndexConvert::convertUserTodoDTO);

    }

    /**
     * 所有审核数据：我列一下
     * 项目审核、提审审核、审核流审核、结算审核、提现审核、达人认证审核。
     *
     * @param userNotNull 当前用户
     * @return 列表
     */
    public AuditCenterDTO auditCenter(UserDTO userNotNull) {
        AuditCenterDTO auditCenterDTO = new AuditCenterDTO();
        auditCenterDTO.setTodayAudit(userIndexMapper.queryTodayAudit(userNotNull.getUserId()));
        auditCenterDTO.setWaitAudit(userIndexMapper.queryWaitAudit(userNotNull.getUserId()));
        return auditCenterDTO;
    }

    public List<BusinessRankDTO> rankList(BusinessRankRequestDTO requestDTO, UserDTO userNotNull) {
        return userIndexMapper.rankList(UserIndexConvert.convertRankListDO(requestDTO), userNotNull);
    }
}
