package com.th.cms.modular.influcer.bizInflucer.service;

public class IdGenerator {
    
    /**
     * 生成TH开头的11位字符串
     * @param number 输入的数字（将被转换为字符串并填充到TH后面）
     * @return 格式为"TH" + 数字（总长度11位，不足部分补0）
     * @throws IllegalArgumentException 如果数字转换后的长度超过9位
     */
    public static String generateThId(Long number) {
        // 将数字转换为字符串
        String numberStr = String.valueOf(number);
        
        // 检查数字位数是否超过允许的最大长度（9位）
        if (numberStr.length() > 9) {
            throw new IllegalArgumentException("输入数字过大，转换后长度超过9位");
        }
        
        // 使用String.format进行格式化，左侧补0
        String paddedNumber = String.format("%09d", number);
        
        // 拼接TH前缀
        return "TH" + paddedNumber;
    }

    public static void main(String[] args) {
        // 测试示例
        System.out.println(generateThId(123L));      // 输出: TH000000123
        System.out.println(generateThId(9876543L));  // 输出: TH009876543
        System.out.println(generateThId(0L));        // 输出: TH000000000
        System.out.println(generateThId(123456789L));// 输出: TH123456789
        
        try {
            System.out.println(generateThId(1234567890L)); // 抛出异常
        } catch (IllegalArgumentException e) {
            System.out.println("捕获异常: " + e.getMessage());
        }
    }
}