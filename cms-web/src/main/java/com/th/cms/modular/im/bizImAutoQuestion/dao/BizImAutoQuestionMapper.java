package com.th.cms.modular.im.bizImAutoQuestion.dao;

import com.th.cms.modular.im.bizImAutoQuestion.model.BizImAutoQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface BizImAutoQuestionMapper extends BaseMapper<BizImAutoQuestion> {

    @Update("update biz_im_auto_question set status = #{newStatus} where id = #{id}")
    int updateStatus(@Param("id") Integer bizImAutoReplyId, @Param("newStatus")String newStatus);
}
