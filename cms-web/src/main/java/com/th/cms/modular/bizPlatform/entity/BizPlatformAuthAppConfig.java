package com.th.cms.modular.bizPlatform.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizPlatformAuthAppConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 认证平台ID
     */
    private Long authPlatId;

    /**
     * 认证平台
     */
    private String authPlatName;

    /**
     * 启用关闭
     */
    private Integer status;

    /**
     * 认证用户
     */
    private String authAccount;

    /**
     * 认证内容
     */
    private String authContent;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新人ID
     */
    private Long updateId;

    /**
     * 最后更新人姓名
     */
    private String updateName;

    /**
     * 最后更新时间
     */
    private Date updateTime;

}
