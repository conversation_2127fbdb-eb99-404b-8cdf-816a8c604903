package com.th.cms.modular.bizPlatform.convert;

import cn.stylefeng.roses.kernel.model.enums.YesOrNotEnum;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformDTO;

public class AppPlatformConvert {
    public static AppPlatformDTO convertAppPlatformDTO(AppPlatformDTO t) {
        AppPlatformDTO appPlatformDTO = new AppPlatformDTO();
        appPlatformDTO.setId(t.getId());
        appPlatformDTO.setPlatId(t.getPlatId());
        appPlatformDTO.setPlatName(t.getPlatName());
        appPlatformDTO.setIconPic(t.getIconPic());
        appPlatformDTO.setStatus(t.getStatus()==null? YesOrNotEnum.N.getCode() :t.getStatus());
        return appPlatformDTO;
    }
}
