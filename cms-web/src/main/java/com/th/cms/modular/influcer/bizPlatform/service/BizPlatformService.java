package com.th.cms.modular.influcer.bizPlatform.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.OptionalUtil;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.enums.BizPlatLevel;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.mapper.BizPlatformMapper;
import com.th.cms.modular.influcer.bizPlatform.model.reqparam.BizPlatformListParam;
import com.th.cms.modular.settle.contract.dto.PlatformDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizPlatformService extends ServiceImpl<BizPlatformMapper, BizPlatform> implements IService<BizPlatform> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizPlatformListParam param) {
        QueryWrapper<BizPlatform> objectQueryWrapper = new QueryWrapper<>();
        if(param.getCengji()!=null){
            objectQueryWrapper.lambda().eq(BizPlatform::getLevels,param.getCengji());
        }
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);
        return layuiPageInfo;

    }

    public void listTree(){

    }

    public List<PlatformDTO> queryByName(String name) {
        LambdaQueryWrapper<BizPlatform> objectQueryWrapper = new LambdaQueryWrapper<>();
        objectQueryWrapper.eq(BizPlatform::getLevels, BizPlatLevel.TWO.getCode());
        if(StringUtils.isNotBlank(name)){
            objectQueryWrapper.like(BizPlatform::getPlatName,"%"+name+"%");
        }
        return OptionalUtil.defaultList(this.baseMapper.selectList(objectQueryWrapper)).stream().map(item->{
            PlatformDTO platformDTO = new PlatformDTO();
            platformDTO.setId(item.getId());
            platformDTO.setPlatName(item.getPlatName());
            platformDTO.setIconPic(item.getIconPic());
            platformDTO.setJiancheng(item.getJiancheng());
            return platformDTO;
        }).collect(Collectors.toList());
    }
}
