package com.th.cms.modular.influcer.bizInflucer.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.th.cms.modular.influcer.bizInflucerPlatform.model.BizInflucerPlatform;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BizInflucerInfoVO {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "达人ID")
    private String influcerId;

    @ApiModelProperty(value = "登录名")
    private String loginName;

    @ApiModelProperty(value = "手机号")
    private String loginTel;

    @ApiModelProperty(value = "昵称")
    private String nikeName;

    @ApiModelProperty(value = "头像")
    private String icon;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "登录凭证")
    private String authToken;

    @ApiModelProperty(value = "云信accid")
    private String accid;

    @ApiModelProperty(value = "云信token")
    private String acctoken;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号")
    private String card;

    @ApiModelProperty(value = "认证状态")
    private Integer isVerification;

    @ApiModelProperty(value = "认证时间")
    private Date authTime;

    @ApiModelProperty(value = "生日")
    private String birthday;

    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "认证头像")
    private String facialPictureUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private Date loginTime;


    private String businessName;//达人绑定商务名称

    private Long businessId;

    private List<BizInflucerPlatform> platformList;//平台认证记录

    private List<PlatformAuthVO> authPlatforms;//平台认证信息-部分信息

    private List<PlatformAuthVO> waitAuthPlatforms;//平台认证信息-部分信息
    @TableField(exist = false)
    private Integer platformAuth;//是否平台认证
    @TableField(exist = false)
    private String platformLogo;//平台logo
    @TableField(exist = false)
    private Boolean enableCancelApprove;//显示作废按钮
}
