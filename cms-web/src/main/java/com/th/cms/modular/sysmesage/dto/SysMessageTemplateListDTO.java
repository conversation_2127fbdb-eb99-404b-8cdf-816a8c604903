package com.th.cms.modular.sysmesage.dto;

import lombok.Data;

import java.util.Date;

@Data
public class SysMessageTemplateListDTO {
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板标题
     */
    private String title;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 模板类型：sms, inapp, email
     */
    private Integer msgType;
    /**
     * 模板类型：sms, inapp, email
     */
    private String msgTypeDesc;
    /**
     * 语言代码，如 en_US, zh_TW
     */
    private String langCode;

    /**
     * 状态 1 启用 0 不可用
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新人ID
     */
    private Long updateId;

    /**
     * 最后更新人姓名
     */
    private String updateName;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 模板代码
     */
    private String templateCode;

    /**
     * 供应商代码
     */
    private Integer supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 适用人群
     */
    private String applicablePeople;
    /**
     * 描述
     */
    private String remark;
}
