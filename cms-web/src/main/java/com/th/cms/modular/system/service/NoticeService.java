package com.th.cms.modular.system.service;

import cn.stylefeng.roses.kernel.model.enums.YesOrNotEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.th.cms.core.common.page.LayuiPageFactory;
import com.th.cms.core.util.NumberUtil;
import com.th.cms.core.util.OptionalUtil;
import com.th.cms.modular.system.entity.Notice;
import com.th.cms.modular.system.mapper.NoticeMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.util.UserDTO;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 通知表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-07
 */
@Service
public class NoticeService extends ServiceImpl<NoticeMapper, Notice> {

    /**
     * 获取通知列表
     *
     * <AUTHOR>
     * @Date 2018/12/23 6:05 PM
     */
    public Page<Map<String, Object>> list(String condition) {
        Page page = LayuiPageFactory.defaultPage();
        return this.baseMapper.list(page, condition);
    }

    public List<Notice> listUnRead(int limit, UserDTO userNotNull) {
        LambdaQueryWrapper<Notice> qw = Wrappers.lambdaQuery();
        qw.eq(Notice::getReadOn, YesOrNotEnum.N.getCode());
        qw.eq(Notice::getReceiveUserId, userNotNull.getUserId());
        qw.orderByDesc(Notice::getCreateTime);
        IPage<Notice> noticeIPage = baseMapper.selectPage(new Page<>(1, limit), qw);
        return noticeIPage.getRecords();
    }

    public void allRead(List<Long> msgIds) {
        for (Long l : OptionalUtil.defaultList(msgIds)) {
            Notice notice = new Notice();
            notice.setNoticeId(l);
            notice.setReadOn(NumberUtil.intToLong(YesOrNotEnum.Y.getCode()));
            notice.setUpdateTime(new Date());
            baseMapper.updateById(notice);
        }
    }
}
