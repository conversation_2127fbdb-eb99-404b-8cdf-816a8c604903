package com.th.cms.modular.settle.settleInfluencerOrder.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.CollectionUtils;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.enums.AccountBizType;
import com.th.cms.modular.enums.AgencyRevenueEnum;
import com.th.cms.modular.enums.InflOrderStatus;
import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import com.th.cms.modular.influcer.bizInflucer.service.BizInflucerService;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.settle.settleAccount.model.SettleAccount;
import com.th.cms.modular.settle.settleAccount.model.SettlePlatAccount;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountLogService;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountService;
import com.th.cms.modular.settle.settleAccount.service.SettlePlatAccountService;
import com.th.cms.modular.settle.settleBatch.model.SettleBatch;
import com.th.cms.modular.settle.settleBatch.service.SettleBatchService;
import com.th.cms.modular.settle.settleInfluencerOrder.dao.SettleInfluencerOrderMapper;
import com.th.cms.modular.settle.settleInfluencerOrder.model.SettleInflucerOrderRsp;
import com.th.cms.modular.settle.settleInfluencerOrder.model.SettleInfluencerOrder;
import com.th.cms.modular.settle.settleInfluencerOrder.model.reqparam.SettleInfluencerOrderListParam;
import com.th.cms.modular.settle.settleOrder.service.SettleOrderExtService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 达人结算订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettleInfluencerOrderService extends ServiceImpl<SettleInfluencerOrderMapper, SettleInfluencerOrder> implements IService<SettleInfluencerOrder> {
    @Autowired
    BizInflucerService bizInflucerService;
    @Autowired
    SettleBatchService settleBatchService;
    @Autowired
    SettleProjectsService settleProjectsService;
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(SettleInfluencerOrderListParam param) {
        SettleBatch settleBatch = settleBatchService.getById(param.getBatchId());
        String companyTalentId = settleBatch.getCompanyTalentRatio();
        SettleProjects settleProjects = settleProjectsService.getById(settleBatch.getProjectId());

        QueryWrapper<SettleInfluencerOrder> wrapper = new QueryWrapper<>();
        // 字段投影
        wrapper.select(
                "influencer_id"
        );
        // 查询条件
        wrapper.eq("settlement_batch", settleBatch.getBatchNo());
        // 分组配置
        wrapper.groupBy(
                "influencer_id"
        );

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,wrapper);

        List<SettleInfluencerOrder> settleInfluencerOrders = layuiPageInfo.getData();

        Set<String> inflIds = settleInfluencerOrders.stream().map(t->t.getInfluencerId()).collect(Collectors.toSet());
        List<SettleInfluencerOrder> settleInfluencerOrdersAl =  lambdaQuery()
                .eq(SettleInfluencerOrder::getSettlementBatch,settleBatch.getBatchNo())
                .in(SettleInfluencerOrder::getInfluencerId,inflIds).list();


        List<SettleInfluencerOrder> settleInfluencerOrdersCal = queryCalDrenInfcOrders(settleInfluencerOrdersAl);
        List<BizInflucer> inflList = bizInflucerService.lambdaQuery().in(BizInflucer::getId,inflIds).list();

        Map<Long, BizInflucer> infsMap = inflList.stream().collect(Collectors.toMap(BizInflucer::getId,t->t));

        List<SettleInflucerOrderRsp> settleInflucerOrderRsps = new ArrayList<>();
        if(inflIds.size()>0){
            for(SettleInfluencerOrder settleInfluencerOrder : settleInfluencerOrdersCal){
                SettleInflucerOrderRsp settleInflucerOrderRsp = new SettleInflucerOrderRsp();
                settleInflucerOrderRsp.setInfluencerOrder(settleInfluencerOrder);
                settleInflucerOrderRsp.setBizInflucer(infsMap.get(settleInfluencerOrder.getInfluencerId()));
                settleInflucerOrderRsp.setSettleBatch(settleBatch);
                settleInflucerOrderRsp.setSettleProjects(settleProjects);
                settleInflucerOrderRsps.add(settleInflucerOrderRsp);
            }
        }

        layuiPageInfo.setData(settleInflucerOrderRsps);
        return layuiPageInfo;

    }
    @Autowired
    SettleOrderExtService settleOrderExtService;

    public List<SettleInfluencerOrder> queryCalDrenInfcOrders(List<SettleInfluencerOrder> settleInfluencerOrders){
        Map<String, List<SettleInfluencerOrder>> setBachMap = CollectionUtils.groupByKeyGeneric(settleInfluencerOrders, SettleInfluencerOrder::getInfluencerId);

        List<SettleInfluencerOrder> inoRsp = new ArrayList<>();
        for(String infId : setBachMap.keySet()){
            List<SettleInfluencerOrder> influencerOrders = setBachMap.get(infId);
            SettleInfluencerOrder settleInfluencerOrder = influencerOrders.get(0);
            settleOrderExtService.calculateTotals(null,null,settleInfluencerOrder, influencerOrders);
            inoRsp.add(settleInfluencerOrder);
        }
        return inoRsp;
    }
    public void batchInsert(List<SettleInfluencerOrder> settleInfluencerOrderList){
        this.baseMapper.batchInsert(settleInfluencerOrderList);
    }

    public SettleInfluencerOrder queryInflOrderForUpd(Integer id){
        SettleInfluencerOrder settleInfluencerOrder = this.baseMapper.selectForUpdateExplicit(id);
        return settleInfluencerOrder;
    }
    @Autowired
    SettleAccountService settleAccountService;
    @Autowired
    SettleAccountLogService settleAccountLogService;
    @Autowired
    SettlePlatAccountService settlePlatAccountService;
    @Transactional
    public void  settleInflOrder(SettleInfluencerOrder settleInfluencerOrder, BizPlatform bizPlatform){
        log.info("开始结算 "+settleInfluencerOrder.getInfluencerName()+" 金额 "+settleInfluencerOrder.getPayableAmount());

        SettleProjects settleProjects = settleProjectsService.getById(settleInfluencerOrder.getProjectId());
        AgencyRevenueEnum agencyRevenueEnum = AgencyRevenueEnum.fromCode(settleProjects.getAgencyRevenue());
        if(AgencyRevenueEnum.you.equals(agencyRevenueEnum)){
            SettleInfluencerOrder settleInfluencerOrderRd = new SettleInfluencerOrder();
            settleInfluencerOrderRd.setId(settleInfluencerOrder.getId());
            settleInfluencerOrderRd.setInflStatusName(InflOrderStatus.yifangkuan.getName());
            settleInfluencerOrderRd.setInflStatus(InflOrderStatus.yifangkuan.getCode());
            settleInfluencerOrderRd.setIncomeTime(new Date());
            updateById(settleInfluencerOrderRd);
            log.info("机构结算完成 ");
        }else{
            SettleInfluencerOrder settleInfluencerOrderDb = queryInflOrderForUpd(settleInfluencerOrder.getId());
            BigDecimal paAmt = settleInfluencerOrderDb.getPayableAmount();
            if(paAmt!=null&&paAmt.doubleValue()>0){
                SettleAccount settleAccount = settleAccountService.queryAccForUpd(settleInfluencerOrderDb.getInfluencerId());
                if(settleAccount==null){
                    //添加账户完成
                    BizInflucer bizInflucer = bizInflucerService.getById(settleInfluencerOrderDb.getInfluencerId());
                    if(bizInflucer!=null){
                        settleAccountService.addSettleAc(bizInflucer);
                    }else {
                        log.error(settleInfluencerOrderDb.getInfluencerId()+"无 达人 无法结算 该笔业务");
                    }
                }

                settleAccount = settleAccountService.queryAccForUpd(settleInfluencerOrderDb.getInfluencerId());
                settleAccountLogService.addAccLog(settleInfluencerOrder,settleAccount,paAmt,bizPlatform, AccountBizType.Jiesuan);
                settleAccountService.addAmt(settleAccount.getId(),paAmt);

                SettleInfluencerOrder settleInfluencerOrderRd = new SettleInfluencerOrder();
                settleInfluencerOrderRd.setId(settleInfluencerOrder.getId());
                settleInfluencerOrderRd.setInflStatusName(InflOrderStatus.yifangkuan.getName());
                settleInfluencerOrderRd.setInflStatus(InflOrderStatus.yifangkuan.getCode());
                settleInfluencerOrderRd.setIncomeTime(new Date());
                updateById(settleInfluencerOrderRd);

                SettlePlatAccount settlePlatAccount = settlePlatAccountService.queryAddAccount(bizPlatform,settleAccount);
                //
                settlePlatAccountService.addAmt(settlePlatAccount.getId(),paAmt);

                log.info("达人结算完成 "+settleInfluencerOrderDb.getInfluencerName());
            }else{
                log.warn("结算金额为空 "+paAmt);
            }
        }

    }
}
