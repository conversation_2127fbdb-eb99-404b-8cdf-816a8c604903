package com.th.cms.modular.workorder.bizWorkorder.model;

public enum WorkSourceType {
    /**
     * 2: 事故维修
     */
    daren("1", "达人"),
    /**
     * 3: 维修
     */
    kefu("2", "客服"),
    ;

    public String value;
    public String name;

    WorkSourceType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static WorkSourceType getType(Integer value) {
        return getType(value + "");
    }

    public static WorkSourceType getType(String value) {
        WorkSourceType[] carLeaveTypeList = WorkSourceType.values();
        for (WorkSourceType carLeaveType : carLeaveTypeList) {
            if (carLeaveType.value.equals(value)) {
                return carLeaveType;
            }
        }
        return null;
    }
}
