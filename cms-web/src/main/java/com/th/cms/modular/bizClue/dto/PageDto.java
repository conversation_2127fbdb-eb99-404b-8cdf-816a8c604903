package com.th.cms.modular.bizClue.dto;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;

import java.util.List;

@Data
public class PageDto<T> implements IPage<T> {

    private long current = 1L;

    private long size = 10L;

    private List<T> data;

    private long count;

    @Override
    public List getRecords() {
        return data;
    }

    @Override
    public IPage setRecords(List records) {
        this.data = (List<T>) records;
        return this;
    }

    @Override
    public long getTotal() {
        return count;
    }

    @Override
    public IPage setTotal(long total) {
        this.count = total;
        return this;
    }

    @Override
    public long getSize() {
        return this.size;
    }

    @Override
    public IPage setSize(long size) {
        this.size = size;
        return this;
    }

    @Override
    public long getCurrent() {
        return this.current;
    }

    @Override
    public IPage setCurrent(long current) {
        this.current = current;
        return this;
    }
}
