package com.th.cms.modular.settle.settleCore.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.th.cms.modular.settle.settleBatch.plat.pdd.CommonSettleFiledModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TaoBaoNeiRongDaiHuiSettleExcel {
    @ExcelProperty(value = "达人昵称")
    private String name;

    @ExcelProperty(value = "机构分账金额")
    private BigDecimal amount;
    
    public CommonSettleFiledModel buildCommonSettleFiledModel() {
        CommonSettleFiledModel model = new CommonSettleFiledModel();
        model.setInfluencerNickname(name);
        model.setRebateAmount(amount);
        return model;
    }
}
