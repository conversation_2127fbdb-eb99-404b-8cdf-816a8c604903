package com.th.cms.modular.im.imGroupUser.model;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="ImGroupUser对象", description="")
public class ImGroupUser implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "ID")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "群昵称")
    @TableField("nick_name")
    private String nickName;

    @ApiModelProperty(value = "用户accid")
    @TableField("accid")
    private String accid;

    @ApiModelProperty(value = "用户类型")
    @TableField("user_type")
    private Integer userType;

    @ApiModelProperty(value = "类型名称")
    @TableField("user_typename")
    private String userTypename;

    @ApiModelProperty(value = "群ID")
    @TableField("gid")
    private Long gid;

    @ApiModelProperty(value = "群名称")
    @TableField("gname")
    private String gname;

    @TableField("mute")
    private Integer mute;

    @ApiModelProperty(value = "移除标识")
    @TableField("is_remove")
    private Integer isRemove;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ExcelField(title="ID",dictType="", align=2, sort=0)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @ExcelField(title="用户ID",dictType="", align=2, sort=1)
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    @ExcelField(title="群昵称",dictType="", align=2, sort=2)
    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
    @ExcelField(title="用户accid",dictType="", align=2, sort=3)
    public String getAccid() {
        return accid;
    }

    public void setAccid(String accid) {
        this.accid = accid;
    }
    @ExcelField(title="用户类型",dictType="", align=2, sort=4)
    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }
    @ExcelField(title="类型名称",dictType="", align=2, sort=5)
    public String getUserTypename() {
        return userTypename;
    }

    public void setUserTypename(String userTypename) {
        this.userTypename = userTypename;
    }
    @ExcelField(title="群ID",dictType="", align=2, sort=6)
    public Long getGid() {
        return gid;
    }

    public void setGid(Long gid) {
        this.gid = gid;
    }
    @ExcelField(title="群名称",dictType="", align=2, sort=7)
    public String getGname() {
        return gname;
    }

    public void setGname(String gname) {
        this.gname = gname;
    }
    @ExcelField(title="",dictType="", align=2, sort=8)
    public Integer getMute() {
        return mute;
    }

    public void setMute(Integer mute) {
        this.mute = mute;
    }
    @ExcelField(title="移除标识",dictType="", align=2, sort=9)
    public Integer getIsRemove() {
        return isRemove;
    }

    public void setIsRemove(Integer isRemove) {
        this.isRemove = isRemove;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=10)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=11)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ImGroupUser{" +
        "id=" + id +
        ", userId=" + userId +
        ", nickName=" + nickName +
        ", accid=" + accid +
        ", userType=" + userType +
        ", userTypename=" + userTypename +
        ", gid=" + gid +
        ", gname=" + gname +
        ", mute=" + mute +
        ", isRemove=" + isRemove +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
