package com.th.cms.modular.influcer.bizPlatform.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.th.cms.core.common.annotion.Permission;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.modular.influcer.bizInflucer.model.enums.PlatformAuthType;
import com.th.cms.modular.influcer.bizInflucer.model.enums.PlatformType;
import com.th.cms.modular.influcer.bizPlatform.dto.BizPlatformAddRequestDTO;
import com.th.cms.modular.influcer.bizPlatform.dto.BizPlatformDetailDTO;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.model.reqparam.BizPlatformListParam;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformExtService;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformService;
import com.th.cms.modular.oss.util.OssConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;

/**
 * 平台设置控制器
 *
 * <AUTHOR>
 * @Date 2025-03-26 11:15:22
 */
@Controller
@RequestMapping("/bizPlatform")
public class BizPlatformController extends BaseController {

    private String PREFIX = "/modular/bizPlatform/bizPlatform/";
    @Autowired
    BizPlatformExtService bizPlatformExtService;
    @Autowired
    private BizPlatformService bizPlatformService;

    /**
     * 跳转到平台设置首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "bizPlatform.html";
    }

    /**
     * 跳转到添加平台设置
     */
    @Permission
    @RequestMapping("/bizPlatform_add")
    public String bizPlatformAdd() {
        return PREFIX + "bizPlatform_add.html";
    }

    /**
     * 跳转到修改平台设置
     */
    @Permission
    @RequestMapping("/bizPlatform_update")
    public String bizPlatformUpdate(@RequestParam Integer bizPlatformId, Model model) {
        BizPlatform bizPlatform = bizPlatformExtService.queryById(bizPlatformId);
        model.addAttribute("item", bizPlatform);
        LogObjectHolder.me().set(bizPlatform);
        return PREFIX + "bizPlatform_edit.html";
    }

    @RequestMapping("/bizPlatform_detail")
    public String bizPlatformDetail(@RequestParam Integer bizPlatformId, Model model) {
        BizPlatform bizPlatform = bizPlatformExtService.queryById(bizPlatformId);
        model.addAttribute("item", bizPlatform);
        LogObjectHolder.me().set(bizPlatform);
        return PREFIX + "bizPlatform_detail.html";
    }

    /**
     * 获取平台设置列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public ResponseData list(BizPlatformListParam bizPlatformParam) {
        List<BizPlatform> dlist = null;
        if (bizPlatformParam.getCengji() != null && bizPlatformParam.getCengji() > 0) {
            dlist = bizPlatformService.lambdaQuery().eq(BizPlatform::getLevels, bizPlatformParam.getCengji()).list();
        }
        if (bizPlatformParam.getCooperation() != null) {
            dlist = bizPlatformService.lambdaQuery().gt(BizPlatform::getLevels, 1).orderByDesc(BizPlatform::getId).list();
        } else {
            dlist = bizPlatformService.list();
        }
        dlist.forEach(bizPlatform -> {
            bizPlatform.setIconPic(OssConfig.hostPre + bizPlatform.getIconPic());
        });
        return ResponseData.success(dlist);
    }

    /**
     * 获取平台设置列表
     */
    @RequestMapping(value = "/authList")
    @ResponseBody
    public ResponseData authList() {

        List<BizPlatform> platforms = new ArrayList<>();
        PlatformType[] values = PlatformType.values();

        for (PlatformType value : values) {
            BizPlatform bizPlatform = new BizPlatform();
            bizPlatform.setPlatName(value.name);
            bizPlatform.setIconPic(value.image);
            platforms.add(bizPlatform);
        }
        return ResponseData.success(platforms);
    }


    @RequestMapping(value = "/listPage")
    @ResponseBody
    public Object listPage(BizPlatformListParam bizPlatformParam) {
        return bizPlatformService.findPageBySpec(bizPlatformParam);
    }


    @RequestMapping("/listtree")
    @ResponseBody
    public Object getTree(String pid) {
        if (StringUtils.isBlank(pid)) {
            pid = "0";
        }
        return bizPlatformExtService.getPlatformTree(pid);
    }

    /**
     * 新增平台设置
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/bizPlatform/bizPlatform_add")
    @ResponseBody
    public ResponseData add(BizPlatformAddRequestDTO bizPlatform) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            return ResponseData.error("未登录");
        }
        bizPlatformExtService.save(bizPlatform, user);

        return ResponseData.success();
    }

    /**
     * 删除平台设置
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer bizPlatformId) {
        bizPlatformExtService.removeById(bizPlatformId);
        return ResponseData.success();
    }

    /**
     * 修改平台设置
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/bizPlatform/bizPlatform_update")
    @ResponseBody
    public ResponseData update(BizPlatformAddRequestDTO bizPlatform) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            return ResponseData.error("未登录");
        }
        bizPlatformExtService.save(bizPlatform, user);
        return ResponseData.success();
    }

    /**
     * 平台设置详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public ResponseData<BizPlatformDetailDTO> detail(@RequestParam Integer bizPlatformId) {
        BizPlatformDetailDTO detail = bizPlatformExtService.detail(bizPlatformId);
        return ResponseData.success(detail);
    }
}
