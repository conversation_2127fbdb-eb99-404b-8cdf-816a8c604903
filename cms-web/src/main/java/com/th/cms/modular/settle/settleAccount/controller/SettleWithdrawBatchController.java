package com.th.cms.modular.settle.settleAccount.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.settle.settleAccount.model.SettleWithdrawBatch;
import com.th.cms.modular.settle.settleAccount.service.SettleWithdrawBatchService;
import com.th.cms.modular.settle.settleAccount.service.SettleWithdrawBatchExtService;
import com.th.cms.modular.settle.settleAccount.model.reqparam.SettleWithdrawBatchListParam;

/**
 * 提现管理控制器
 *
 * <AUTHOR>
 * @Date 2025-04-18 18:44:50
 */
@Controller
@RequestMapping("/settleWithdrawBatch")
public class SettleWithdrawBatchController extends BaseController {

    private String PREFIX = "/modular/settleAccount/settleWithdrawBatch/";
    @Autowired
    SettleWithdrawBatchExtService settleWithdrawBatchExtService;
    @Autowired
    private SettleWithdrawBatchService settleWithdrawBatchService;

    /**
     * 跳转到提现管理首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "settleWithdrawBatch.html";
    }

    /**
     * 跳转到添加提现管理
     */
    @Permission
    @RequestMapping("/settleWithdrawBatch_add")
    public String settleWithdrawBatchAdd() {
        return PREFIX + "settleWithdrawBatch_add.html";
    }

    /**
     * 跳转到修改提现管理
     */
    @Permission
    @RequestMapping("/settleWithdrawBatch_update")
    public String settleWithdrawBatchUpdate(@RequestParam  Integer settleWithdrawBatchId, Model model) {
        SettleWithdrawBatch settleWithdrawBatch = settleWithdrawBatchExtService.queryById(settleWithdrawBatchId);
        model.addAttribute("item",settleWithdrawBatch);
        LogObjectHolder.me().set(settleWithdrawBatch);
        return PREFIX + "settleWithdrawBatch_edit.html";
    }
    @RequestMapping("/settleWithdrawBatch_detail")
    public String settleWithdrawBatchDetail(@RequestParam Integer settleWithdrawBatchId, Model model) {
        SettleWithdrawBatch settleWithdrawBatch = settleWithdrawBatchExtService.queryById(settleWithdrawBatchId);
        model.addAttribute("item",settleWithdrawBatch);
        LogObjectHolder.me().set(settleWithdrawBatch);
        return PREFIX + "settleWithdrawBatch_detail.html";
    }
    /**
     * 获取提现管理列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(SettleWithdrawBatchListParam settleWithdrawBatchParam) {
        return settleWithdrawBatchService.findPageBySpec(settleWithdrawBatchParam);
    }

    /**
     * 新增提现管理
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/settleWithdrawBatch/settleWithdrawBatch_add")
    @ResponseBody
    public ResponseData add(SettleWithdrawBatch settleWithdrawBatch) {
         settleWithdrawBatchExtService.save(settleWithdrawBatch);
         return ResponseData.success();
    }

    /**
     * 删除提现管理
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  settleWithdrawBatchId) {
        settleWithdrawBatchExtService.removeById(settleWithdrawBatchId);
         return ResponseData.success();
    }

    /**
     * 修改提现管理
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/settleWithdrawBatch/settleWithdrawBatch_update")
    @ResponseBody
    public ResponseData update(SettleWithdrawBatch settleWithdrawBatch) {
        settleWithdrawBatchExtService.updateById(settleWithdrawBatch);
        return ResponseData.success();
    }

    /**
     * 提现管理详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  settleWithdrawBatchId) {
       SettleWithdrawBatch detail = settleWithdrawBatchExtService.queryById(settleWithdrawBatchId);
       return ResponseData.success(detail);
    }
}
