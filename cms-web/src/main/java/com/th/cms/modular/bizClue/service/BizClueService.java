package com.th.cms.modular.bizClue.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.modular.bizClue.dto.TakeClueDto;
import com.th.cms.modular.bizClue.entity.BizClue;
import com.th.cms.modular.bizClue.mapper.BizClueMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BizClueService extends ServiceImpl<BizClueMapper, BizClue> implements IService<BizClue> {




    @Autowired
    private BizClueMapper bizClueMapper;

    public List<BizClue> takeList(TakeClueDto dto){
        return bizClueMapper.takeList(dto);
    }

    public Integer leftTakeCount(){
        return bizClueMapper.leftTakeCount();
    }

    public void countUp(List<Long> clueIdList) {
        bizClueMapper.countUp(clueIdList);
    }

    public void countDown(List<Long> clueIdList) {
        bizClueMapper.countDown(clueIdList);
    }

}
