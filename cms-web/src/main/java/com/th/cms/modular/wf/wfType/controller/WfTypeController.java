package com.th.cms.modular.wf.wfType.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.annotion.Permission;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.modular.enums.ApprovalBillType;
import com.th.cms.modular.enums.ApprovalStatus;
import com.th.cms.modular.wf.wfStep.model.ApproveType;
import com.th.cms.modular.wf.wfStep.model.AssignPermissonType;
import com.th.cms.modular.wf.wfStep.model.AssignType;
import com.th.cms.modular.wf.wfStep.model.StepNodeType;
import com.th.cms.modular.wf.wfStepRelation.model.WfStepRelation;
import com.th.cms.modular.wf.wfStepRelation.service.WfStepRelationExtService;
import com.th.cms.modular.wf.wfStepRelation.service.WfStepRelationService;
import com.th.cms.modular.wf.wfType.model.*;
import com.th.cms.modular.wf.wfType.model.reqparam.WfTypeListParam;
import com.th.cms.modular.wf.wfType.service.WfTypeExtService;
import com.th.cms.modular.wf.wfType.service.WfTypeService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程类型控制器
 *
 * <AUTHOR>
 * @Date 2025-03-27 11:37:29
 */
@Controller
@RequestMapping("/wfType")
public class WfTypeController extends BaseController {

    private String PREFIX = "/modular/wfType/wfType/";
    @Autowired
    WfTypeExtService wfTypeExtService;
    @Autowired
    private WfTypeService wfTypeService;

    /**
     * 跳转到流程类型首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "userWfType.html";
    }

    @RequestMapping("/simple")
    public String simpleIndex() {
        return PREFIX + "simpleWfType.html";
    }

    /**
     * 跳转到添加流程类型
     */
    @Permission
    @RequestMapping("/wfType_add")
    public String wfTypeAdd() {
        return PREFIX + "wfType_add.html";
    }

    /**
     * 跳转到修改流程类型
     */
    @Permission
    @RequestMapping("/wfType_update")
    public String wfTypeUpdate(@RequestParam Integer wfTypeId, Model model) {
        WfType wfType = wfTypeExtService.queryById(wfTypeId);
        model.addAttribute("item", wfType);
        LogObjectHolder.me().set(wfType);
        return PREFIX + "wfType_edit.html";
    }

    @RequestMapping("/wfType_detail")
    public String wfTypeDetail(@RequestParam Integer wfTypeId, Model model) {
        WfType wfType = wfTypeExtService.queryById(wfTypeId);
        model.addAttribute("item", wfType);
        LogObjectHolder.me().set(wfType);
        return PREFIX + "wfType_detail.html";
    }

    /**
     * 可以使用的审批流
     */
    @RequestMapping(value = "/flowList")
    @ResponseBody
    public ResponseData flowList() {
        return ResponseData.success(wfTypeService.flowList());
    }


    /**
     * 获取流程类型列表
     */
    //TODO 权限，
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(WfTypeListParam wfTypeParam) {

        wfTypeParam.setClassifyType(ApprovalBillType.SubmitApprove.getClassifyName());

        return wfTypeService.findPageBySpec(wfTypeParam);
    }

    @RequestMapping(value = "/modifyApproveFlow")
    @ResponseBody
    public ResponseData modifyApproveFlow(@RequestBody WfType dto) {
        WfTypeStepVO vo = wfTypeService.modifyApproveFlow(dto);
        return ResponseData.success(vo);
    }

    @RequestMapping(value = "/stepType")
    @ResponseBody
    public ResponseData stepType() {

        StepNodeType[] values = StepNodeType.values();
        List<StepTypeVO> vos = new ArrayList<>();
        for (StepNodeType value : values) {
            vos.add(StepTypeVO.builder().name(value.getName()).value(value.getCode()).build());
        }

        return ResponseData.success(vos);
    }

    @RequestMapping(value = "/approveType")
    @ResponseBody
    public ResponseData approveType() {

        ApproveType[] values = ApproveType.values();
        List<StepTypeVO> vos = new ArrayList<>();
        for (ApproveType value : values) {
            vos.add(StepTypeVO.builder().name(value.getName()).value(value.getCode()).build());
        }

        return ResponseData.success(vos);
    }

    @RequestMapping(value = "/assignPermission")
    @ResponseBody
    public ResponseData assignPermission() {

        AssignPermissonType[] values = AssignPermissonType.values();
        List<StepTypeVO> vos = new ArrayList<>();
        for (AssignPermissonType value : values) {
            vos.add(StepTypeVO.builder().name(value.getName()).value(value.getCode()).build());
        }

        return ResponseData.success(vos);
    }

    @RequestMapping(value = "/assignType")
    @ResponseBody
    public ResponseData assignType() {

        AssignType[] values = AssignType.values();
        List<StepTypeVO> vos = new ArrayList<>();
        for (AssignType value : values) {
            vos.add(StepTypeVO.builder().name(value.name).value(value.code).build());
        }

        return ResponseData.success(vos);
    }

    @RequestMapping(value = "/approvalStatus")
    @ResponseBody
    public ResponseData approvalStatus() {

        ApprovalStatus[] values = ApprovalStatus.values();
        List<StepTypeVO> vos = new ArrayList<>();
        for (ApprovalStatus value : values) {
            vos.add(StepTypeVO.builder().name(value.getDescription()).value(value.getCode()).build());
        }

        return ResponseData.success(vos);
    }

    @RequestMapping(value = "/approvalBillType")
    @ResponseBody
    public ResponseData approvalBillType() {

        ApprovalBillType[] values = ApprovalBillType.values();
        List<StepTypeVO> vos = new ArrayList<>();
        for (ApprovalBillType value : values) {
            vos.add(StepTypeVO.builder().name(value.getName()).value(value.getName()).build());
        }
        return ResponseData.success(vos);
    }

    /**
     * 添加审批流节点 WfTypeStepDTO
     *
     * @param stepDTO
     * @return
     */
    @RequestMapping(value = "/modifyApproveStep")
    @ResponseBody
    public ResponseData addApproveStep(@RequestBody WfTypeStepDTO stepDTO) {
        WfTypeStepVO vo = wfTypeService.modifyApproveStep(stepDTO);
        return ResponseData.success(vo);
    }

    /**
     * 删除节点
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "/delApproveStep")
    @ResponseBody
    public ResponseData delApproveStep(@RequestParam Long id) {
        return ResponseData.success(wfTypeService.delApproveStep(id));
    }


    /**
     * 自定义审批-提交审批-需要创建审批流的待审批记录
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/submitApprove")
    @ResponseBody
    public ResponseData submitApprove(@RequestParam Long id) {
        return ResponseData.success(wfTypeService.submitApprove(id));
    }


    /**
     * 获取流程类型列表
     */
    @RequestMapping(value = "/simpleList")
    @ResponseBody
    public LayuiPageInfo simpleList(WfTypeListParam wfTypeParam) {

        wfTypeParam.setClassifyType(ApprovalBillType.SettleOrder.getClassifyName());
        return wfTypeService.findPageBySpec(wfTypeParam);
    }

    /**
     * 高级审批里-提交审批
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/simpleSubmit")
    @ResponseBody
    public ResponseData simpleSubmit(@RequestParam Long id) {
        return ResponseData.success(wfTypeService.simpleSubmit(id));
    }

    /**
     * 审批流详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/getApprove")
    @ResponseBody
    public ResponseData getApproveDetail(@RequestParam Long id, @RequestParam(required = false) String requestId) {
        return ResponseData.success(wfTypeService.getApproveDetail(id, requestId));
    }

    /**
     * 启用-禁用
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/enableApprove")
    @ResponseBody
    public ResponseData enableApprove(@RequestParam Long id) {

        WfType wfType = wfTypeService.getById(id);
        if (wfType != null) {

            if (wfType.getIsActive() != null && wfType.getIsActive() == 1) {
                wfType.setIsActive(0);
            } else {
                wfType.setIsActive(1);
            }
            wfTypeService.updateById(wfType);
            return ResponseData.success("状态转换完成");
        }

        return ResponseData.error("无效的数据信息！");
    }


    /**
     * 删除审批流
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/delApprove")
    @ResponseBody
    public ResponseData delApprove(@RequestParam Long id) {

        String message = wfTypeService.delApprove(id);

        if (!StringUtils.isEmpty(message)) {
            return ResponseData.error(message);
        }
        return ResponseData.success();
    }


    /**
     * 添加审批流 WfTypeDTO
     *
     * @param dto
     * @return
     */
    @RequestMapping(value = "/addApprove")
    @ResponseBody
    public ResponseData addApprove(@RequestBody WfTypeDTO dto) {
        Long id = wfTypeService.addApprove(dto);
        return ResponseData.success(id);
    }

    /**
     * 编辑审批流
     *
     * @param wfType
     * @return
     */
    @RequestMapping(value = "/editApprove")
    @ResponseBody
    public ResponseData editApprove(@RequestBody WfType wfType) {
        return ResponseData.success(wfTypeService.editApprove(wfType));
    }


    /**
     * 新增流程类型
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/wfType/wfType_add")
    @ResponseBody
    public ResponseData add(WfType wfType) {
        wfTypeExtService.save(wfType);
        return ResponseData.success();
    }

    /**
     * 删除流程类型
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer wfTypeId) {
        wfTypeExtService.removeById(wfTypeId);
        return ResponseData.success();
    }

    /**
     * 修改流程类型
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/wfType/wfType_update")
    @ResponseBody
    public ResponseData update(WfType wfType, String isCk) {
        wfTypeExtService.updateById(wfType);
        return ResponseData.success();
    }

    /**
     * 流程类型详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer wfTypeId) {
        WfType detail = wfTypeExtService.queryById(wfTypeId);
        return ResponseData.success(detail);
    }

    @Autowired
    WfStepRelationService wfStepRelationService;

    /**
     * 审批流节点
     *
     * @param wfTypeId
     * @param model
     * @return
     */
    @RequestMapping("/spliuAddUpd")
    public String spliuAddUpd(@RequestParam Integer wfTypeId, Model model) {
        if (wfTypeId != null) {
            model.addAttribute("shpilg", 1);
            WfType wfType = wfTypeExtService.queryById(wfTypeId);
            //查询所有节点
            List<WfStepRelation> wfStepRelationList = wfStepRelationService.queryAllRelation(wfTypeId);
            model.addAttribute("wfStepRelationList", wfStepRelationList);
            model.addAttribute("wfType", wfType);
            LogObjectHolder.me().set(wfType);
        } else {
            model.addAttribute("shpilg", 2);
        }
        model.addAttribute("wfTypeId", wfTypeId);
        return PREFIX + "shenpi.html";
    }


    @RequestMapping(value = "/wfdetail")
    @ResponseBody
    public Object wfdetail(@RequestParam Integer wfTypeId) {
        Map<String, Object> dmap = new HashMap<>();
        WfType detail = wfTypeExtService.queryById(wfTypeId);
        dmap.put("wftype", detail);

        List<WfStepRelation> wfStepRelationList = wfStepRelationService.queryAllRelation(wfTypeId);
        dmap.put("wfStepRelationList", wfStepRelationList);
        return ResponseData.success(dmap);
    }


    @RequestMapping("/wfTypeAddHt")
    public String wfTypeAddHt(@RequestParam Integer wfTypeId, Model model) {
        WfType wfType = wfTypeExtService.queryById(wfTypeId);
        model.addAttribute("wfType", wfType);
        return "/modular/wfType/wfType/wfStepNodeAdd.html";
    }

    @Autowired
    WfStepRelationExtService wfStepRelationExtService;

    @RequestMapping(value = "/deleteNode")
    @ResponseBody
    public ResponseData deleteNode(@RequestParam Integer wfStepRelationId) {
        wfStepRelationExtService.removeById(wfStepRelationId);
        return ResponseData.success();
    }
}
