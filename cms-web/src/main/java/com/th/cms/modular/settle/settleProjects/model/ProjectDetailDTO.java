package com.th.cms.modular.settle.settleProjects.model;

import com.th.cms.modular.settle.settleProjects.model.reqparam.SettleProjectFieldRequestDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProjectDetailDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 序号
     */
    private Integer serialNumber;

    /**
     * 项目简称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目类型ID
     */
    private String projectTypeId;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 标准分成比例（公司/达人）
     */
    private String commissionRate;

    /**
     * 分成比例ID
     */
    private String commissionRateId;

    /**
     * 达人分成
     */
    private Double commissionToDaren;

    /**
     * 公司分成
     */
    private Double commissionToComp;

    /**
     * 收益类型
     */
    private String revenueType;

    /**
     * 收益类型名称
     */
    private String revenueTypeName;

    /**
     * 平台名称
     */
    private String platLstName;

    /**
     * 审批流id
     */
    private Long flowId;
    /**
     * 平台ID
     */
    private String platformId;

    /**
     * 服务平台
     */
    private String platform;

    /**
     * 一级平台
     */
    private String yijiPlatform;

    /**
     * 二级平台
     */
    private String erjiPlatform;

    /**
     * 三级平台
     */
    private String sanjiPlatform;

    /**
     * 量化目标
     */
    private String quantTarget;

    /**
     * 达人结算周期
     */
    private String settlementCycle;

    /**
     * 结算周期名称
     */
    private String settlementCycleName;

    /**
     * 立项时间
     */
    private Date startDate;

    /**
     * 项目开始时间
     */
    private Date projectStarttime;

    /**
     * 项目结束时间
     */
    private Date projectEndtime;

    /**
     * 机构收益
     */
    private Integer agencyRevenue;

    /**
     * 业务负责人
     */
    private String businessOwner;

    /**
     * 业务负责人名称
     */
    private String businessOwnerName;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 状态编号
     */
    private Integer projectStatus;

    /**
     * 结算状态名称
     */
    private String projectStatusName;

    /**
     * 操作记录（存储操作日志）
     */
    private String operations;

    /**
     * 项目背景介绍
     */
    private String projectDesc;

    /**
     * 背景附件
     */
    private String beijingFile;

    /**
     * 项目执行方案与目标
     */
    private String projectZhixingFa;

    /**
     * 执行附件
     */
    private String zhixingFiles;

    /**
     * 机构名称
     */
    private String jigouName;

    /**
     * 参与公司主体
     */
    private String compName;

    /**
     * 公司主体ID
     */
    private String compNameId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private String createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 项目状态
     */
    private Integer projectStatusInt;
    /**
     * 自定义字段
     */
    private List<SettleProjectFieldRequestDTO> fieldRequest;
}
