package com.th.cms.modular.settle.settleAccount.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;
import com.th.cms.core.util.JedisClient;
import com.th.cms.core.util.ProductConst;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.util.ThreadPoolUtil;
import com.th.cms.modular.enums.BizPlatLevel;
import com.th.cms.modular.enums.InfcTopType;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.mapper.BizPlatformMapper;
import com.th.cms.modular.settle.settleAccount.consts.TopDataSource;
import com.th.cms.modular.settle.settleAccount.dao.BizInflucerTopMapper;
import com.th.cms.modular.settle.settleAccount.model.BizInflucerTop;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class BizInflucerTopExtService {
    @Autowired
    BizInflucerTopService bizInflucerTopService;
    @Autowired
    private BizInflucerTopMapper bizInflucerTopMapper;
    @Autowired
    private BizPlatformMapper bizPlatformMapper;
    @Autowired
    private JedisClient client;

    public void save(BizInflucerTop bizInflucerTop) {
        setEnumsName(bizInflucerTop);
        ShiroUtils.setAddAuthInfo(bizInflucerTop);
        bizInflucerTopService.save(bizInflucerTop);
    }

    public void updateById(BizInflucerTop bizInflucerTop) {
        setEnumsName(bizInflucerTop);
        DataAuthService.checkPermision(bizInflucerTopService.getById(bizInflucerTop.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(bizInflucerTop);
        BizInflucerTop bizInflucerTopDb = queryById(bizInflucerTop.getId());

        bizInflucerTopService.updateById(bizInflucerTop);
    }

    public BizInflucerTop queryById(Serializable id) {
        BizInflucerTop bizInflucerTop = bizInflucerTopService.getById(id);
        DataAuthService.checkPermision(bizInflucerTop, DataPermisionTypeEnum.ByComp);

        return bizInflucerTop;
    }

    public void removeById(Serializable id) {
        BizInflucerTop bizInflucerTop = bizInflucerTopService.getById(id);
        DataAuthService.checkPermision(bizInflucerTop, DataPermisionTypeEnum.ByComp);
        BizInflucerTop bizInflucerTopRecord = new BizInflucerTop();
        bizInflucerTopRecord.setId(bizInflucerTop.getId());

        bizInflucerTopService.updateById(bizInflucerTopRecord);
    }

    private void setEnumsName(BizInflucerTop bizInflucerTop) {
    }

    /**
     * 初始化榜单
     */
    public void initRankList() {
        log.info("初始化榜单");
        ThreadPoolUtil.exec(() -> {
            updateRankList(InfcTopType.zuix.getCode());
        });
        ThreadPoolUtil.exec(() -> {
            updateRankList(InfcTopType.zongbang.getCode());
        });
    }

    public void updateRankList(Integer topType) {
        String key = ProductConst.RANK_LIST_INIT_ING + "@" + topType;
        String val = client.get(key);
        if(StringUtils.isNotBlank(val)){
            log.info("updateRankList ranking {} = {}",key, val);
            return;
        }
        client.set(key, "1");
        log.info("updateRankList start {} = {}",key, val);
        LambdaQueryWrapper<BizInflucerTop> qw = Wrappers.lambdaQuery();
        qw.eq(BizInflucerTop::getTopType, topType);
        qw.eq(BizInflucerTop::getDataSource, TopDataSource.AUTO.getCode());
        bizInflucerTopMapper.delete(qw);
        long page = 1;
        long pages = 1;
        long size = 1000;
        LocalDateTime endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(0);
        LocalDateTime startTime = endTime.minusDays(30).withHour(0).withMinute(0).withSecond(0).withNano(0);
        while (page <= pages) {
            IPage<BizInflucerTop> pageData = bizInflucerTopMapper.queryInitTopList(
                    new Page<>(1, size),
                    topType, startTime, endTime);
            pages = pageData.getPages();
            page++;
            if (CollectionUtil.isNotEmpty(pageData.getRecords())) {
                List<BizInflucerTop> records = repleaceBizPlat(pageData.getRecords(),topType);
                bizInflucerTopMapper.insertBatch(records);
            }
        }
        log.info("updateRankList end {} = {}",key, val);
        client.del(key);
    }

    private List<BizInflucerTop> repleaceBizPlat(List<BizInflucerTop> records, Integer topType) {
        List<BizInflucerTop> list = Lists.newArrayList();
        for (BizInflucerTop record : records) {
            Integer platId = record.getPlatId();
            record.setTopType(topType);
            BizPlatform bizPlatform = queryTwoPlatform(platId);
            if (bizPlatform == null||bizPlatform.getId()==0) {
                continue;
            }
            record.setPlatName(bizPlatform.getPlatName());
            record.setPlatId(bizPlatform.getId());
            record.setDataSource(TopDataSource.AUTO.getCode());
            list.add(record);

        }
        return list;
    }

    private BizPlatform queryTwoPlatform(Integer platId) {
        String key = "BizPlatform"+ "@" + platId;
        String s = client.get(key);
        if (StringUtils.isNotBlank(s)) {
            return JSONObject.parseObject(s, BizPlatform.class);
        }
        /**
         * 立项限制了，不可能是一级，不考虑一级得数据
         */
        BizPlatform bizPlatform = bizPlatformMapper.selectById(platId);
        if (bizPlatform == null) {
            putRedis(key, zeroIdPlatform());
            return null;
        }
        if (bizPlatform.getLevels()== BizPlatLevel.ONE.getCode()) {
            putRedis(key, zeroIdPlatform());
            return null;
        }
        if (bizPlatform.getLevels()== BizPlatLevel.TWO.getCode()) {
            putRedis(key, bizPlatform);
            return bizPlatform;
        }
        if (bizPlatform.getLevels()== BizPlatLevel.THREE.getCode()) {
            bizPlatform = queryTwoPlatformByThree(bizPlatform);
            putRedis(key, bizPlatform);
            return bizPlatform;
        }
        putRedis(key, zeroIdPlatform());
        return null;
    }

    /**
     * 避免空值一直读库
     * @return
     */
    private BizPlatform zeroIdPlatform() {
        BizPlatform bizPlatform = new BizPlatform();
        bizPlatform.setId(0);
        return bizPlatform;
    }

    private void putRedis(String key, BizPlatform bizPlatform) {
        client.set(key, JSONObject.toJSONString(bizPlatform));
        client.expire(key, 300);
    }

    private BizPlatform queryTwoPlatformByThree(BizPlatform bizPlatform) {
        LambdaQueryWrapper<BizPlatform> qw = Wrappers.lambdaQuery();
        qw.eq(BizPlatform::getLevels, BizPlatLevel.TWO.getCode());
        qw.eq(BizPlatform::getId, bizPlatform.getParentId());
        List<BizPlatform> list = bizPlatformMapper.selectList(qw);
        return CollectionUtil.isNotEmpty(list) ? list.get(0) : null;
    }
}
