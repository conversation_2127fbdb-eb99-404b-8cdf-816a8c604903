package com.th.cms.modular.wf.wfApprovalRecord.service;

import com.th.cms.modular.enums.ApprovalStatus;
import com.th.cms.modular.wf.wfApprovalRecord.model.WfApprovalRecord;

import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

@Service
public class WfApprovalRecordExtService {
    @Autowired
    WfApprovalRecordService wfApprovalRecordService;

    public void save(WfApprovalRecord wfApprovalRecord) {
        setEnumsName(wfApprovalRecord);
        ShiroUtils.setAddAuthInfo(wfApprovalRecord);
        wfApprovalRecordService.save(wfApprovalRecord);
    }

    public void updateById(WfApprovalRecord wfApprovalRecord) {
        setEnumsName(wfApprovalRecord);
        DataAuthService.checkPermision(wfApprovalRecordService.getById(wfApprovalRecord.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(wfApprovalRecord);
        WfApprovalRecord wfApprovalRecordDb = queryById(wfApprovalRecord.getId());

        wfApprovalRecordService.updateById(wfApprovalRecord);
    }

    public WfApprovalRecord queryById(Serializable id) {
        WfApprovalRecord wfApprovalRecord = wfApprovalRecordService.getById(id);
        DataAuthService.checkPermision(wfApprovalRecord, DataPermisionTypeEnum.ByComp);

        return wfApprovalRecord;
    }

    public void removeById(Serializable id) {
        WfApprovalRecord wfApprovalRecord = wfApprovalRecordService.getById(id);
        DataAuthService.checkPermision(wfApprovalRecord, DataPermisionTypeEnum.ByComp);
        WfApprovalRecord wfApprovalRecordRecord = new WfApprovalRecord();
        wfApprovalRecordRecord.setId(wfApprovalRecord.getId());

        wfApprovalRecordService.updateById(wfApprovalRecordRecord);
    }

    private void setEnumsName(WfApprovalRecord wfApprovalRecord) {
    }

    public void updateRecordStatus(Long apprId, ApprovalStatus approvalStatus){
        WfApprovalRecord wfApprovalRecord = new WfApprovalRecord();
        wfApprovalRecord.setId(apprId);
        wfApprovalRecord.setApprovalStatus(approvalStatus.getCode());
        wfApprovalRecord.setApprovalStatusName(approvalStatus.name());
        updateById(wfApprovalRecord);
    }

    public List<WfApprovalRecord> queryRcds(Long requestId){
        return  wfApprovalRecordService.lambdaQuery().eq(WfApprovalRecord::getRequestId,requestId).list();
    }

}
