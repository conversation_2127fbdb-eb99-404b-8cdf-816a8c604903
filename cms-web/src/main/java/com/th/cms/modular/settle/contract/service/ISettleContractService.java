package com.th.cms.modular.settle.contract.service;

import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.modular.settle.contract.dto.*;
import com.th.cms.modular.settle.contract.entity.SettleContract;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.IOException;

/**
 * <p>
 * 合同信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
public interface ISettleContractService extends IService<SettleContract> {
    /**
     * 新增合同
     * @param requestDTO 请求参数
     * @param user 登录人
     * @return 结果
     */
    Boolean add(AddContractRequestDTO requestDTO, ShiroUser user);

    /**
     * 删除合同
     * @param requestDTO 请求参数
     * @param user 登录人
     * @return 结果
     */
    Boolean delete(DeleteContractRequestDTO requestDTO, ShiroUser user);
    /**
     * 更新合同
     * @param requestDTO 请求参数
     * @param user 登录人
     * @return 结果
     */
    Boolean update(UpdateContractRequestDTO requestDTO, ShiroUser user);
    /**
     * 合同详情
     * @param requestDTO 请求参数
     * @param user 登录人
     * @return 结果
     */
    DetailContractDTO detail(DetailContractRequestDTO requestDTO, ShiroUser user);

    /**
     * 查询合同列表
     * @param requestDTO 请求参数
     * @param user 登录人
     * @return 结果
     */
    LayuiPageInfo<ListContractDTO> listPage(ListContractRequestDTO requestDTO, ShiroUser user);

    /**
     * 导出
     *
     * @param requestDTO 请求参数
     * @param user       登录人
     * @return 结果
     */
    String export(ListContractRequestDTO requestDTO, ShiroUser user) throws IOException;
}
