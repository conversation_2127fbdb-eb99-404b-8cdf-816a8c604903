package com.th.cms.modular.bizSettleFencheng.service;

import com.th.cms.modular.bizSettleFencheng.model.BizSettleFencheng;
import com.th.cms.modular.bizSettleFencheng.dao.BizSettleFenchengMapper;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.common.page.LayuiPageFactory;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.modular.bizSettleFencheng.model.reqparam.BizSettleFenchengListParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizSettleFenchengService extends ServiceImpl<BizSettleFenchengMapper, BizSettleFencheng> implements IService<BizSettleFencheng> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizSettleFenchengListParam param) {
        QueryWrapper<BizSettleFencheng> objectQueryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(param.getProjName())){
            objectQueryWrapper.lambda().eq(BizSettleFencheng::getProjName,param.getProjName());
        }

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);
        return layuiPageInfo;

    }
}
