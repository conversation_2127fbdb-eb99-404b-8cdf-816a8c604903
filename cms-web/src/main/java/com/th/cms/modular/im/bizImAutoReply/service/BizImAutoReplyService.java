package com.th.cms.modular.im.bizImAutoReply.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.im.bizImAutoReply.dao.BizImAutoReplyMapper;
import com.th.cms.modular.im.bizImAutoReply.model.BizImAutoReply;
import com.th.cms.modular.im.bizImAutoReply.model.enums.ReplyTypeEnum;
import com.th.cms.modular.im.bizImAutoReply.model.reqparam.BizImAutoReplyListParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizImAutoReplyService extends ServiceImpl<BizImAutoReplyMapper, BizImAutoReply> implements IService<BizImAutoReply> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizImAutoReplyListParam param) {
        QueryWrapper<BizImAutoReply> objectQueryWrapper = new QueryWrapper<>();

        objectQueryWrapper.lambda().orderByDesc(BizImAutoReply::getUpdateTime);
        if (StringUtils.isNotBlank(param.getReplyContent())) {
            objectQueryWrapper.lambda().like(BizImAutoReply::getReplyContent, "%" + param.getReplyContent() + "%");
        }
        if (null != param.getReplyType()) {
            objectQueryWrapper.lambda().eq(BizImAutoReply::getReplyType, param.getReplyType());
        }
        if (null != param.getStatus()) {
            objectQueryWrapper.lambda().like(BizImAutoReply::getStatus, param.getStatus());
        }
        //类型不等于引导语
        objectQueryWrapper.lambda().ne(BizImAutoReply::getReplyType, ReplyTypeEnum.GUIDE.value);

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);

        List<BizImAutoReply> list = layuiPageInfo.getData();
        for (BizImAutoReply bizImAutoReply : list) {

            bizImAutoReply.setReplyTypeName(Objects.requireNonNull(ReplyTypeEnum.getType(bizImAutoReply.getReplyType())).name);
        }

        return layuiPageInfo;

    }
}
