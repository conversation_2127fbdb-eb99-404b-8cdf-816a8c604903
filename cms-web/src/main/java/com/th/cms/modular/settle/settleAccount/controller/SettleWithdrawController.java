package com.th.cms.modular.settle.settleAccount.controller;

import com.th.cms.modular.settle.settleAccount.model.reqparam.ImportSettleWithdrawFileListParam;
import com.th.cms.modular.settle.settleAccount.model.reqparam.SettleWithdrawImportDto;
import com.th.cms.modular.settle.settleAccount.service.SettleWithdrawImportService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.settle.settleAccount.model.SettleWithdraw;
import com.th.cms.modular.settle.settleAccount.service.SettleWithdrawService;
import com.th.cms.modular.settle.settleAccount.service.SettleWithdrawExtService;
import com.th.cms.modular.settle.settleAccount.model.reqparam.SettleWithdrawListParam;

/**
 * 提现管理控制器
 *
 * <AUTHOR>
 * @Date 2025-04-16 14:06:04
 */
@Controller
@RequestMapping("/settleWithdraw")
public class SettleWithdrawController extends BaseController {

    private String PREFIX = "/modular/settleAccount/settleWithdraw/";
    @Autowired
    SettleWithdrawExtService settleWithdrawExtService;
    @Autowired
    private SettleWithdrawService settleWithdrawService;


    /**
     * 跳转到提现管理首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "settleWithdraw.html";
    }

    /**
     * 跳转到添加提现管理
     */
    @Permission
    @RequestMapping("/settleWithdraw_add")
    public String settleWithdrawAdd() {
        return PREFIX + "settleWithdraw_add.html";
    }

    /**
     * 跳转到添加提现导入页面
     */
    @Permission
    @RequestMapping("/settleWithdraw_import")
    public String settleWithdrawImport() {
        return PREFIX + "settleWithdraw_import.html";
    }


    /**
     * 跳转到修改提现管理
     */
    @Permission
    @RequestMapping("/settleWithdraw_update")
    public String settleWithdrawUpdate(@RequestParam  Integer settleWithdrawId, Model model) {
        SettleWithdraw settleWithdraw = settleWithdrawExtService.queryById(settleWithdrawId);
        model.addAttribute("item",settleWithdraw);
        LogObjectHolder.me().set(settleWithdraw);
        return PREFIX + "settleWithdraw_edit.html";
    }
    @RequestMapping("/settleWithdraw_detail")
    public String settleWithdrawDetail(@RequestParam Integer settleWithdrawId, Model model) {
        SettleWithdraw settleWithdraw = settleWithdrawExtService.queryById(settleWithdrawId);
        model.addAttribute("item",settleWithdraw);
        LogObjectHolder.me().set(settleWithdraw);
        return PREFIX + "settleWithdraw_detail.html";
    }
    /**
     * 获取提现管理列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(SettleWithdrawListParam settleWithdrawParam) {
        return settleWithdrawService.findPageBySpec(settleWithdrawParam);
    }

    /**
     * 新增提现管理
     */
    @RequestMapping(value = "/import")
    @Permission
    @ResponseBody
    public ResponseData importSettleWithdrawFile(SettleWithdrawImportDto settleWithdrawImportDto) {
         settleWithdrawExtService.importSettleWithdrawFile(settleWithdrawImportDto);
         return ResponseData.success();
    }



    /**
     * 新增提现管理
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/settleWithdraw/settleWithdraw_add")
    @ResponseBody
    public ResponseData add(SettleWithdraw settleWithdraw) {
        settleWithdrawExtService.save(settleWithdraw);
        return ResponseData.success();
    }

    /**
     * 删除提现管理
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  settleWithdrawId) {
        settleWithdrawExtService.removeById(settleWithdrawId);
         return ResponseData.success();
    }

    /**
     * 修改提现管理
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/settleWithdraw/settleWithdraw_update")
    @ResponseBody
    public ResponseData update(SettleWithdraw settleWithdraw) {
        settleWithdrawExtService.updateById(settleWithdraw);
        return ResponseData.success();
    }

    /**
     * 提现管理详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  settleWithdrawId) {
       SettleWithdraw detail = settleWithdrawExtService.queryById(settleWithdrawId);
       return ResponseData.success(detail);
    }
}
