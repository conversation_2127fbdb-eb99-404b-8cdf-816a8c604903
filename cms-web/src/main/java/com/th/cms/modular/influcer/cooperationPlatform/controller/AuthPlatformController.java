package com.th.cms.modular.influcer.cooperationPlatform.controller;

import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.dto.FrontPageResult;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuth;
import com.th.cms.modular.influcer.cooperationPlatform.dto.*;
import com.th.cms.modular.influcer.cooperationPlatform.service.AuthPlatformService;
import com.th.cms.util.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 认证平台Controller
 *
 * <AUTHOR>
 */

@Controller
@RequestMapping("user/authPlatform")
public class AuthPlatformController {


    @Autowired
    private AuthPlatformService authPlatformService;
    @Autowired
    private UserUtil userUtil;
    /**
     * 认证平台列表
     */
    @RequestMapping(value = "/basePlatformList")
    @ResponseBody
    public ResponseData<FrontPageResult<BizPlatformAuth>> basePlatformList(@RequestBody BasePlatformRequestDTO paramDTO, HttpServletRequest request) {
        return ResponseData.success(authPlatformService.basePlatformList(paramDTO,  userUtil.getUserNotNull(request)));
    }
    /**
     * 新增认证平台
     */
    @RequestMapping(value = "/saveOrUpdateBasePlatform")
    @ResponseBody
    public ResponseData<Boolean> saveOrUpdateBasePlatform(@RequestBody BasePlatformAddRequestDTO paramDTO, HttpServletRequest request) {
        return ResponseData.success(authPlatformService.saveOrUpdateBasePlatform(paramDTO,  userUtil.getUserNotNull(request)));
    }
    /**
     * 启用关闭认证平台
     */
    @RequestMapping(value = "/toggleBasePlatform")
    @ResponseBody
    public ResponseData<Boolean> toggleBasePlatform(@RequestParam(value = "platId")Long platId, @RequestParam(value = "status")Integer status, HttpServletRequest request) {
        return ResponseData.success(authPlatformService.toggleBasePlatform(platId,status, userUtil.getUserNotNull(request)));
    }




    /**
     * 认证平台列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public ResponseData<FrontPageResult<AppPlatformAuthDTO>> list(@RequestBody AppPlatformRequestDTO paramDTO, HttpServletRequest request) {
        return ResponseData.success(authPlatformService.list(paramDTO,  userUtil.getUserNotNull(request)));
    }
    /**
     * 认证平台详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public ResponseData<AppPlatformAuthDTO> detail(@RequestParam(value = "id",required = false)Long id, HttpServletRequest request) {
        return ResponseData.success(authPlatformService.detail(id, userUtil.getUserNotNull(request)));
    }
    /**
     * 修改认证平台配置
     */
    @RequestMapping(value = "/saveOrUpdateAppPlatform")
    @ResponseBody
    public ResponseData<Boolean> saveOrUpdateAppPlatform(@RequestBody AppPlatformAddRequestDTO paramDTO, HttpServletRequest request) {
        return ResponseData.success(authPlatformService.saveOrUpdateAppPlatform(paramDTO,userUtil.getUserNotNull(request)));
    }
}
