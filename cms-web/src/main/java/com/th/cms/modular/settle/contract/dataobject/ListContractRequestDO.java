package com.th.cms.modular.settle.contract.dataobject;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ListContractRequestDO {

    /**
     * 合同号
     */
    private String contractNumber;
    /**
     * 达人昵称或ID
     */
    private String influencerId;

    /**
     * 分成比例（百分比）
     */
    private BigDecimal revenueShareRatioStart;
    /**
     * 分成比例（百分比）
     */
    private BigDecimal revenueShareRatioEnd;
    /**
     * 合同日期范围开始
     */
    private Date contractStartDate;

    /**
     * 合同日期范围结束
     */
    private Date contractEndDate;

    /**
     * 合同状态
     */
    private Integer contractStatus;

    /**
     * 工区
     */
    private String workArea;

    /**
     * 商务人员
     */
    private String businessPerson;
}
