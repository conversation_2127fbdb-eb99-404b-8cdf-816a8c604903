package com.th.cms.modular.userindex.controller;

import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.modular.userindex.dto.*;
import com.th.cms.modular.userindex.service.UserIndexService;
import com.th.cms.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 用户首页
 */
@RestController
@RequestMapping("user/userIndex")
public class UserIndexController {
    @Autowired
    private UserIndexService userIndexService;
    @Autowired
    private UserUtil userUtil;
    /**
     * 明月号人均认证情况📊
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping("/authenticationStatusPerPerson")
    public ResponseData<AuthenticationStatusPerPersonDTO>  authenticationStatusPerPerson(@RequestBody AuthenticationStatusPerPersonRequestDTO  requestDTO, HttpServletRequest request) {
        return ResponseData.success(userIndexService.authenticationStatusPerPerson(requestDTO,userUtil.getUserNotNull(request)));
    }
    /**
     * 明月号人均完成情况📊
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping("/completePerPerson")
    public ResponseData<CompletePerPersonDTO> completePerPerson(@RequestBody CompletePerPersonRequestDTO requestDTO, HttpServletRequest request) {
        return ResponseData.success(userIndexService.completePerPerson(requestDTO,userUtil.getUserNotNull(request)));
    }
    /**
     * 用户信息
     * @return 结果
     */
    @RequestMapping("/userInfo")
    public ResponseData<UserInfoDTO> userInfo(HttpServletRequest request) {
        return ResponseData.success(userIndexService.userInfo(userUtil.getUserNotNull(request)));
    }
    /**
     * 待办事项
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping("/todo")
    public ResponseData<UserTodoDTO> todo(@RequestBody UserTodoRequestDTO requestDTO, HttpServletRequest request) {
        return ResponseData.success(userIndexService.todo(requestDTO,userUtil.getUserNotNull(request)));
    }
    /**
     * 商务合作邀请达人数排行榜
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping("/rankList")
    public ResponseData<List<BusinessRankDTO>> rankList(@RequestBody BusinessRankRequestDTO requestDTO, HttpServletRequest request) {
        return ResponseData.success(userIndexService.rankList(requestDTO,userUtil.getUserNotNull(request)));
    }
    /**
     * 审核中心
     * @return 结果
     */
    @RequestMapping("/auditCenter")
    public ResponseData<AuditCenterDTO> auditCenter(HttpServletRequest request) {
        return ResponseData.success(userIndexService.auditCenter(userUtil.getUserNotNull(request)));
    }
    /**
     * 发送验证码
     * @param phone 参数
     * @return 结果
     */
    @RequestMapping("/sendValidCode")
    public ResponseData<Boolean> sendValidCode(@RequestParam(value = "phone") String phone, HttpServletRequest request) throws Exception {
        if (StringUtils.isBlank(phone)) {
            return ResponseData.error("手机号错误");
        }
        return ResponseData.success(userIndexService.sendValidCode(phone));
    }
    /**
     * 修改用户头像
     * @param file 参数
     * @return 结果
     */
    @RequestMapping("/updateUserAvatar")
    public ResponseData<Boolean> updateUserAvatar(MultipartFile file, HttpServletRequest request) {
        return ResponseData.success(userIndexService.updateUserAvatar(file,userUtil.getUserNotNull(request)));
    }
    /**
     * 修改用户手机号
     * @param phone 参数
     * @return 结果
     */
    @RequestMapping("/updateUserPhone")
    public ResponseData<Boolean> updateUserPhone(@RequestParam(value = "phone") String phone,@RequestParam(value = "code") String code, HttpServletRequest request) {
        return ResponseData.success(userIndexService.updateUserPhone(phone,code,userUtil.getUserNotNull(request)));
    }
    /**
     * 修改用户密码
     * @param password 参数
     * @return 结果
     */
    @RequestMapping("/updateUserPassword")
    public ResponseData<Boolean> updateUserPassword(@RequestParam(value = "password") String password, HttpServletRequest request) {
        return ResponseData.success(userIndexService.updateUserPassword(password, userUtil.getUserNotNull(request)));
    }

}
