package com.th.cms.modular.im.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageConfig {
    private Boolean unread_enabled = true;
    private Boolean mutil_sync_enabled = true;
    private Boolean offline_enabled = true;
    private Boolean history_enabled = true;
    private Boolean roaming_enabled = true;
    private Boolean conversation_update_enabled = true;

}
