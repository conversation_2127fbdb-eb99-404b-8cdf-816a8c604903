package com.th.cms.modular.userindex.convert;

import com.google.common.collect.Lists;
import com.th.cms.core.util.DateUtils;
import com.th.cms.modular.sysmesage.consts.UrgencyLevel;
import com.th.cms.modular.system.entity.Dept;
import com.th.cms.modular.system.entity.Notice;
import com.th.cms.modular.userindex.dataobject.*;
import com.th.cms.modular.userindex.dto.*;
import com.th.cms.util.UserDTO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class UserIndexConvert {
    public static AuthenticationStatusPerPersonDTO convertAuthenticationStatusPerPersonDTO(List<AuthenticationStatusPerPersonDO> list, Boolean isAll, Dept dept) {
        AuthenticationStatusPerPersonDTO authenticationStatusPerPersonDTO = new AuthenticationStatusPerPersonDTO();
        authenticationStatusPerPersonDTO.setList(convertAuthenticationStatusPerPersonList(list));
        authenticationStatusPerPersonDTO.setGroupName(getGroupName(isAll, dept));
        return authenticationStatusPerPersonDTO;


    }

    private static String getGroupName(Boolean isAll, Dept dept) {
        if (isAll) {
            return "各工区";
        } else {
            if (dept == null) {
                return "工区各组";
            } else {
                return dept.getSimpleName() + "各组";
            }
        }
    }

    private static List<AuthenticationStatusPerPersonDTO.DataDTO> convertAuthenticationStatusPerPersonList(List<AuthenticationStatusPerPersonDO> list) {
        List<AuthenticationStatusPerPersonDTO.DataDTO> dataDTOlist = Lists.newArrayList();
        for (AuthenticationStatusPerPersonDO authenticationStatusPerPersonDO : list) {
            dataDTOlist.add(convertFromAuthenticationStatusPerPersonDO(authenticationStatusPerPersonDO));
        }
        return dataDTOlist;


    }

    private static AuthenticationStatusPerPersonDTO.DataDTO convertFromAuthenticationStatusPerPersonDO(AuthenticationStatusPerPersonDO authenticationStatusPerPersonDO) {
        AuthenticationStatusPerPersonDTO.DataDTO dataDTO = new AuthenticationStatusPerPersonDTO.DataDTO();
        dataDTO.setName(authenticationStatusPerPersonDO.getGroupName());
        dataDTO.setCount(calculateCount(authenticationStatusPerPersonDO.getUserCount(), authenticationStatusPerPersonDO.getCn()));
        return dataDTO;
    }

    private static BigDecimal calculateCount(Long userCount, Long cn) {
        if (userCount==0) {
            return BigDecimal.ZERO;
        }
        // 将 long 转换为 BigDecimal 并进行除法运算
     return new BigDecimal(cn)
                .divide(new BigDecimal(userCount), 10, RoundingMode.HALF_UP);
    }

    public static CompletePerPersonDTO convertCompletePerPersonDTO(CompletePerPersonRequestDTO requestDTO, Boolean isAll, Dept dept, List<CompletePerPersonDO> list) {
        String startDate = requestDTO.getStartDate();
        String endDate = requestDTO.getEndDate();
        Date st = DateUtils.parse(startDate, DateUtils.PATTERN_YHMS);
        Date et = DateUtils.parse(endDate, DateUtils.PATTERN_YHMS);
        List<String> dateList = Lists.newArrayList();
        Map<String, List<CompletePerPersonDO>> collect = list.stream().collect(Collectors.groupingBy(CompletePerPersonDO::getDeptName));
        /* *
         *
         * 提审审核人均情况
         */
        CompletePerPersonDTO.DataDTO dataDTO = new CompletePerPersonDTO.DataDTO();
        for (Date index = st; index.before(et); index = org.apache.commons.lang3.time.DateUtils.addDays(index, 1)) {
            dateList.add(DateUtils.formatDate(index, "YYYY-MM"));
        }
        dataDTO.setDateList(dateList);
        collect.forEach((deptName, completePerPersonDOList) -> {
            CompletePerPersonDTO.GroupDTO groupDTO = new CompletePerPersonDTO.GroupDTO();
            groupDTO.setName(deptName);
            List<Long> countList = new ArrayList<>();
            for (String s : dateList) {
                countList.add(getDateCount(s,deptName, list));
            }
        });
        CompletePerPersonDTO completePerPersonDTO = new CompletePerPersonDTO();
        completePerPersonDTO.setDataDTO(dataDTO);
        completePerPersonDTO.setGroupName(getGroupName(isAll, dept));
        return completePerPersonDTO;
    }

    private static Long getDateCount(String date, String deptName, List<CompletePerPersonDO> list) {
        for (CompletePerPersonDO completePerPersonDO : list) {
            if (completePerPersonDO.getDeptName().equals(deptName) && completePerPersonDO.getUpdateTime().equals(date)) {
                return completePerPersonDO.getCn();
            }
        }
        return 0L;
    }

    public static UserTodoDTO.DataDTO convertUserTodoDTO(Notice t) {
        UserTodoDTO.DataDTO dataDTO = new UserTodoDTO.DataDTO();
        dataDTO.setName(t.getSendUser());
        dataDTO.setDeptName(t.getDeptName());
        dataDTO.setCreateTime(t.getCreateTime());
        dataDTO.setMsgType(t.getMsgType());
        dataDTO.setEmergency(UrgencyLevel.getNameByCode(t.getUrgencyLevel()));
        dataDTO.setTitle(t.getTitle());
        dataDTO.setContent(t.getContent());
        return dataDTO;


    }

    public static AuthenticationStatusPerPersonRequestDO convertAuthenticationStatusPerPersonDO(AuthenticationStatusPerPersonRequestDTO requestDTO, UserDTO userNotNull, Boolean isAll, List<Long> allDeptIds) {
        AuthenticationStatusPerPersonRequestDO authenticationStatusPerPersonRequestDO = new AuthenticationStatusPerPersonRequestDO();
        authenticationStatusPerPersonRequestDO.setStartDate(DateUtils.minTime(requestDTO.getStartDate(), DateUtils.PATTERN_YHMS));
        authenticationStatusPerPersonRequestDO.setEndDate(DateUtils.maxTime(requestDTO.getEndDate(), DateUtils.PATTERN_YHMS));
        authenticationStatusPerPersonRequestDO.setIsAll(isAll);
        authenticationStatusPerPersonRequestDO.setDeptIds(allDeptIds);
        return authenticationStatusPerPersonRequestDO;


    }

    public static CompletePerPersonRequestDO convertCompletePerPersonDO(CompletePerPersonRequestDTO requestDTO, UserDTO userNotNull, Boolean isAll, List<Long> allDeptIds) {
        CompletePerPersonRequestDO completePerPersonRequestDO = new CompletePerPersonRequestDO();
        completePerPersonRequestDO.setStartDate(DateUtils.minTime(requestDTO.getStartDate(), DateUtils.PATTERN_YHMS));
        completePerPersonRequestDO.setEndDate(DateUtils.maxTime(requestDTO.getEndDate(), DateUtils.PATTERN_YHMS));
        completePerPersonRequestDO.setIsAll(isAll);
        completePerPersonRequestDO.setDeptIds(allDeptIds);
        completePerPersonRequestDO.setQueryType(requestDTO.getQueryType());
        return completePerPersonRequestDO;


    }

    public static BusinessRankRequestDO convertRankListDO(BusinessRankRequestDTO requestDTO) {
        BusinessRankRequestDO authenticationStatusPerPersonRequestDO = new BusinessRankRequestDO();
        authenticationStatusPerPersonRequestDO.setStartDate(DateUtils.minTime(requestDTO.getStartDate(), DateUtils.PATTERN_YHMS));
        authenticationStatusPerPersonRequestDO.setEndDate(DateUtils.maxTime(requestDTO.getEndDate(), DateUtils.PATTERN_YHMS));

        return authenticationStatusPerPersonRequestDO;

    }
}
