package com.th.cms.modular.settle.settleProjects.model.reqparam;

import cn.stylefeng.roses.kernel.model.validator.BaseValidatingParam;
import lombok.Data;

import java.io.Serializable;


/**
 * <p>
 * 项目信息表 列表查询实体
 * </p>
 */
@Data
public class SettleProjectsListParam implements Serializable, BaseValidatingParam {
    private static final long serialVersionUID = 1L;
    private String projectName;
    private String serialNumber;
    private String businessOwnerName;
    private String businessOwner;
    private String projectTypeId;
    private String projectTypeName;
    private String settleStatus;
    private String commissionRateId;
    private String commissionRate;
    private String settlementCycleName;
    private String settlementCycle;
    private String revenueTypeName;
    private String revenueType;
    private String created_by;
    private String platform1;
    private String platform2;
    private String platform3;
    //        queryData['platform1'] = $("#platform1").val().trim();
//        queryData['platform2'] = $("#platform2").val().trim();
//        queryData['platform3'] = $("#platform3").val().trim();

    @Override
    public String checkParam() {
        return "";
    }
}
