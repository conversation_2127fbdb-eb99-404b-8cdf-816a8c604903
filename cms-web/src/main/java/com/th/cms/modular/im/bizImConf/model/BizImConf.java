package com.th.cms.modular.im.bizImConf.model;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="BizImConf对象", description="")
public class BizImConf implements Serializable {


    @ApiModelProperty(value = "编号")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "平均分配")
    @TableField("fenp_rule")
    private Integer fenpRule;

    @ApiModelProperty(value = "接待过的分配")
    @TableField("jiedai_fenp")
    private Integer jiedaiFenp;

    @ApiModelProperty(value = "延迟分钟预警")
    @TableField("yanshi_alt_time")
    private Integer yanshiAltTime;

    @ApiModelProperty(value = "延迟次数预警")
    @TableField("yanshi_alt_cs")
    private Integer yanshiAltCs;

    @ApiModelProperty(value = "升级相应人ID")
    @TableField("shenji_user_id")
    private Long shenjiUserId;

    @ApiModelProperty(value = "升级相应人")
    @TableField("shegji_user_name")
    private String shegjiUserName;

    @ApiModelProperty(value = "是否电话")
    @TableField("is_use_tel")
    private Integer isUseTel;

    @ApiModelProperty(value = "电话号码")
    @TableField("user_tels")
    private String userTels;

    @TableField("uname")
    private String uname;

    @TableField("utel")
    private String utel;

    @TableField("uname2")
    private String uname2;

    @TableField("utel2")
    private String utel2;

    @TableField("uname3")
    private String uname3;

    @TableField("utel3")
    private String utel3;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ExcelField(title="编号",dictType="", align=2, sort=0)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @ExcelField(title="平均分配",dictType="", align=2, sort=1)
    public Integer getFenpRule() {
        return fenpRule;
    }

    public void setFenpRule(Integer fenpRule) {
        this.fenpRule = fenpRule;
    }
    @ExcelField(title="接待过的分配",dictType="", align=2, sort=2)
    public Integer getJiedaiFenp() {
        return jiedaiFenp;
    }

    public void setJiedaiFenp(Integer jiedaiFenp) {
        this.jiedaiFenp = jiedaiFenp;
    }
    @ExcelField(title="延迟分钟预警",dictType="", align=2, sort=3)
    public Integer getYanshiAltTime() {
        return yanshiAltTime;
    }

    public void setYanshiAltTime(Integer yanshiAltTime) {
        this.yanshiAltTime = yanshiAltTime;
    }
    @ExcelField(title="延迟次数预警",dictType="", align=2, sort=4)
    public Integer getYanshiAltCs() {
        return yanshiAltCs;
    }

    public void setYanshiAltCs(Integer yanshiAltCs) {
        this.yanshiAltCs = yanshiAltCs;
    }
    @ExcelField(title="升级相应人ID",dictType="", align=2, sort=5)
    public Long getShenjiUserId() {
        return shenjiUserId;
    }

    public void setShenjiUserId(Long shenjiUserId) {
        this.shenjiUserId = shenjiUserId;
    }
    @ExcelField(title="升级相应人",dictType="", align=2, sort=6)
    public String getShegjiUserName() {
        return shegjiUserName;
    }

    public void setShegjiUserName(String shegjiUserName) {
        this.shegjiUserName = shegjiUserName;
    }
    @ExcelField(title="是否电话",dictType="", align=2, sort=7)
    public Integer getIsUseTel() {
        return isUseTel;
    }

    public void setIsUseTel(Integer isUseTel) {
        this.isUseTel = isUseTel;
    }
    @ExcelField(title="电话号码",dictType="", align=2, sort=8)
    public String getUserTels() {
        return userTels;
    }

    public void setUserTels(String userTels) {
        this.userTels = userTels;
    }
    @ExcelField(title="",dictType="", align=2, sort=9)
    public String getUname() {
        return uname;
    }

    public void setUname(String uname) {
        this.uname = uname;
    }
    @ExcelField(title="",dictType="", align=2, sort=10)
    public String getUtel() {
        return utel;
    }

    public void setUtel(String utel) {
        this.utel = utel;
    }
    @ExcelField(title="",dictType="", align=2, sort=11)
    public String getUname2() {
        return uname2;
    }

    public void setUname2(String uname2) {
        this.uname2 = uname2;
    }
    @ExcelField(title="",dictType="", align=2, sort=12)
    public String getUtel2() {
        return utel2;
    }

    public void setUtel2(String utel2) {
        this.utel2 = utel2;
    }
    @ExcelField(title="",dictType="", align=2, sort=13)
    public String getUname3() {
        return uname3;
    }

    public void setUname3(String uname3) {
        this.uname3 = uname3;
    }
    @ExcelField(title="",dictType="", align=2, sort=14)
    public String getUtel3() {
        return utel3;
    }

    public void setUtel3(String utel3) {
        this.utel3 = utel3;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=15)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=16)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BizImConf{" +
        "id=" + id +
        ", fenpRule=" + fenpRule +
        ", jiedaiFenp=" + jiedaiFenp +
        ", yanshiAltTime=" + yanshiAltTime +
        ", yanshiAltCs=" + yanshiAltCs +
        ", shenjiUserId=" + shenjiUserId +
        ", shegjiUserName=" + shegjiUserName +
        ", isUseTel=" + isUseTel +
        ", userTels=" + userTels +
        ", uname=" + uname +
        ", utel=" + utel +
        ", uname2=" + uname2 +
        ", utel2=" + utel2 +
        ", uname3=" + uname3 +
        ", utel3=" + utel3 +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
