package com.th.cms.modular.influcer.bizInflucerPlatform.model.reqparam;

import cn.stylefeng.roses.kernel.model.validator.BaseValidatingParam;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 * 列表查询实体
 * </p>
 */
@Data
public class BizInflucerPlatformListParam implements Serializable, BaseValidatingParam {

    private static final long serialVersionUID = 1L;

    @Override
    public String checkParam() {
        return "";
    }

    private String keywords;//关键词
    private String phone;//手机号1
    private Long platformId;//授权平台1
    private Integer sourceForm;//认证来源1
    private Integer approveStatus;//审核状态1
    private Integer receiveStatus;
    private Date approveStartTime;//审核开始时间
    private Date approveEndTime;//审核结束时间

    private Long businessId;//商务id
    private Long approveUser;//审核人
    private String approveTime;//审核时间

}
