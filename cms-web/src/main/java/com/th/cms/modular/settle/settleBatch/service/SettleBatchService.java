package com.th.cms.modular.settle.settleBatch.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.util.CollectionUtils;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.enums.*;
import com.th.cms.modular.influcer.bizInflucer.service.BizInflucerService;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformService;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountService;
import com.th.cms.modular.settle.settleBatch.dao.SettleBatchMapper;
import com.th.cms.modular.settle.settleBatch.model.SettleBatch;
import com.th.cms.modular.settle.settleBatch.model.reqparam.SettleBatchListParam;
import com.th.cms.modular.settle.settleInfluencerOrder.model.SettleInfluencerOrder;
import com.th.cms.modular.settle.settleInfluencerOrder.service.SettleInfluencerOrderService;
import com.th.cms.modular.settle.settleOrder.model.SettleOrderProject;
import com.th.cms.modular.settle.settleOrder.service.SettleOrderExtService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import com.th.cms.modular.system.service.UserService;
import com.th.cms.modular.wf.wfApprovalRecord.model.WfApprovalRecord;
import com.th.cms.modular.wf.wfApprovalRecord.service.WfApprovalRecordExtService;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestExtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 达人结算订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettleBatchService extends ServiceImpl<SettleBatchMapper, SettleBatch> implements IService<SettleBatch> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    
    public LayuiPageInfo findPageBySpec(SettleBatchListParam param) {
        QueryWrapper<SettleBatch> objectQueryWrapper = new QueryWrapper<>();
        if(param.getProjectId()!=null && param.getProjectId()>0){
            objectQueryWrapper.lambda().eq(SettleBatch::getProjectId,param.getProjectId());
        }

        if(param.getJigouShouyi()!=null && param.getJigouShouyi()>0){
            objectQueryWrapper.lambda().eq(SettleBatch::getAgencyRevenue,param.getJigouShouyi());
        }
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);

        return layuiPageInfo;
    }

    @Autowired
    SettleProjectsService settleProjectsService;
    @Autowired
    SettleOrderExtService settleOrderExtService;
    @Autowired
    WfApprovalRecordExtService wfApprovalRecordExtService;

    public LayuiPageInfo findPageBtBySpec(SettleBatchListParam param) {
        QueryWrapper<SettleBatch> objectQueryWrapper = new QueryWrapper<>();
        if(param.getProjectId()!=null && param.getProjectId()>0){
            objectQueryWrapper.lambda().eq(SettleBatch::getProjectId,param.getProjectId());
        }

        if(param.getJigouShouyi()!=null && param.getJigouShouyi()>0){
            AgencyRevenueEnum agencyRevenueEnum = AgencyRevenueEnum.fromCode(param.getJigouShouyi());
            objectQueryWrapper.lambda().eq(SettleBatch::getAgencyRevenue,agencyRevenueEnum.getCode());
        }
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);

        List<SettleBatch> dlist = layuiPageInfo.getData();
        //填充结算金额字段
        dlist = settleOrderExtService.querySettleBatchs(dlist);
        //填充审核数据

        //SettleOrderProject
        List<SettleOrderProject> settleOrderProjectList = cvtSettlePjs(dlist);
        layuiPageInfo.setData(settleOrderProjectList);

        return layuiPageInfo;
    }

    @Autowired
    WfApprovalRequestExtService wfApprovalRequestExtService;
    public SettleBatch querySettleBatch(String settleId){
        SettleBatch settleBatch = getById(settleId);
        settleBatch = settleOrderExtService.querySettleBatchs(Arrays.asList(settleBatch)).get(0);
        return settleBatch;
    }
    @Autowired
    UserService userService;
    private List<SettleOrderProject> cvtSettlePjs(List<SettleBatch> dlist){
        //填充结算金额字段
        dlist = settleOrderExtService.querySettleBatchs(dlist);
        //填充审核数据

        //SettleOrderProject
        if(dlist.size()>0){
            List<Integer> projectIdsList = dlist.stream().map(SettleBatch::getProjectId).collect(Collectors.toList());
            List<SettleProjects> projects = settleProjectsService.lambdaQuery().in(SettleProjects::getId, projectIdsList).list();
            Map<Integer,SettleProjects> dmap = projects.stream().collect(Collectors.toMap(SettleProjects::getId, a->a));


            List<String> batchIds = dlist.stream().map(t->t.getBatchNo()).collect(Collectors.toList());
            //开始填充审核数据
            List<WfApprovalRecord>  wfApprovalRecordList = wfApprovalRequestExtService.getWfApprovalRecordByUid(batchIds, ApprovalBillType.SettleOrder,ShiroKit.getUser().getId()+"");
            Map<String,List<WfApprovalRecord>> wfArovMap = CollectionUtils.groupByKeyGeneric(wfApprovalRecordList,WfApprovalRecord::getBillNo);
            
            List<WfApprovalRecord>  wfConfirmApprovalRecordList = wfApprovalRequestExtService.getWfApprovalRecordByUid(batchIds, ApprovalBillType.SettleOrderConfirm,ShiroKit.getUser().getId()+"");
            Map<String,List<WfApprovalRecord>> wfConfirmArovMap = CollectionUtils.groupByKeyGeneric(wfConfirmApprovalRecordList, WfApprovalRecord::getBillNo);

            List<SettleOrderProject> settleOrderProjectList = dlist.stream().map(a -> {
                SettleOrderProject settleOrderProject = new SettleOrderProject();
                SettleProjects settleProjects = dmap.get(a.getProjectId());
                settleOrderProject.setSettleBatch(a);
                settleOrderProject.setSettleProjects(settleProjects);

                List<WfApprovalRecord> wfLst = wfArovMap.get(a.getBatchNo());
                if(wfLst!=null){
                    for (WfApprovalRecord wfApprovalRecord : wfLst){
                        ApprovalStatus approvalStatus = ApprovalStatus.fromCode(wfApprovalRecord.getApprovalStatus());
                        if(ApprovalStatus.UNDER_REVIEW.equals(approvalStatus) && ShiroKit.getUser().getId().equals(wfApprovalRecord.getApproverId())){
                            if(userService.isContainRoleName(wfApprovalRecord.getApproverId(),"老板")){
                                settleOrderProject.setShowLb(1);
                            }
                            if(userService.isContainRoleName(wfApprovalRecord.getApproverId(),"财务")){
                                settleOrderProject.setShowCw(1);
                                //判断用户角色
                            }
                        }

                        //
                    }

                    settleOrderProject.setWfApprovalRecordList(wfLst);
                }
                
                //二次结算审批流相关
                List<WfApprovalRecord> wfConfirmLst = wfConfirmArovMap.get(a.getBatchNo());
                if(CollectionUtils.isNotEmpty(wfConfirmLst) && SettleBatchConfirmStatusEnum.UNFINISH_STATUS.contains(a.getConfirmStatus())) {
                    for (WfApprovalRecord wfApprovalRecord : wfConfirmLst){
                        ApprovalStatus approvalStatus = ApprovalStatus.fromCode(wfApprovalRecord.getApprovalStatus());
                        if(!ApprovalStatus.UNDER_REVIEW.equals(approvalStatus)) {
                            continue;
                        }
                        if(ShiroKit.getUser().getId().equals(wfApprovalRecord.getApproverId()) 
                                && userService.isContainRoleName(wfApprovalRecord.getApproverId(),"业务负责人")
                                && (a.getConfirmStatus().intValue() == SettleBatchConfirmStatusEnum.WAIT.getCode() 
                                        || a.getConfirmStatus().intValue() == SettleBatchConfirmStatusEnum.FINANCE_REJECT.getCode())){
                            settleOrderProject.setBusinessConfirmApproval(Boolean.TRUE);
                        }
                        if(ShiroKit.getUser().getId().equals(wfApprovalRecord.getApproverId()) 
                                && userService.isContainRoleName(wfApprovalRecord.getApproverId(),"财务")
                                && a.getConfirmStatus().intValue() == SettleBatchConfirmStatusEnum.BUSINESS_FINISH.getCode()){
                            settleOrderProject.setFinanceConfirmApproval(Boolean.TRUE);
                        }
                    }
                }
                
                return settleOrderProject;
            }).collect(Collectors.toList());
            return settleOrderProjectList;
        }
        return new ArrayList<>();
    }

    @Autowired
    SettleInfluencerOrderService settleInfluencerOrderService;
    @Autowired
    SettleAccountService settleAccountService;
    @Autowired
    BizInflucerService bizInflucerService;
    @Autowired
    BizPlatformService bizPlatformService;

    @Transactional
    public synchronized void settleToDaren(SettleBatch settleBatch){
        //锁定该笔批次

        SettleBatch settleBatchDb = this.getBaseMapper().selectForUpdateExplicit(settleBatch.getId());
        SettleBatchStatus settleBatchStatus = SettleBatchStatus.fromCode(settleBatchDb.getBatchStatus());

        SettleProjects settleProjects = settleProjectsService.getById(settleBatch.getProjectId());
        BizPlatform bizPlatform = bizPlatformService.getById(settleProjects.getPlatformId());
        if(SettleBatchStatus.ShengSucing.equals(settleBatchStatus) || SettleBatchStatus.Daifa.equals(settleBatchStatus)){
            this.getBaseMapper().selectForUpdateExplicit(settleBatch.getId());
            boolean isSuc = updateBatchStatus(settleBatch.getId(),SettleBatchStatus.Daifa);
            if(isSuc){
                log.info("开始结算 批次 "+settleBatch.getBatchNo());
                List<SettleInfluencerOrder> toSetlInfOrdres = settleInfluencerOrderService.lambdaQuery().eq(SettleInfluencerOrder::getSettlementBatch,settleBatchDb.getBatchNo()).eq(SettleInfluencerOrder::getInflStatus, InflOrderStatus.shhe.getCode()).list();
                for(SettleInfluencerOrder settleInfluencerOrder : toSetlInfOrdres){
                    //开始加单个结算锁
                    settleInfluencerOrderService.settleInflOrder(settleInfluencerOrder,bizPlatform);
                }
            }
            updateBatchStatus(settleBatch.getId(),SettleBatchStatus.Yifa);
        }
    }

    private boolean updateBatchStatus(Integer batchId,SettleBatchStatus settleBatchStatus){
        SettleBatch settleBatchRd = new SettleBatch();
        settleBatchRd.setId(batchId);
        settleBatchRd.setBatchStatus(settleBatchStatus.getCode());
        settleBatchRd.setBatchStatusName(settleBatchStatus.getName());
        boolean isSuc = updateById(settleBatchRd);
        return isSuc;
    }
}
