package com.th.cms.modular.bizClue.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MyInflucerDetailIncomeDTO {


    /*** 结算批次号 */
    @ExcelProperty("结算批次号")
    private String batchNo;

    /**
     * 平台ID
     */
    @ExcelProperty("平台ID")
    private Long platformId;

    /**
     * 平台名称
     */
    @ExcelProperty("平台名称")
    private String platformName;

    /**
     * 图标
     */
    @ExcelProperty("图标")
    private String icon;

    /*** 项目ID */
    @ExcelProperty("项目ID")
    private Long projectId;

    /*** 项目名称 */
    @ExcelProperty("项目名称")
    private String projectName;


    /**
     * 项目开始
     */
    @ExcelProperty("项目开始")
    private Date projectStarttime;

    /**
     * 项目结束
     */
    @ExcelProperty("项目结束")
    private Date projectEndtime;

    /**
     * 收益类型（收益来源）
     */
    @ExcelProperty("收益类型")
    private String revenueType;

    /**
     * 收入日期
     */
    @ExcelProperty("收入日期")
    private Date incomeDate;

    /**
     * 收益
     */
    @ExcelProperty("收益")
    private BigDecimal income;
}
