package com.th.cms.modular.appconfig.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * APP端我的配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysAppConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关于听海
     */
    private String aboutUs;

    /**
     * 用户协议
     */
    private String userAgreement;

    /**
     * 隐私协议
     */
    private String privacyPolicy;

    /**
     * 版本号
     */
    private String appVersion;

    /**
     * 注销协议
     */
    private String terminationAgreement;

}
