package com.th.cms.modular.settle.cushionBatch.model;

import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <p>
 * 达人垫付订单表
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="CushionBatch对象", description="达人垫付订单表")
public class CushionBatch implements Serializable {


    @ApiModelProperty(value = "主键ID（自增）")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "批次号")
    @TableField("batch_no")
    private String batchNo;

    @ApiModelProperty(value = "垫付ID")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private Integer projectId;

    @ApiModelProperty(value = "项目简称")
    @TableField("project_name")
    private String projectName;

    @ApiModelProperty(value = "周期开始时间")
    @TableField("zhouqi_start")
    private Date zhouqiStart;

    @ApiModelProperty(value = "周期结束时间")
    @TableField("zhouqi_end")
    private Date zhouqiEnd;

    @ApiModelProperty(value = "公司与达人分成比例（示例：70%/30%）")
    @TableField("company_talent_ratio")
    private String companyTalentRatio;

    @ApiModelProperty(value = "参与垫付人数")
    @TableField("cushion_pnum")
    private Integer cushionPnum;

    @ApiModelProperty(value = "流水金额")
    @TableField("liushui_amount")
    private BigDecimal liushuiAmount;

    @ApiModelProperty(value = "机构收益")
    @TableField("organization_amount")
    private BigDecimal organizationAmount;
    
    @ApiModelProperty(value = "总应发放收益")
    @TableField("yingfa_amount")
    private Double yingfaAmount;

    @ApiModelProperty(value = "总已发放收益")
    @TableField("yifa_amount")
    private Double yifaAmount;

    @ApiModelProperty(value = "已提现金额")
    @TableField("withdrawn_amount")
    private BigDecimal withdrawnAmount;

    @ApiModelProperty(value = "待提现金额（计算字段）")
    @TableField("unwithdrawn_amount")
    private BigDecimal unwithdrawnAmount;

    @ApiModelProperty(value = "已到账金额")
    @TableField("arrive_amount")
    private BigDecimal arriveAmount;

    @ApiModelProperty(value = "未到账金额")
    @TableField("noarrive_amount")
    private BigDecimal noarriveAmount;

    @ApiModelProperty(value = "非标垫付人数")
    @TableField("feibiao_pnum")
    private Integer feibiaoPnum;

    @ApiModelProperty(value = "非标人数比例")
    @TableField("feibiao_rate")
    private Double feibiaoRate;

    @ApiModelProperty(value = "非标金额")
    @TableField("feibiao_amount")
    private BigDecimal feibiaoAmount;

    @ApiModelProperty(value = "非标金额比例")
    @TableField("feibiao_amount_rate")
    private BigDecimal feibiaoAmountRate;

    @ApiModelProperty(value = "批次状态")
    @TableField("batch_status")
    private Integer batchStatus;

    @ApiModelProperty(value = "批次状态")
    @TableField("batch_status_name")
    private String batchStatusName;

    /**
     * @see com.th.cms.modular.enums.SettleBatchConfirmStatusEnum
     */
    @ApiModelProperty(value = "确认垫付状态")
    private Integer confirmStatus;

    @ApiModelProperty(value = "确认垫付状态名称")
    private String confirmStatusName;
    
    @ApiModelProperty(value = "垫付单")
    @TableField("jisuan_file")
    private String jisuanFile;

    @ApiModelProperty(value = "垫付单MD5")
    @TableField("jisuan_file_md5")
    private String jisuanFileMd5;

    @ApiModelProperty(value = "附件")
    @TableField("fujian_files")
    private String fujianFiles;

    @ApiModelProperty(value = "财务周期开始")
    @TableField("caiwu_zhouqi_start")
    private Date caiwuZhouqiStart;

    @ApiModelProperty(value = "财务垫付结束")
    @TableField("caiwu_zhouqi_end")
    private Date caiwuZhouqiEnd;

    @ApiModelProperty(value = "财务垫付单")
    @TableField("caiwu_jisuan_file")
    private String caiwuJisuanFile;

    @ApiModelProperty(value = "财务附件")
    @TableField("caiwu_fujian_files")
    private String caiwuFujianFiles;

    @ApiModelProperty(value = "财务备注")
    @TableField("caiwu_remark")
    private String caiwuRemark;

    /**
     * 机构收益 1有 2没有
     */
    @TableField("agency_revenue")
    private Integer agencyRevenue;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "运营小组名称")
    @TableField("group_name")
    private String groupName;

    @ApiModelProperty(value = "对接商务姓名")
    @TableField("business_contact")
    private String businessContact;

    @ApiModelProperty(value = "状态变更流水（JSON格式记录操作日志）")
    @TableField("status_log")
    private String statusLog;

    @ApiModelProperty(value = "发送短信")
    @TableField("send_sms")
    private Integer sendSms;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_id")
    private String createId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private String deptId;

    @ApiModelProperty(value = "部门")
    @TableField("dept_name")
    private String deptName;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ExcelField(title="主键ID（自增）",dictType="", align=2, sort=0)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @ExcelField(title="批次号",dictType="", align=2, sort=1)
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
    @ExcelField(title="垫付ID",dictType="", align=2, sort=2)
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    @ExcelField(title="项目ID",dictType="", align=2, sort=3)
    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }
    @ExcelField(title="项目简称",dictType="", align=2, sort=4)
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    @ExcelField(title="周期开始时间",dictType="", align=2, sort=5)
    public Date getZhouqiStart() {
        return zhouqiStart;
    }

    public void setZhouqiStart(Date zhouqiStart) {
        this.zhouqiStart = zhouqiStart;
    }
    @ExcelField(title="周期结束时间",dictType="", align=2, sort=6)
    public Date getZhouqiEnd() {
        return zhouqiEnd;
    }

    public void setZhouqiEnd(Date zhouqiEnd) {
        this.zhouqiEnd = zhouqiEnd;
    }
    @ExcelField(title="公司与达人分成比例（示例：70%/30%）",dictType="", align=2, sort=7)
    public String getCompanyTalentRatio() {
        return companyTalentRatio;
    }

    public void setCompanyTalentRatio(String companyTalentRatio) {
        this.companyTalentRatio = companyTalentRatio;
    }
    @ExcelField(title="参与垫付人数",dictType="", align=2, sort=8)
    public Integer getCushionPnum() {
        return cushionPnum;
    }

    public void setCushionPnum(Integer cushionPnum) {
        this.cushionPnum = cushionPnum;
    }
    @ExcelField(title="流水金额",dictType="", align=2, sort=9)
    public BigDecimal getLiushuiAmount() {
        return liushuiAmount;
    }

    public void setLiushuiAmount(BigDecimal liushuiAmount) {
        this.liushuiAmount = liushuiAmount;
    }
    @ExcelField(title="应发放权益",dictType="", align=2, sort=10)
    public Double getYingfaAmount() {
        return yingfaAmount;
    }

    public void setYingfaAmount(Double yingfaAmount) {
        this.yingfaAmount = yingfaAmount;
    }
    @ExcelField(title="已发放权益",dictType="", align=2, sort=11)
    public Double getYifaAmount() {
        return yifaAmount;
    }

    public void setYifaAmount(Double yifaAmount) {
        this.yifaAmount = yifaAmount;
    }
    @ExcelField(title="已提现金额",dictType="", align=2, sort=12)
    public BigDecimal getWithdrawnAmount() {
        return withdrawnAmount;
    }

    public void setWithdrawnAmount(BigDecimal withdrawnAmount) {
        this.withdrawnAmount = withdrawnAmount;
    }
    @ExcelField(title="待提现金额（计算字段）",dictType="", align=2, sort=13)
    public BigDecimal getUnwithdrawnAmount() {
        return unwithdrawnAmount;
    }

    public void setUnwithdrawnAmount(BigDecimal unwithdrawnAmount) {
        this.unwithdrawnAmount = unwithdrawnAmount;
    }
    @ExcelField(title="已到账金额",dictType="", align=2, sort=14)
    public BigDecimal getArriveAmount() {
        return arriveAmount;
    }

    public void setArriveAmount(BigDecimal arriveAmount) {
        this.arriveAmount = arriveAmount;
    }
    @ExcelField(title="未到账金额",dictType="", align=2, sort=15)
    public BigDecimal getNoarriveAmount() {
        return noarriveAmount;
    }

    public void setNoarriveAmount(BigDecimal noarriveAmount) {
        this.noarriveAmount = noarriveAmount;
    }
    @ExcelField(title="非标垫付人数",dictType="", align=2, sort=16)
    public Integer getFeibiaoPnum() {
        return feibiaoPnum;
    }

    public void setFeibiaoPnum(Integer feibiaoPnum) {
        this.feibiaoPnum = feibiaoPnum;
    }
    @ExcelField(title="非标人数比例",dictType="", align=2, sort=17)
    public Double getFeibiaoRate() {
        return feibiaoRate;
    }

    public void setFeibiaoRate(Double feibiaoRate) {
        this.feibiaoRate = feibiaoRate;
    }
    @ExcelField(title="非标金额",dictType="", align=2, sort=18)
    public BigDecimal getFeibiaoAmount() {
        return feibiaoAmount;
    }

    public void setFeibiaoAmount(BigDecimal feibiaoAmount) {
        this.feibiaoAmount = feibiaoAmount;
    }
    @ExcelField(title="非标金额比例",dictType="", align=2, sort=19)
    public BigDecimal getFeibiaoAmountRate() {
        return feibiaoAmountRate;
    }

    public void setFeibiaoAmountRate(BigDecimal feibiaoAmountRate) {
        this.feibiaoAmountRate = feibiaoAmountRate;
    }
    @ExcelField(title="批次状态",dictType="", align=2, sort=20)
    public Integer getBatchStatus() {
        return batchStatus;
    }

    public void setBatchStatus(Integer batchStatus) {
        this.batchStatus = batchStatus;
    }
    @ExcelField(title="批次状态",dictType="", align=2, sort=21)
    public String getBatchStatusName() {
        return batchStatusName;
    }

    public Integer getConfirmStatus() {
        return confirmStatus;
    }

    public void setConfirmStatus(Integer confirmStatus) {
        this.confirmStatus = confirmStatus;
    }

    public String getConfirmStatusName() {
        return confirmStatusName;
    }

    public void setConfirmStatusName(String confirmStatusName) {
        this.confirmStatusName = confirmStatusName;
    }

    public void setBatchStatusName(String batchStatusName) {
        this.batchStatusName = batchStatusName;
    }
    @ExcelField(title="垫付单",dictType="", align=2, sort=22)
    public String getJisuanFile() {
        return jisuanFile;
    }

    public void setJisuanFile(String jisuanFile) {
        this.jisuanFile = jisuanFile;
    }
    @ExcelField(title="附件",dictType="", align=2, sort=23)
    public String getFujianFiles() {
        return fujianFiles;
    }

    public void setFujianFiles(String fujianFiles) {
        this.fujianFiles = fujianFiles;
    }
    @ExcelField(title="财务周期开始",dictType="", align=2, sort=24)
    public Date getCaiwuZhouqiStart() {
        return caiwuZhouqiStart;
    }

    public void setCaiwuZhouqiStart(Date caiwuZhouqiStart) {
        this.caiwuZhouqiStart = caiwuZhouqiStart;
    }
    @ExcelField(title="财务垫付结束",dictType="", align=2, sort=25)
    public Date getCaiwuZhouqiEnd() {
        return caiwuZhouqiEnd;
    }

    public void setCaiwuZhouqiEnd(Date caiwuZhouqiEnd) {
        this.caiwuZhouqiEnd = caiwuZhouqiEnd;
    }
    @ExcelField(title="财务垫付单",dictType="", align=2, sort=26)
    public String getCaiwuJisuanFile() {
        return caiwuJisuanFile;
    }

    public void setCaiwuJisuanFile(String caiwuJisuanFile) {
        this.caiwuJisuanFile = caiwuJisuanFile;
    }
    @ExcelField(title="财务附件",dictType="", align=2, sort=27)
    public String getCaiwuFujianFiles() {
        return caiwuFujianFiles;
    }

    public void setCaiwuFujianFiles(String caiwuFujianFiles) {
        this.caiwuFujianFiles = caiwuFujianFiles;
    }
    @ExcelField(title="财务备注",dictType="", align=2, sort=28)
    public String getCaiwuRemark() {
        return caiwuRemark;
    }

    public void setCaiwuRemark(String caiwuRemark) {
        this.caiwuRemark = caiwuRemark;
    }
    @ExcelField(title="",dictType="", align=2, sort=29)
    public Integer getAgencyRevenue() {
        return agencyRevenue;
    }

    public void setAgencyRevenue(Integer agencyRevenue) {
        this.agencyRevenue = agencyRevenue;
    }
    @ExcelField(title="备注",dictType="", align=2, sort=30)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    @ExcelField(title="运营小组名称",dictType="", align=2, sort=31)
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    @ExcelField(title="对接商务姓名",dictType="", align=2, sort=32)
    public String getBusinessContact() {
        return businessContact;
    }

    public void setBusinessContact(String businessContact) {
        this.businessContact = businessContact;
    }
    @ExcelField(title="状态变更流水（JSON格式记录操作日志）",dictType="", align=2, sort=33)
    public String getStatusLog() {
        return statusLog;
    }

    public void setStatusLog(String statusLog) {
        this.statusLog = statusLog;
    }
    @ExcelField(title="发送短信",dictType="", align=2, sort=34)
    public Integer getSendSms() {
        return sendSms;
    }

    public void setSendSms(Integer sendSms) {
        this.sendSms = sendSms;
    }
    @ExcelField(title="创建人ID",dictType="", align=2, sort=35)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
    @ExcelField(title="创建人",dictType="", align=2, sort=36)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    @ExcelField(title="部门ID",dictType="", align=2, sort=37)
    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
    @ExcelField(title="部门",dictType="", align=2, sort=38)
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    @ExcelField(title="公司ID",dictType="", align=2, sort=39)
    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
    @ExcelField(title="公司名称",dictType="", align=2, sort=40)
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=41)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=42)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getJisuanFileMd5() {
        return jisuanFileMd5;
    }

    public void setJisuanFileMd5(String jisuanFileMd5) {
        this.jisuanFileMd5 = jisuanFileMd5;
    }

    public BigDecimal getOrganizationAmount() {
        return organizationAmount;
    }

    public void setOrganizationAmount(BigDecimal organizationAmount) {
        this.organizationAmount = organizationAmount;
    }

    @Override
    public String toString() {
        return "CushionBatch{" +
        "id=" + id +
        ", batchNo=" + batchNo +
        ", orderId=" + orderId +
        ", projectId=" + projectId +
        ", projectName=" + projectName +
        ", zhouqiStart=" + zhouqiStart +
        ", zhouqiEnd=" + zhouqiEnd +
        ", companyTalentRatio=" + companyTalentRatio +
        ", cushionPnum=" + cushionPnum +
        ", liushuiAmount=" + liushuiAmount +
        ", yingfaAmount=" + yingfaAmount +
        ", yifaAmount=" + yifaAmount +
        ", withdrawnAmount=" + withdrawnAmount +
        ", unwithdrawnAmount=" + unwithdrawnAmount +
        ", arriveAmount=" + arriveAmount +
        ", noarriveAmount=" + noarriveAmount +
        ", feibiaoPnum=" + feibiaoPnum +
        ", feibiaoRate=" + feibiaoRate +
        ", feibiaoAmount=" + feibiaoAmount +
        ", feibiaoAmountRate=" + feibiaoAmountRate +
        ", batchStatus=" + batchStatus +
        ", batchStatusName=" + batchStatusName +
        ", jisuanFile=" + jisuanFile +
        ", fujianFiles=" + fujianFiles +
        ", caiwuZhouqiStart=" + caiwuZhouqiStart +
        ", caiwuZhouqiEnd=" + caiwuZhouqiEnd +
        ", caiwuJisuanFile=" + caiwuJisuanFile +
        ", caiwuFujianFiles=" + caiwuFujianFiles +
        ", caiwuRemark=" + caiwuRemark +
        ", agencyRevenue=" + agencyRevenue +
        ", remark=" + remark +
        ", groupName=" + groupName +
        ", businessContact=" + businessContact +
        ", statusLog=" + statusLog +
        ", sendSms=" + sendSms +
        ", createId=" + createId +
        ", createName=" + createName +
        ", deptId=" + deptId +
        ", deptName=" + deptName +
        ", companyId=" + companyId +
        ", companyName=" + companyName +
        ", updateTime=" + updateTime +
        ", createTime=" + createTime +
        "}";
    }
}
