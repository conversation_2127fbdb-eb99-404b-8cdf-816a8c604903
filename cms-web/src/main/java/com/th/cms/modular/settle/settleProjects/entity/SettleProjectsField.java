package com.th.cms.modular.settle.settleProjects.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 项目灵活字段存储表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SettleProjectsField implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（自增）
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 字段配置表settle_projects_config的id
     */
    private Integer configId;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段类型，1 文本框，2多行文本框 3下拉框 4 日期选择器
     */
    private Integer fieldType;
    /**
     * 字段注释
     */
    private String fieldRemark;
    /**
     * 字段值
     */
    private String fieldValue;

    /**
     * 排序
     */
    private Integer orderWith;

    /**
     * 是否启用，1启用，0 关闭
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新ID
     */
    private Long updateId;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 项目ID
     */
    private Integer projectId;


}
