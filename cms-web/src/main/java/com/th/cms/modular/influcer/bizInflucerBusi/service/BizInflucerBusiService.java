package com.th.cms.modular.influcer.bizInflucerBusi.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.influcer.bizInflucerBusi.dao.BizInflucerBusiMapper;
import com.th.cms.modular.influcer.bizInflucerBusi.model.BizInflucerBusi;
import com.th.cms.modular.influcer.bizInflucerBusi.model.reqparam.BizInflucerBusiListParam;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizInflucerBusiService extends ServiceImpl<BizInflucerBusiMapper, BizInflucerBusi> implements IService<BizInflucerBusi> {

    @Autowired
    UserService userService;

    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizInflucerBusiListParam param) {
        QueryWrapper<BizInflucerBusi> objectQueryWrapper = new QueryWrapper<>();
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);
        return layuiPageInfo;
    }

    public BizInflucerBusi queryBusi(String platId,Long influcerId){
        QueryWrapper<BizInflucerBusi> objectQueryWrapper = new QueryWrapper<>();

        objectQueryWrapper.lambda().eq(BizInflucerBusi::getInflucerId,influcerId).eq(BizInflucerBusi::getPlatId,platId);

        List<BizInflucerBusi> dlist = list(objectQueryWrapper);

        if(dlist.size()>0){
            dlist.get(0);
        }

        return null;

    }

    /**
     * 达人绑定商务
     * @param influcerIds
     * @param businessId
     */
    public void influcerBindBusiness(List<Long> influcerIds, Long businessId){
        LambdaQueryWrapper<BizInflucerBusi> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BizInflucerBusi::getInflucerId,influcerIds);
        List<BizInflucerBusi> list = this.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)){
            list.stream().forEach(e -> {
                if (influcerIds.contains(Long.parseLong(e.getInflucerId()))){
                    influcerIds.remove(Long.parseLong(e.getInflucerId()));
                }
            });
        }

        User user = userService.getById(businessId);
        List<BizInflucerBusi> saveInflucerBusi = new ArrayList<>();
        influcerIds.stream().forEach(e -> {
            BizInflucerBusi influcerBusi = new BizInflucerBusi();
            influcerBusi.setInflucerId(String.valueOf(e));
            if (Objects.nonNull(user)){
                influcerBusi.setBusiName(user.getName());
            }
            influcerBusi.setBusiId(businessId);
//            influcerBusi.setBusiName("");
            influcerBusi.setCreateTime(new Date());
            influcerBusi.setUpdateTime(new Date());
            saveInflucerBusi.add(influcerBusi);
        });

        if (CollectionUtil.isNotEmpty(saveInflucerBusi)){
            this.saveBatch(saveInflucerBusi);
        }
    }

    public List<BizInflucerBusi> queryByBusinessId(Long businessId){
        QueryWrapper<BizInflucerBusi> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizInflucerBusi::getBusiId,businessId);
        return this.list(queryWrapper);
    }

}
