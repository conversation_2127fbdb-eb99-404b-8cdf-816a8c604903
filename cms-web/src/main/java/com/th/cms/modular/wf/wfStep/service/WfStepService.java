package com.th.cms.modular.wf.wfStep.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.config.web.UserContextHolder;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.enums.ApprovalStatus;
import com.th.cms.modular.sysmesage.util.MessageSendHelper;
import com.th.cms.modular.wf.wfApprovalRecord.model.WfApprovalRecord;
import com.th.cms.modular.wf.wfApprovalRecord.service.WfApprovalRecordService;
import com.th.cms.modular.wf.wfApprovalRequest.model.WfApprovalRequest;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestService;
import com.th.cms.modular.wf.wfApprovalRequestView.model.WfApprovalRequestView;
import com.th.cms.modular.wf.wfApprovalRequestView.service.WfApprovalRequestViewService;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.WfInflucerSubmitApprove;
import com.th.cms.modular.wf.wfStep.dao.WfStepMapper;
import com.th.cms.modular.wf.wfStep.model.*;
import com.th.cms.modular.wf.wfStep.model.reqparam.WfStepListParam;
import com.th.cms.modular.wf.wfType.dao.WfTypeMapper;
import com.th.cms.modular.wf.wfType.model.WfType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class WfStepService extends ServiceImpl<WfStepMapper, WfStep> implements IService<WfStep> {

    @Resource
    private WfTypeMapper wfTypeMapper;
    @Resource
    private WfApprovalRequestService wfApprovalRequestService;
    @Resource
    private WfApprovalRecordService wfApprovalRecordService;
    @Resource
    private WfApprovalRequestViewService wfApprovalRequestViewService;
    @Autowired
    private MessageSendHelper messageSendHelper;

    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(WfStepListParam param) {
        QueryWrapper<WfStep> objectQueryWrapper = new QueryWrapper<>();
        if (param.getTypeId() != null) {
            objectQueryWrapper.lambda().eq(WfStep::getTypeId, param.getTypeId());
        }
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;

    }

    /**
     * @param approvalRequest
     * @param typeId          流程id
     */
    public WfStep generateApprovalRecords(WfInflucerSubmitApprove influcerSubmitApprove,
                                           WfApprovalRequest approvalRequest, Long typeId) {

        //stepId 为空时，起始节点
        QueryWrapper<WfStep> stepQueryWrapper = new QueryWrapper<>();
        stepQueryWrapper.lambda().eq(WfStep::getTypeId, typeId);
        List<WfStep> stepList = list(stepQueryWrapper);

        //审批记录
        List<WfApprovalRecord> approvalRecords = new ArrayList<>();
        //审批单可见逻辑
        List<WfApprovalRequestView> viewList = new ArrayList<>();

        WfStep wfStep = null;
        boolean flag = true;

        for (int i = 0; i < stepList.size(); i++) {
            WfStep currentStep = stepList.get(i);
            WfStep nextStep = (i + 1) == stepList.size() ? null : stepList.get(i + 1);

            String stepType = currentStep.getStepType();

            //第一个待审批流程节点
            if (flag && stepType.equals(String.valueOf(StepNodeType.ProcessNode.getCode()))) {
                //生成当前节点，所有待审批记录
                generateStepApprovalRecords(approvalRecords, viewList, approvalRequest,
                        currentStep, nextStep, true);

                if (influcerSubmitApprove != null) {

                    influcerSubmitApprove.setStepId(currentStep.getId());
                    influcerSubmitApprove.setStatus(ApprovalStatus.UNDER_REVIEW.getCode());
                    influcerSubmitApprove.setStatusName(getStatusName(currentStep, nextStep, ApprovalStatus.UNDER_REVIEW.getCode()));
                    wfStep = currentStep;



                }
                flag = false;
            } else {

                //生成当前节点，所有待审批记录
                generateStepApprovalRecords(approvalRecords, viewList, approvalRequest,
                        currentStep, nextStep, false);
            }
        }
        if (!CollectionUtils.isEmpty(approvalRecords)) {
            wfApprovalRecordService.saveBatch(approvalRecords);
        }
        if (!CollectionUtils.isEmpty(viewList)) {
            wfApprovalRequestViewService.saveBatch(viewList);
        }

        return wfStep;
    }

    /**
     * @param approvalRecords  待保存的审批列表
     * @param approvalRequest  审批单
     * @param wfStep           当前审批节点
     * @param nextStep         下一个审批节点
     * @param firstApproveStep 是否第一审批节点(非第一个节点，开始-审批-结束 审批-审批-...)
     */
    private void generateStepApprovalRecords(List<WfApprovalRecord> approvalRecords,
                                             List<WfApprovalRequestView> viewList,
                                             WfApprovalRequest approvalRequest,
                                             WfStep wfStep, WfStep nextStep,
                                             boolean firstApproveStep) {

        String stepType = wfStep.getStepType();

        //起始节点特殊处理
        if (isNumeric(stepType) && StepNodeType.StartNode == StepNodeType.fromCode(Integer.parseInt(stepType))) {

            approvalRecords.add(new WfApprovalRecord()
                    //审批单
                    .setRequestId(approvalRequest.getId())
                    //业务信息
                    .setBillType(approvalRequest.getBillType())
                    .setBillNo(approvalRequest.getBillNo())
                    //步骤信息
                    .setStepId(wfStep.getId())
                    .setStepName(wfStep.getStepName())
                    .setStepOrder(wfStep.getStepOrder())
                    //审批信息
                    .setApproverId(UserContextHolder.getUserId())
                    .setApproverName(UserContextHolder.getUserName())
                    .setApprovalStatus(ApprovalStatus.APPROVED.getCode())
                    .setApprovalStatusName(ApprovalStatus.APPROVED.getDescription())
                    //下一个节点信息
                    .setNextStepId(null != nextStep ? nextStep.getId() : null)
                    .setNextStepName(null != nextStep ? nextStep.getStepName() : null)
                    //依次审批===按照顺序审批，设置审批序号
                    .setApproveSort(1)
                    .setCreateTime(new Date()));

            viewList.add(new WfApprovalRequestView()
                    .setRequestId(approvalRequest.getId())
                    .setUserId(UserContextHolder.getUserId())
                    .setCreateTime(new Date()));

            messageSendHelper.sendMessage("WORKFLOW_APPROVE", null, UserContextHolder.getUserId(), null);

            return;
        }

        //以下是审批节点，或者结束节点
        String assignId = wfStep.getAssignId();
        if (StringUtils.isEmpty(assignId)) {
            return;
        }

        //审批人，部门，角色 信息
        String[] idSplit = assignId.split(",");
        String[] nameSplit = wfStep.getAssignNames().split(",");

        //生成节点待审批记录
        for (int i = 0; i < idSplit.length; i++) {

            String appId = idSplit[i];
            String appName = nameSplit[i];

            //添加待审批记录
            approvalRecords.add(new WfApprovalRecord()
                    //审批单
                    .setRequestId(approvalRequest.getId())
                    //业务信息
                    .setBillType(approvalRequest.getBillType())
                    .setBillNo(approvalRequest.getBillNo())
                    //步骤信息
                    .setStepId(wfStep.getId())
                    .setStepName(wfStep.getStepName())
                    .setStepOrder(wfStep.getStepOrder())
                    //审批信息
                    .setApproverId(Long.parseLong(appId))
                    .setApproverName(appName)
                    .setApprovalStatus(firstApproveStep ? ApprovalStatus.UNDER_REVIEW.getCode() : ApprovalStatus.DRAFT.getCode())
                    .setApprovalStatusName(firstApproveStep ? ApprovalStatus.UNDER_REVIEW.getDescription() : ApprovalStatus.DRAFT.getDescription())
                    //下一个节点信息
                    .setNextStepId(null != nextStep ? nextStep.getId() : null)
                    .setNextStepName(null != nextStep ? nextStep.getStepName() : null)
                    //依次审批===按照顺序审批，设置审批序号
                    .setApproveSort(ApproveType.SortApprove == ApproveType.fromCode(wfStep.getApproveType()) ? i + 1 : 1)
                    .setCreateTime(new Date()));

            if (!StringUtils.isEmpty(wfStep.getAssignType())) {

                Integer assignTypeValue = Integer.valueOf(wfStep.getAssignType());

                viewList.add(new WfApprovalRequestView()
                        .setRequestId(approvalRequest.getId())
                        .setUserId(AssignType.user.code.equals(assignTypeValue) ? Long.parseLong(appId) : null)
                        .setDeptId(AssignType.dept.code.equals(assignTypeValue) ? Long.parseLong(appId) : null)
                        .setRoleId(AssignType.role.code.equals(assignTypeValue) ? Long.parseLong(appId) : null)
                        .setCreateTime(new Date()));
            }
        }
    }

    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 匹配整数或小数（包括负数）
        return str.matches("-?\\d+(\\.\\d+)?");
    }

    /**
     * 审批节点
     *
     * @param dto
     */
    public void approve(ApproveRequestDTO dto) {

        int recordId = dto.getRecordId();
        Integer approveStatus = dto.getApproveStatus();
        String approvalComment = dto.getApprovalComment();
        List<ApprovalFileVO> approvalFiles = dto.getApprovalFiles();

        WfApprovalRecord approvalRecord = wfApprovalRecordService.getById(recordId);

        //last 节点，触发额外逻辑
        if (approveStatus != null) {

            Long requestId = approvalRecord.getRequestId();

            ApprovalStatus approvalStatus = ApprovalStatus.fromCode(approveStatus);
            if (approvalStatus == null) {
                return;
            }

            if (approvalStatus == ApprovalStatus.REVOKED) {

                //撤销操作
                revokedApproval(requestId, approvalRecord, approvalStatus);

            } else if (approvalStatus == ApprovalStatus.APPROVED || approvalStatus == ApprovalStatus.REJECTED) {

                //审批 同意/驳回
                approvedOrRejectedApproval(requestId, approvalRecord, approvalStatus, approvalComment);
            }
        }
    }

    private void revokedApproval(Long requestId, WfApprovalRecord approvalRecord, ApprovalStatus approvalStatus) {

        //历史审批通过或者驳回均走以下逻辑
        //修改审批单
        UpdateWrapper<WfApprovalRequest> requestUpdateWrapper = new UpdateWrapper<>();
        requestUpdateWrapper.lambda()
                .set(WfApprovalRequest::getRequestStatus, ApprovalStatus.UNDER_REVIEW.getCode())
                .eq(WfApprovalRequest::getId, requestId);
        wfApprovalRequestService.update(requestUpdateWrapper);

        //修改流程标识
        wfTypeMapper.updateStatus(requestId, ApprovalStatus.UNDER_REVIEW.getCode());

        //审批单改为待审批
        approvalRecord.setApprovalStatus(ApprovalStatus.UNDER_REVIEW.getCode());
        wfApprovalRecordService.updateById(approvalRecord);
    }

    private void approvedOrRejectedApproval(Long requestId, WfApprovalRecord approvalRecord,
                                            ApprovalStatus approvalStatus, String approvalComment) {

        //TODO 考虑会签，依次审批情况 暂不考虑
        Long nextStepId = approvalRecord.getNextStepId();

        if (approvalStatus.equals(ApprovalStatus.REJECTED)) {
            approvalRecord.setApprovalStatus(ApprovalStatus.REJECTED.getCode());
            approvalRecord.setApprovalStatusName(ApprovalStatus.REJECTED.getDescription());
            approvalRecord.setApprovalComment(approvalComment);
        }

        //同意
        if (approvalStatus.equals(ApprovalStatus.APPROVED)) {
            approvalRecord.setApprovalStatus(ApprovalStatus.APPROVED.getCode());
            approvalRecord.setApprovalStatusName(ApprovalStatus.APPROVED.getDescription());
        }

        Long userId = UserContextHolder.getUserId();
        if (null != userId) {
            approvalRecord.setApproverId(userId);
            approvalRecord.setApproverName(UserContextHolder.getUserName());
        } else {
            approvalRecord.setApproverId(1L);
            approvalRecord.setApproverName("ABCD");
        }
        approvalRecord.setUpdateTime(new Date());

        wfApprovalRecordService.updateById(approvalRecord);

        //到达最后一个节点
        if (nextStepId == null) {

            updateApproveOrders(requestId, approvalStatus.getCode(), approvalStatus.getDescription());

        } else {

            //设置下一个审批节点，为待审批状态

            //TODO 考虑当前节点多人审批情况 暂不考虑

            if (approvalStatus.equals(ApprovalStatus.APPROVED)) {

                UpdateWrapper<WfApprovalRecord> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().set(WfApprovalRecord::getApprovalStatus, ApprovalStatus.UNDER_REVIEW.getCode())
                        .eq(WfApprovalRecord::getRequestId, requestId)
                        .eq(WfApprovalRecord::getStepId, nextStepId);
                wfApprovalRecordService.update(updateWrapper);
            } else {
                updateApproveOrders(requestId, ApprovalStatus.REJECTED.getCode(), ApprovalStatus.REJECTED.getDescription());
            }
        }
    }

    private void updateApproveOrders(Long requestId, Integer approvalStatus, String approvalStatusName) {
        //审批单
        WfApprovalRequest approvalRequest = wfApprovalRequestService.getById(requestId);


        approvalRequest.setRequestStatus(approvalStatus);
        approvalRequest.setRequestStatusName(approvalStatusName);
        wfApprovalRequestService.updateById(approvalRequest);

        String[] split = approvalRequest.getBillNo().split("-");
        String billId = split[split.length - 1];

        WfType wfType = wfTypeMapper.selectById(billId);
        wfType.setStatus(approvalStatus);
        wfTypeMapper.updateById(wfType);
    }

    /**
     * 获取最新的状态名
     *
     * @param step
     * @param nextStep
     * @param status
     * @return
     */
    public String getStatusName(WfStep step, WfStep nextStep, Integer status) {

        ApprovalStatus approvalStatus = ApprovalStatus.fromCode(status);

        if (approvalStatus == null) {
            return null;
        }

        switch (approvalStatus) {
            case UNDER_REVIEW:
                return "待" + step.getShortName();
            case RE_UNDER_REVIEW:
                return "修改待" + step.getShortName();// + "待" +nextStep.getShortName();
            case APPROVED:
                if (nextStep == null) {
                    return step.getShortName() + "中";
                } else {
                    if (nextStep.getStepType().equals(StepNodeType.EndNode.getCode().toString())) {
                        return nextStep.getShortName() + "中";
                    }
                    return "已" + step.getShortName() + "待" +nextStep.getShortName();
                }
            case REJECTED:
                return step.getShortName() + "未成功";

        }

        return null;
    }

}
