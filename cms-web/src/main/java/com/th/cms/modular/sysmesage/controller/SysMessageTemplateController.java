package com.th.cms.modular.sysmesage.controller;


import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.modular.sysmesage.dto.SysMessageTemplateDetailDTO;
import com.th.cms.modular.sysmesage.dto.SysMessageTemplateListDTO;
import com.th.cms.modular.sysmesage.dto.SysMessageTemplateListRequestDTO;
import com.th.cms.modular.sysmesage.dto.SystemMessageTemplateAddDTO;
import com.th.cms.modular.sysmesage.service.ISysMessageService;
import com.th.cms.modular.sysmesage.service.ISysMessageTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统消息主表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@RestController
@RequestMapping("/sysMessageTemplate")
public class SysMessageTemplateController {
    @Autowired
    private ISysMessageService sysMessageService;
    @Autowired
    private ISysMessageTemplateService sysMessageTemplateService;

    /**
     * 新增
     *
     * @param requestDTO 参数
     * @return 结果
     */
    @RequestMapping(value = "/addOrUpdate")
    public ResponseData<Long> addOrUpdate(@RequestBody @Valid SystemMessageTemplateAddDTO requestDTO) throws Exception {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageTemplateService.addOrUpdate(requestDTO, user));
    }

    /**
     * 删除
     *
     * @param id 主键
     * @return 删除结果
     */
    @RequestMapping(value = "/delete")
    public ResponseData<Boolean> delete(@RequestParam("id") Long id) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageTemplateService.deleteTemplate(id,user));
    }




    /**
     * 查询
     */
    @RequestMapping(value = "/list")
    public LayuiPageInfo<SysMessageTemplateListDTO> list(@RequestBody SysMessageTemplateListRequestDTO requestDTO) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return sysMessageTemplateService.listByPage(requestDTO,user);
    }

    /**
     * 查询
     */
    @RequestMapping(value = "/listAll")
    public ResponseData<List<SysMessageTemplateListDTO>> listAll(@RequestParam(value =  "keyword", required = false)String keyword) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageTemplateService.listAll(keyword,user));
    }
    /**
     * 模板详情
     */
    @RequestMapping(value = "/detail")
    public ResponseData<SysMessageTemplateDetailDTO> detail(@RequestParam(value =  "id", required = true) Long id) {
        ShiroUser user = ShiroKit.getUser();
        if (user == null) {
            throw new RuntimeException("用户未登录");
        }
        return ResponseData.success(sysMessageTemplateService.detail(id,user));
    }

}
