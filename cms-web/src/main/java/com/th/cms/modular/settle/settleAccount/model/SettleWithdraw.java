package com.th.cms.modular.settle.settleAccount.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <p>
 * 达人结算订单表
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="SettleWithdraw对象", description="达人结算订单表")
public class SettleWithdraw implements Serializable {


    @ApiModelProperty(value = "主键ID（自增）")
    @ExcelProperty(value = "流水编号（勿改）", index = 0)
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "达人ID")
    @ExcelProperty(value = "达人ID", index = 1)
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "达人昵称")
    @ExcelProperty(value = "达人昵称", index = 2)
    @TableField("nick_name")
    private String nickName;

    @ApiModelProperty(value = "达人手机号")
    @ExcelProperty(value = "达人手机号", index = 3)
    @TableField("user_tel")
    private String userTel;

    @ApiModelProperty(value = "达人姓名")
    @ExcelProperty(value = "达人姓名", index = 4)
    @TableField("user_name")
    private String userName;

    @ApiModelProperty(value = "账户号")
    @ExcelProperty(value = "账户号", index = 5)
    @TableField("account_no")
    private String accountNo;

    @ApiModelProperty(value = "提现金额")
    @ExcelProperty(value = "提现金额", index = 6)
    @TableField("tixian_amount")
    private BigDecimal tixianAmount;

    @ApiModelProperty(value = "提现状态")
    @TableField("withdraw_status")
    @ExcelIgnore
    private Integer withdrawStatus;

    @ApiModelProperty(value = "导入文件")
    @ExcelIgnore
    @TableField("excels_imprt")
    private String excelsImprt;

    @ApiModelProperty(value = "导出文件")
    @ExcelIgnore
    @TableField("excels_exprt")
    private String excelsExprt;
    @ApiModelProperty(value = "提现状态")
    @ExcelProperty(value = "提现状态", index = 7)
    @TableField("withdraw_status_name")
    private String withdrawStatusName;

    @ApiModelProperty(value = "导出状态")
    @ExcelIgnore
    @TableField("export_status")
    private Integer exportStatus;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_id")
    @ExcelIgnore
    private String createId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_name")
    @ExcelIgnore
    private String createName;

    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    @ExcelIgnore
    private String deptId;

    @ApiModelProperty(value = "部门")
    @TableField("dept_name")
    @ExcelIgnore
    private String deptName;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    @ExcelIgnore
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    @TableField("company_name")
    @ExcelIgnore
    private String companyName;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    @ExcelIgnore
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间", index = 8)
    @TableField("create_time")
    private Date createTime;
    @ApiModelProperty(value = "项目ID")
    @ExcelProperty(value = "项目ID", index = 9)
    @TableField("project_id")
    private Long projectId;

    @ApiModelProperty(value = "失败原因")
    @ExcelProperty(value = "提现失败原因")
    @TableField("failed_reason")
    private String failedReason;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
    public String getUserTel() {
        return userTel;
    }

    public String getExcelsImprt() {
        return excelsImprt;
    }

    public void setExcelsImprt(String excelsImprt) {
        this.excelsImprt = excelsImprt;
    }

    public String getExcelsExprt() {
        return excelsExprt;
    }

    public void setExcelsExprt(String excelsExprt) {
        this.excelsExprt = excelsExprt;
    }

    public void setUserTel(String userTel) {
        this.userTel = userTel;
    }
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
    public BigDecimal getTixianAmount() {
        return tixianAmount;
    }

    public void setTixianAmount(BigDecimal tixianAmount) {
        this.tixianAmount = tixianAmount;
    }
    public Integer getWithdrawStatus() {
        return withdrawStatus;
    }

    public void setWithdrawStatus(Integer withdrawStatus) {
        this.withdrawStatus = withdrawStatus;
    }
    public String getWithdrawStatusName() {
        return withdrawStatusName;
    }

    public void setWithdrawStatusName(String withdrawStatusName) {
        this.withdrawStatusName = withdrawStatusName;
    }
    public Integer getExportStatus() {
        return exportStatus;
    }

    public void setExportStatus(Integer exportStatus) {
        this.exportStatus = exportStatus;
    }
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    @Override
    public String toString() {
        return "SettleWithdraw{" +
                "id=" + id +
                ", userId=" + userId +
                ", nickName='" + nickName + '\'' +
                ", userTel='" + userTel + '\'' +
                ", userName='" + userName + '\'' +
                ", accountNo='" + accountNo + '\'' +
                ", tixianAmount=" + tixianAmount +
                ", withdrawStatus=" + withdrawStatus +
                ", excelsImprt='" + excelsImprt + '\'' +
                ", excelsExprt='" + excelsExprt + '\'' +
                ", withdrawStatusName='" + withdrawStatusName + '\'' +
                ", exportStatus=" + exportStatus +
                ", createId='" + createId + '\'' +
                ", createName='" + createName + '\'' +
                ", deptId='" + deptId + '\'' +
                ", deptName='" + deptName + '\'' +
                ", companyId='" + companyId + '\'' +
                ", companyName='" + companyName + '\'' +
                ", updateTime=" + updateTime +
                ", createTime=" + createTime +
                ", projectId=" + projectId +
                ", failedReason='" + failedReason + '\'' +
                '}';
    }
}
