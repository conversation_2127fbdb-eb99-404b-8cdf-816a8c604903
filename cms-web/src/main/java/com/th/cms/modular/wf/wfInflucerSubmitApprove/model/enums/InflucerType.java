package com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums;

public enum InflucerType {

    VIDEO(1, "视频达人"),
    LIVE(2, "直播达人"),
    ONLINE(3, "电商达人"),
    PICTURE(3, "图文达人"),
    ;

    public Integer value;
    public String name;

    InflucerType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static InflucerType getType(Integer value) {
        InflucerType[] statusList = InflucerType.values();
        for (InflucerType status : statusList) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return null;
    }
}
