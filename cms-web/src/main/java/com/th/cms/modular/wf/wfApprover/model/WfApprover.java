package com.th.cms.modular.wf.wfApprover.model;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="WfApprover对象", description="")
public class WfApprover implements Serializable {


    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "关联步骤ID")
    @TableField("step_id")
    private Long stepId;

    @ApiModelProperty(value = "步奏名称")
    @TableField("step_name")
    private String stepName;

    @ApiModelProperty(value = "指定类型（USER/ROLE/DEPT）")
    @TableField("assign_type")
    private String assignType;

    @ApiModelProperty(value = "用户ID/角色编码/部门编码")
    @TableField("assign_value")
    private String assignValue;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ExcelField(title="",dictType="", align=2, sort=0)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @ExcelField(title="关联步骤ID",dictType="", align=2, sort=1)
    public Long getStepId() {
        return stepId;
    }

    public void setStepId(Long stepId) {
        this.stepId = stepId;
    }
    @ExcelField(title="步奏名称",dictType="", align=2, sort=2)
    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }
    @ExcelField(title="指定类型（USER/ROLE/DEPT）",dictType="", align=2, sort=3)
    public String getAssignType() {
        return assignType;
    }

    public void setAssignType(String assignType) {
        this.assignType = assignType;
    }
    @ExcelField(title="用户ID/角色编码/部门编码",dictType="", align=2, sort=4)
    public String getAssignValue() {
        return assignValue;
    }

    public void setAssignValue(String assignValue) {
        this.assignValue = assignValue;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=5)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=6)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "WfApprover{" +
        "id=" + id +
        ", stepId=" + stepId +
        ", stepName=" + stepName +
        ", assignType=" + assignType +
        ", assignValue=" + assignValue +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
