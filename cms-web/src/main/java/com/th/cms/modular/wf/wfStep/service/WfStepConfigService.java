package com.th.cms.modular.wf.wfStep.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.modular.wf.wfStep.dao.WfStepConfigMapper;
import com.th.cms.modular.wf.wfStep.model.WfStepConfig;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class WfStepConfigService extends ServiceImpl<WfStepConfigMapper, WfStepConfig> implements IService<WfStepConfig> {

}
