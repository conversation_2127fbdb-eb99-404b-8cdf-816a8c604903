package com.th.cms.modular.wf.wfStep.model;

public enum StepNodeType {

    /**
     * <option value="1">起点</option>
     * <option value="2">过程</option>
     * <option value="3">结束</option>
     */
    StartNode(1, "开始节点"),
    ProcessNode(2, "审批节点"),
    EndNode(9, "结束节点"),
    ;
    /**
     * 状态码（持久化到数据库的值）
     */
    private final Integer code;

    /**
     * 状态描述（前端展示用）
     */
    private final String name;

    StepNodeType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态码
     * @return 对应的枚举实例，未找到时返回null
     */
    public static StepNodeType fromCode(Integer code) {
        for (StepNodeType status : StepNodeType.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

}
