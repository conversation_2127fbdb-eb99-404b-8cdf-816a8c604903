package com.th.cms.modular.influcer.cooperationPlatform.controller;

import cn.stylefeng.roses.core.reqres.response.ResponseData;
import cn.stylefeng.roses.kernel.model.enums.YesOrNotEnum;
import com.th.cms.core.dto.FrontPageResult;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatformIntroduction;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatformRelatedAnnouncements;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatformTrackStandard;
import com.th.cms.modular.influcer.cooperationPlatform.dto.*;
import com.th.cms.modular.influcer.cooperationPlatform.service.CooperationPlatformService;
import com.th.cms.modular.settle.settleAccount.model.BizInflucerTop;
import com.th.cms.util.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 合作平台Controller
 *
 * <AUTHOR>
 */

@Controller
@RequestMapping("user/cooperationPlatform")
public class CooperationPlatformController {

    
    @Autowired
    CooperationPlatformService cooperationPlatformService;
    @Autowired
    private UserUtil userUtil;
    /**
     * 获取一级平台列表,新增时候用
     */
    @RequestMapping(value = "/listOneLevelPlatforms")
    @ResponseBody
    public ResponseData<List<BizPlatform>> listOneLevelPlatforms(HttpServletRequest request) {
        return ResponseData.success(cooperationPlatformService.listOneLevelPlatforms());
    }

    /**
     * 获二级平台列表,新增时候用
     */
    @RequestMapping(value = "/listTwoLevelPlatforms")
    @ResponseBody
    public ResponseData<List<BizPlatform>> listTwoLevelPlatforms(@RequestParam(value = "platId")Long platId, HttpServletRequest request) {
        return ResponseData.success(cooperationPlatformService.listTwoLevelPlatforms(platId,userUtil.getUserNotNull(request)));
    }
    /**
     * 新增平台
     */
    @RequestMapping(value = "/addPlatform")
    @ResponseBody
    public ResponseData<Boolean> addPlatform(@RequestParam(value = "platId")Long platId, HttpServletRequest request) {
        return ResponseData.success(cooperationPlatformService.addPlatform(platId,  userUtil.getUserNotNull(request), YesOrNotEnum.Y.getCode()));
    }
    /**
     * 合作平台列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public ResponseData<FrontPageResult<AppPlatformDTO>> list(@RequestBody CooperationPlatformListParamDTO paramDTO, HttpServletRequest request) {
        return ResponseData.success(cooperationPlatformService.list(paramDTO));
    }
    /**
     * 更新关闭和开启
     */
    @RequestMapping(value = "/toggle")
    @ResponseBody
    public ResponseData<Boolean> toggle(@RequestParam(value = "platId")Long platId,  @RequestParam(value = "status")Integer status, HttpServletRequest request) {
        return ResponseData.success(cooperationPlatformService.toggle(platId,status,userUtil.getUserNotNull(request)));
    }
    /**
     * 删除平台
     */
    @RequestMapping(value = "/deleteAppPlatform")
    @ResponseBody
    public ResponseData<Boolean> deleteAppPlatform(@RequestParam(value = "id")Long id, HttpServletRequest request) {
        return ResponseData.success(cooperationPlatformService.deleteAppPlatform(id));
    }
    /**
     * 创建榜单数据
     */
    @RequestMapping(value = "/createTop")
    @ResponseBody
    public ResponseData<BizInflucerTop> createTop(@RequestBody CooperationPlatformTopInfoDTO topInfoDTO) {
        return cooperationPlatformService.createTop(topInfoDTO);
    }

    /**
     * 更新榜单数据
     */
    @RequestMapping(value = "/updateTop")
    @ResponseBody
    public ResponseData<BizInflucerTop> updateTop(CooperationPlatformTopInfoDTO topInfoDTO) {
        return cooperationPlatformService.updateTop(topInfoDTO);
    }







    /**
     * 查询榜单列表
     */
    @RequestMapping(value = "/topList")
    @ResponseBody
    public ResponseData<BizInflucerTop> topList(CooperationPlatformTopParamDTO topParamDTO) {
        return cooperationPlatformService.topList(topParamDTO);
    }

    /**
     * 设置平台简介
     */
    @RequestMapping(value = "/setIntroduction")
    @ResponseBody
    public ResponseData<Boolean> setIntroduction(@RequestBody CooperationPlatformIntroductionInfoDTO introductionInfoDTO) {
        return cooperationPlatformService.setIntroduction(introductionInfoDTO);
    }

    /**
     * 获取平台简介
     * @param platformId 平台ID
     * @return 结果
     */
    @RequestMapping(value = "/getIntroduction")
    @ResponseBody
    public ResponseData<BizPlatformIntroduction> getIntroduction(@RequestParam Long platformId) {
        return cooperationPlatformService.getIntroduction(platformId);
    }
    
    /**
     * 创建赛道数据
     */
    @RequestMapping(value = "/createTrack")
    @ResponseBody
    public ResponseData<BizPlatformTrackStandard> createTrack(@RequestBody CooperationPlatformTrackInfoDTO trackInfoDTO) {
        return cooperationPlatformService.createTrack(trackInfoDTO);
    }

    /**
     * 更新赛道数据
     */
    @RequestMapping(value = "/updateTrack")
    @ResponseBody
    public ResponseData<BizPlatformTrackStandard> updateTrack(@RequestBody CooperationPlatformTrackInfoDTO trackInfoDTO) {
        return cooperationPlatformService.updateTrack(trackInfoDTO);
    }

    /**
     * 赛道数据列表
     */
    @RequestMapping(value = "/trackList")
    @ResponseBody
    public ResponseData<List<BizPlatformTrackStandard>> trackList(@RequestParam Long platformId) {
        return cooperationPlatformService.trackList(platformId);
    }

    /**
     * 删除赛道数据
     */
    @RequestMapping(value = "/deleteTrack")
    @ResponseBody
    public ResponseData<Void> deleteTrack(@RequestParam Long id) {
        return cooperationPlatformService.deleteTrack(id);
    }

    /**
     * 创建公告数据
     */
    @RequestMapping(value = "/createAnnouncement")
    @ResponseBody
    public ResponseData<BizPlatformRelatedAnnouncements> createAnnouncement(@RequestBody CooperationPlatformAnnouncementInfoDTO announcementInfoDTO) {
        return cooperationPlatformService.createAnnouncement(announcementInfoDTO);
    }

    /**
     * 更新公告数据
     */
    @RequestMapping(value = "/updateAnnouncement")
    @ResponseBody
    public ResponseData<BizPlatformRelatedAnnouncements> updateAnnouncement(@RequestBody CooperationPlatformAnnouncementInfoDTO announcementInfoDTO) {
        return cooperationPlatformService.updateAnnouncement(announcementInfoDTO);
    }

    /**
     * 公告数据列表
     */
    @RequestMapping(value = "/announcementList")
    @ResponseBody
    public ResponseData<List<BizPlatformRelatedAnnouncements>> announcementList(@RequestParam Long platformId) {
        return cooperationPlatformService.announcementList(platformId);
    }

    /**
     * 删除公告数据
     */
    @RequestMapping(value = "/deleteAnnouncement")
    @ResponseBody
    public ResponseData<Void> deleteAnnouncement(@RequestParam Long id) {
        return cooperationPlatformService.deleteAnnouncement(id);
    }
}
