
package com.th.cms.modular.system.model;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户传输bean
 *
 * <AUTHOR>
 * @Date 2017/5/5 22:40
 */
@Data
public class UserDto {

    private Long userId;
    private String account;
    private String password;
    private String name;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthday;
    private String sex;
    private String email;
    private String phone;
    private String roleId;
    private Long deptId;
    private String status;
    private String avatar;

    private String deptName;
    /**
     * 上级领导ID
     */
    private Long leaderId;

    private String authPlats;//认证平台
    private String coopPlats;//合作平台
}
