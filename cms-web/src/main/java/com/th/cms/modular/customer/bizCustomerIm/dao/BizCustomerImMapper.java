package com.th.cms.modular.customer.bizCustomerIm.dao;

import com.th.cms.modular.customer.bizCustomerIm.model.BizCustomerIm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface BizCustomerImMapper extends BaseMapper<BizCustomerIm> {

    @Select("select customer_accid from biz_customer_im where customer_id = #{customerId}")
    String selectAccId(@Param("customerId") String customerId);
}
