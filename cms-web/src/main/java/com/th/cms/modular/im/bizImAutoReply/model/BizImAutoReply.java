package com.th.cms.modular.im.bizImAutoReply.model;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="BizImAutoReply对象", description="")
public class BizImAutoReply implements Serializable {


    @ApiModelProperty(value = "id")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "内容")
    @TableField("reply_content")
    private String replyContent;

    @ApiModelProperty(value = "图片")
    @TableField("reply_image")
    private String replyImage;

    @ApiModelProperty(value = "视频")
    @TableField("reply_video")
    private String replyVideo;

    @ApiModelProperty(value = "类型")
    @TableField("reply_type")
    private Integer replyType;

    @TableField(exist = false)
    private String replyTypeName;

    @ApiModelProperty(value = "启用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @TableField("sort_num")
    private Integer sortNum;


}
