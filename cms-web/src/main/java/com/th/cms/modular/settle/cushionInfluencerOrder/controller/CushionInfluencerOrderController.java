package com.th.cms.modular.settle.cushionInfluencerOrder.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.annotion.Permission;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.modular.enums.ApprovalBillType;
import com.th.cms.modular.settle.cushionInfluencerOrder.model.CushionInfluencerOrder;
import com.th.cms.modular.settle.cushionInfluencerOrder.model.reqparam.CushionInfluencerOrderListParam;
import com.th.cms.modular.settle.cushionInfluencerOrder.service.CushionInfluencerOrderExtService;
import com.th.cms.modular.settle.cushionInfluencerOrder.service.CushionInfluencerOrderService;
import com.th.cms.modular.settle.cushionBatch.model.CushionBatch;
import com.th.cms.modular.settle.cushionBatch.service.CushionBatchExtService;
import com.th.cms.modular.settle.cushionBatch.service.CushionBatchService;
import com.th.cms.modular.settle.cushionOrder.model.CushionOrder;
import com.th.cms.modular.settle.cushionOrder.service.CushionOrderExtService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.wf.wfApprovalRequest.model.WfRequestRsp;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestExtService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 垫付管理控制器
 *
 * <AUTHOR>
 * @Date 2025-04-01 16:16:47
 */
@Controller
@RequestMapping("/cushionInfluencerOrder")
public class CushionInfluencerOrderController extends BaseController {

    private String PREFIX = "/modular/cushionInfluencerOrder/cushionInfluencerOrder/";
    @Autowired
    CushionInfluencerOrderExtService cushionInfluencerOrderExtService;
    @Autowired
    private CushionInfluencerOrderService cushionInfluencerOrderService;
    @Autowired
    CushionBatchExtService cushionBatchExtService;

    @Autowired
    CushionBatchService cushionBatchService;
    @Autowired
    CushionOrderExtService cushionOrderExtService;
    @Autowired
    WfApprovalRequestExtService wfApprovalRequestExtService;
    /**
     * 跳转到垫付管理首页
     */
    @RequestMapping("/idx")
    public String index(@RequestParam  String cushionBatchId, Model model) {

        CushionBatch cushionBatch = cushionBatchService.getById(cushionBatchId);
        CushionOrder cushionOrder = cushionOrderExtService.queryCushionOrder(cushionBatch.getOrderId()+"");
        model.addAttribute("cushionBatch", cushionBatch);

        model.addAttribute("cushionOrder", cushionOrder);
        model.addAttribute("item", cushionOrder);

        WfRequestRsp<SettleProjects> wfRequestRsp = wfApprovalRequestExtService.queryReqAuditInfos(cushionBatch.getBatchNo(), ApprovalBillType.SettleOrder, ShiroKit.getUser().getId() + "");
        model.addAttribute("wfRequestRsp", wfRequestRsp);
        model.addAttribute("authuser", ShiroKit.getUser());
        model.addAttribute("isshow", 2);

        return PREFIX + "cushionInfluencerOrder.html";
    }

    /**
     * 跳转到添加垫付管理
     */
    @Permission
    @RequestMapping("/cushionInfluencerOrder_add")
    public String cushionInfluencerOrderAdd() {
        return PREFIX + "cushionInfluencerOrder_add.html";
    }

    /**
     * 跳转到修改垫付管理
     */
    @Permission
    @RequestMapping("/cushionInfluencerOrder_update")
    public String cushionInfluencerOrderUpdate(@RequestParam  Integer cushionInfluencerOrderId, Model model) {
        CushionInfluencerOrder cushionInfluencerOrder = cushionInfluencerOrderExtService.queryById(cushionInfluencerOrderId);
        model.addAttribute("item",cushionInfluencerOrder);
        LogObjectHolder.me().set(cushionInfluencerOrder);
        return PREFIX + "cushionInfluencerOrder_edit.html";
    }
    @RequestMapping("/cushionInfluencerOrder_detail")
    public String cushionInfluencerOrderDetail(@RequestParam Integer cushionInfluencerOrderId, Model model) {
        CushionInfluencerOrder cushionInfluencerOrder = cushionInfluencerOrderExtService.queryById(cushionInfluencerOrderId);
        model.addAttribute("item",cushionInfluencerOrder);
        LogObjectHolder.me().set(cushionInfluencerOrder);
        return PREFIX + "cushionInfluencerOrder_detail.html";
    }
    /**
     * 获取垫付管理列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(CushionInfluencerOrderListParam cushionInfluencerOrderParam) {
        return cushionInfluencerOrderService.findPageBySpec(cushionInfluencerOrderParam);
    }

    /**
     * 新增垫付管理
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/cushionInfluencerOrder/cushionInfluencerOrder_add")
    @ResponseBody
    public ResponseData add(CushionInfluencerOrder cushionInfluencerOrder) {
         cushionInfluencerOrderExtService.save(cushionInfluencerOrder);
         return ResponseData.success();
    }

    /**
     * 删除垫付管理
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  cushionInfluencerOrderId) {
        cushionInfluencerOrderExtService.removeById(cushionInfluencerOrderId);
         return ResponseData.success();
    }

    /**
     * 修改垫付管理
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/cushionInfluencerOrder/cushionInfluencerOrder_update")
    @ResponseBody
    public ResponseData update(CushionInfluencerOrder cushionInfluencerOrder) {
        cushionInfluencerOrderExtService.updateById(cushionInfluencerOrder);
        return ResponseData.success();
    }

    /**
     * 垫付管理详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  cushionInfluencerOrderId) {
       CushionInfluencerOrder detail = cushionInfluencerOrderExtService.queryById(cushionInfluencerOrderId);
       return ResponseData.success(detail);
    }
}
