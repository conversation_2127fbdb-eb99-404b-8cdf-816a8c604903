package com.th.cms.modular.settle.cushionBatch.service;

import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.settle.cushionBatch.model.CushionBatch;
import com.th.cms.modular.settle.settleProjects.third.MessageProjectHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;

@Service
public class CushionBatchExtService {
    @Autowired
    CushionBatchService cushionBatchService;
    @Autowired
    private MessageProjectHelper messageProjectHelper;

    public void save(CushionBatch cushionBatch) {
        setEnumsName(cushionBatch);
        ShiroUtils.setAddAuthInfo(cushionBatch);
        cushionBatchService.save(cushionBatch);
        messageProjectHelper.sendCushionSubmit(cushionBatch);
    }

    public void updateById(CushionBatch cushionBatch) {
        setEnumsName(cushionBatch);
        DataAuthService.checkPermision(cushionBatchService.getById(cushionBatch.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(cushionBatch);
        CushionBatch cushionBatchDb = queryById(cushionBatch.getId());

        cushionBatchService.updateById(cushionBatch);
    }

    public CushionBatch queryById(Serializable id) {
        CushionBatch cushionBatch = cushionBatchService.getById(id);
        DataAuthService.checkPermision(cushionBatch, DataPermisionTypeEnum.ByComp);

        return cushionBatch;
    }
    public void removeById(Serializable id) {
        CushionBatch cushionBatch = cushionBatchService.getById(id);
        DataAuthService.checkPermision(cushionBatch, DataPermisionTypeEnum.ByComp);
        CushionBatch cushionBatchRecord = new CushionBatch();
        cushionBatchRecord.setId(cushionBatch.getId());

        cushionBatchService.updateById(cushionBatchRecord);
    }

    private void setEnumsName(CushionBatch cushionBatch) {
    }

}
