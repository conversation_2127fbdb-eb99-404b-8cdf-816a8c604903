package com.th.cms.modular.workorder.bizWorkorder.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.customer.bizCustomerIm.dao.BizCustomerImMapper;
import com.th.cms.modular.customer.bizCustomerIm.model.BizCustomerIm;
import com.th.cms.modular.im.bizImLk.dao.BizImLkMapper;
import com.th.cms.modular.im.bizImLk.model.BizImLk;
import com.th.cms.modular.im.bizImLk.model.ImLkStatus;
import com.th.cms.modular.influcer.bizInflucer.dao.BizInflucerMapper;
import com.th.cms.modular.workorder.bizWorkorder.model.BizWorkorder;
import com.th.cms.modular.workorder.bizWorkorder.model.CloseLinkDTO;
import com.th.cms.modular.workorder.bizWorkorder.model.WorkSourceType;
import com.th.cms.modular.workorder.bizWorkorder.model.WorkStatus;
import com.th.cms.modular.im.MessageSendType;
import com.th.cms.modular.im.YxImService;
import com.th.cms.modular.workorder.bizWorkorder.third.MessageWorkorderHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BizWorkorderExtService {
    @Autowired
    BizWorkorderService bizWorkorderService;
    @Autowired
    private YxImService yxImService;
    @Autowired
    private BizImLkMapper bizImLkMapper;
    @Autowired
    private BizCustomerImMapper bizCustomerImMapper;
    @Autowired
    private BizInflucerMapper bizInflucerMapper;
    @Autowired
    private MessageWorkorderHelper messageWorkorderHelper;


    public BizWorkorder createWorkorder(BizWorkorder bizWorkorder) {

        //判断是否存在会话，不存在创建
        QueryWrapper<BizImLk> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizImLk::getCustomerId, bizWorkorder.getToId())
                .eq(BizImLk::getInflcId, bizWorkorder.getInfucerId());
        Integer lkCount = bizImLkMapper.selectCount(queryWrapper);
        if (lkCount == 0) {

            String customerAcccId = bizCustomerImMapper.selectAccId(bizWorkorder.getToId());
            String influcerAcccId = bizInflucerMapper.selectAccId(bizWorkorder.getInfucerId());

            log.info("customerAcccId=" + customerAcccId + "_______influcerAcccId=" + influcerAcccId);
            yxImService.createConversion(customerAcccId, 1, influcerAcccId);
        }

        BizWorkorder save = save(bizWorkorder);
        messageWorkorderHelper.new_ticket_message(save,ShiroKit.getUser().getId());
        return save;

    }

    public BizWorkorder save(BizWorkorder bizWorkorder) {
        setEnumsName(bizWorkorder);
        ShiroUtils.setAddAuthInfo(bizWorkorder);

        //
        ShiroUser user = ShiroKit.getUser();
        bizWorkorder.setCreateId(user.getId());
        bizWorkorder.setCreateName(user.getName());
        bizWorkorder.setToId(bizWorkorder.getCustomerId() + "");
        bizWorkorder.setToName(bizWorkorder.getCustomerName());
        bizWorkorder.setWorkerSource(WorkSourceType.kefu.value);
        bizWorkorder.setQuestStatus(WorkStatus.daichuli.value);
        bizWorkorder.setQuestStatusname(WorkStatus.daichuli.name);
        bizWorkorder.setCreateTime(new Date());
        bizWorkorder.setUpdateTime(new Date());

        bizWorkorderService.save(bizWorkorder);

        return bizWorkorder;
    }

    public void updateById(BizWorkorder bizWorkorder) {
        setEnumsName(bizWorkorder);
        DataAuthService.checkPermision(bizWorkorderService.getById(bizWorkorder.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(bizWorkorder);
        BizWorkorder bizWorkorderDb = queryById(bizWorkorder.getId());

        bizWorkorderService.updateById(bizWorkorder);
    }

    public BizWorkorder queryById(Serializable id) {
        BizWorkorder bizWorkorder = bizWorkorderService.getById(id);
        DataAuthService.checkPermision(bizWorkorder, DataPermisionTypeEnum.ByComp);
        if (bizWorkorder != null && StringUtils.isNotBlank(bizWorkorder.getImgs())) {
            bizWorkorder.setImgList(Arrays.asList(bizWorkorder.getImgs().split(",")));
        }

        return bizWorkorder;
    }

    public void removeById(Serializable id) {
        BizWorkorder bizWorkorder = bizWorkorderService.getById(id);
        DataAuthService.checkPermision(bizWorkorder, DataPermisionTypeEnum.ByComp);
        BizWorkorder bizWorkorderRecord = new BizWorkorder();
        bizWorkorderRecord.setId(bizWorkorder.getId());
        bizWorkorderRecord.setQuestStatus(WorkStatus.closed.value);
        bizWorkorderRecord.setQuestStatusname(WorkStatus.closed.name);
        bizWorkorderService.updateById(bizWorkorderRecord);
    }

    /**
     * 撤回
     *
     * @param id
     */
    public void revoteById(Serializable id) {
        BizWorkorder bizWorkorder = bizWorkorderService.getById(id);
        DataAuthService.checkPermision(bizWorkorder, DataPermisionTypeEnum.ByComp);
        BizWorkorder bizWorkorderRecord = new BizWorkorder();
        bizWorkorderRecord.setId(bizWorkorder.getId());

        bizWorkorderService.updateById(bizWorkorderRecord);
    }


    public void zhuanfaById(BizWorkorder bizWorkorder) {
        bizWorkorder.setToId(bizWorkorder.getCustomerId() + "");
        bizWorkorder.setToName(bizWorkorder.getCustomerName());

        BizWorkorder bizWorkorderRecord = new BizWorkorder();
        bizWorkorderRecord.setId(bizWorkorder.getId());
        bizWorkorderRecord.setToId(bizWorkorder.getToId());
        bizWorkorderRecord.setToName(bizWorkorder.getToName());
        bizWorkorderRecord.setCustomerId(bizWorkorder.getCustomerId());
        bizWorkorderRecord.setCustomerName(bizWorkorder.getCustomerName());

        bizWorkorderService.updateById(bizWorkorder);
    }

    private void setEnumsName(BizWorkorder bizWorkorder) {
    }

    public long getGongdanNum(Long userId, List<WorkStatus> gdanStusLst) {
        List<Integer> statusList = gdanStusLst.stream().map(a -> a.value).collect(Collectors.toList());

//        userId 换 客服id
        QueryWrapper<BizCustomerIm> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("user_id", userId);
        BizCustomerIm bizCustomerIm = bizCustomerImMapper.selectOne(objectQueryWrapper);

        long wkCt = bizWorkorderService.lambdaQuery()
                .eq(BizWorkorder::getToId, bizCustomerIm.getCustomerId())
                .in(BizWorkorder::getQuestStatus, statusList).count();

        return wkCt;
    }

    /**
     * 关闭工单
     * 发送工单完毕，客户端，显示（确认结束，重新发起）
     *
     * @param id
     * @return
     */
    public Boolean closeWorkorder(Integer id) {

        BizWorkorder bizWorkorder = bizWorkorderService.getById(id);
        bizWorkorder.setQuestStatus(WorkStatus.closed.value);
        bizWorkorder.setQuestStatusname(WorkStatus.closed.name);
        bizWorkorder.setUpdateTime(new Date());
        bizWorkorderService.updateById(bizWorkorder);

        //查询链路
        QueryWrapper<BizImLk> lkQueryWrapper = new QueryWrapper<>();
        lkQueryWrapper.lambda()
                .eq(BizImLk::getCustomerId, bizWorkorder.getCustomerId())
                .eq(BizImLk::getInflcId, bizWorkorder.getInfucerId())
                .orderByDesc(BizImLk::getCreateTime);
        List<BizImLk> bizImLks = bizImLkMapper.selectList(lkQueryWrapper);

        if (!CollectionUtils.isEmpty(bizImLks)) {

            BizImLk bizImLk = bizImLks.get(0);
            bizImLk.setCloseTime(new Date());
            bizImLk.setZixunStatus(ImLkStatus.cstmclosed.value);
            bizImLkMapper.updateById(bizImLk);

            JSONObject jsonObject = new JSONObject();
            jsonObject.remove("isWorkOrder");
            jsonObject.put("id", bizWorkorder.getId());//工单id
            jsonObject.put("isOverWorkOrder", true);//结束

            String context = "亲爱的达人，您反馈的问题" + bizWorkorder.getTitle() + "已经处理完成";
            //发送确认消息
            yxImService.sendConversion(bizImLk.getCustomerAccid(), 1, bizImLk.getInflcAccid(),
                    MessageSendType.CUSTOMER.value, "text", context, jsonObject);
        }

        return true;
    }

    /**
     * 关闭的未关闭的会话
     *
     * @param dto
     * @return
     */
    public Boolean closeLink(CloseLinkDTO dto) {

        QueryWrapper<BizImLk> lkQueryWrapper = new QueryWrapper<>();
        lkQueryWrapper.lambda().eq(BizImLk::getConversionId, dto.getConversionId())
                .gt(BizImLk::getZixunStatus, ImLkStatus.daifenp.value);

        List<BizImLk> bizImLks = bizImLkMapper.selectList(lkQueryWrapper);

        if (!CollectionUtils.isEmpty(bizImLks)) {

            closeLinkList(bizImLks);
            return true;
        } else {

            String cid = reversalConversionId(dto.getConversionId());

            QueryWrapper<BizImLk> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BizImLk::getConversionId, cid)
                    .gt(BizImLk::getZixunStatus, ImLkStatus.daifenp.value);

            bizImLks = bizImLkMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(bizImLks)) {
                closeLinkList(bizImLks);
                return true;
            }
        }

        return false;
    }

    private void closeLinkList(List<BizImLk> bizImLks) {
        bizImLks.forEach(im -> {
            im.setCloseTime(new Date());
            im.setUpdateTime(new Date());
            im.setZixunStatus(ImLkStatus.cstmclosed.value);
            bizImLkMapper.updateById(im);
        });
    }

    private String reversalConversionId(String conversionId) {
        String[] split = conversionId.split("\\|");
        return split[2] + "|" + split[1] + "|" + split[0];
    }
}
