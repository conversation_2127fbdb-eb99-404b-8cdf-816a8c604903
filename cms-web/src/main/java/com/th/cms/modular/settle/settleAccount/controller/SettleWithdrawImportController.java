package com.th.cms.modular.settle.settleAccount.controller;


import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.modular.settle.settleAccount.model.SettleWithdrawImport;
import com.th.cms.modular.settle.settleAccount.model.reqparam.ImportSettleWithdrawFileListParam;
import com.th.cms.modular.settle.settleAccount.service.SettleWithdrawImportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Controller
@RequestMapping("/settleWithdrawImport")
public class SettleWithdrawImportController extends BaseController {

    @Autowired
    private SettleWithdrawImportService settleWithdrawImportService;

    private String PREFIX = "/modular/settleAccount/settleWithdrawImport/";

    /**
     * 跳转到提现管理首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "settleWithdrawImport.html";
    }


    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo importSettleWithdrawFileList(ImportSettleWithdrawFileListParam param){
        return settleWithdrawImportService.findPageBySpec(param);
    }

    @RequestMapping("/settleWithdrawImport_detail")
    public String importDetail(Long id, Model model){
        SettleWithdrawImport settleWithdrawImport = settleWithdrawImportService.getById(id);
        model.addAttribute("item",settleWithdrawImport);
        LogObjectHolder.me().set(settleWithdrawImport);
        return PREFIX + "settleWithdrawImport_detail.html";
    }

    @RequestMapping("/settleWithdrawImport_reject")
    public String settleWithdrawImportReject(Long id){
        return PREFIX + "settleWithdrawImport_reject.html";
    }

    @ResponseBody
    @RequestMapping("/detail")
    public Object detail(Long id){
        SettleWithdrawImport settleWithdrawImport = settleWithdrawImportService.getById(id);
        return ResponseData.success(settleWithdrawImport);
    }


    @PostMapping("/reject")
    @ResponseBody
    public ResponseData reject(@RequestBody SettleWithdrawImport settleWithdrawImport){
        settleWithdrawImportService.reject(settleWithdrawImport);
        return ResponseData.success();
    }

    @RequestMapping("/approved")
    public String approve(Long id){
        settleWithdrawImportService.approve(id);
        return PREFIX + "settleWithdrawImport.html";
    }



}
