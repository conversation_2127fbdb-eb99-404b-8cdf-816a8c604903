package com.th.cms.modular.im.bizImAutoQuestion.service;

import com.th.cms.core.util.IntegerUtils;
import com.th.cms.modular.im.bizImAutoQuestion.model.BizImAutoQuestion;

import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.Serializable;
@Service
public class BizImAutoQuestionExtService {
    @Autowired
    BizImAutoQuestionService bizImAutoQuestionService;

    public void save(BizImAutoQuestion bizImAutoQuestion){
        setEnumsName(bizImAutoQuestion);
        ShiroUtils.setAddAuthInfo(bizImAutoQuestion);
        bizImAutoQuestionService.save(bizImAutoQuestion);
    }

    public void updateById(BizImAutoQuestion bizImAutoQuestion){
        setEnumsName(bizImAutoQuestion);
        DataAuthService.checkPermision(bizImAutoQuestionService.getById(bizImAutoQuestion.getId()),DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(bizImAutoQuestion);
        BizImAutoQuestion bizImAutoQuestionDb = queryById(bizImAutoQuestion.getId());

        bizImAutoQuestionService.updateById(bizImAutoQuestion);
    }
    public BizImAutoQuestion queryById(Serializable id){
        BizImAutoQuestion bizImAutoQuestion = bizImAutoQuestionService.getById(id);
        DataAuthService.checkPermision(bizImAutoQuestion,DataPermisionTypeEnum.ByComp);

        return   bizImAutoQuestion;
    }
    public void removeById(Serializable id){
       BizImAutoQuestion bizImAutoQuestion = bizImAutoQuestionService.getById(id);
       DataAuthService.checkPermision(bizImAutoQuestion,DataPermisionTypeEnum.ByComp);
       BizImAutoQuestion bizImAutoQuestionRecord = new BizImAutoQuestion();
       bizImAutoQuestionRecord.setId(bizImAutoQuestion.getId());

       bizImAutoQuestionService.updateById( bizImAutoQuestionRecord);
    }
    private void setEnumsName(BizImAutoQuestion bizImAutoQuestion){
    }

    public void updSort(BizImAutoQuestion bizImAutoQuestion) {

        BizImAutoQuestion bizAutoQuestionDb = queryById(bizImAutoQuestion.getId());
        if (bizAutoQuestionDb != null) {
            BizImAutoQuestion autoQuestionRecord = new BizImAutoQuestion();
            autoQuestionRecord.setId(bizImAutoQuestion.getId());
            autoQuestionRecord.setSort(IntegerUtils.add(bizAutoQuestionDb.getSort(), bizImAutoQuestion.getSort()));
            bizImAutoQuestionService.updateById(autoQuestionRecord);

//            ShiroUser userLogin = ShiroKit.getUser();
//            LogManager.me().executeLog(LogTaskFactory.bussinessLog(userLogin.getId(),
//                    "客服顺序",
//                    BizCustomerExtService.class.getName(),
//                    "save",
//                    userLogin.getDeptName() + " " + userLogin.getName() + " " +
//                            userLogin.getId() + " " + bizCustomer.getSort() + " 客服ID：" + bizCustomer.getId()));

        }
    }
}
