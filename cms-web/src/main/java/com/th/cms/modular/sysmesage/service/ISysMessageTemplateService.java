package com.th.cms.modular.sysmesage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.modular.sysmesage.dto.SysMessageTemplateDetailDTO;
import com.th.cms.modular.sysmesage.dto.SysMessageTemplateListDTO;
import com.th.cms.modular.sysmesage.dto.SysMessageTemplateListRequestDTO;
import com.th.cms.modular.sysmesage.dto.SystemMessageTemplateAddDTO;
import com.th.cms.modular.sysmesage.entity.SysMessageTemplate;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 消息模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface ISysMessageTemplateService extends IService<SysMessageTemplate> {
    /**
     * 新增或者更新
     * @param requestDTO 请求参数
     * @param user 当前登录用户
     * @return 新增或者更新结果
     */
    Long addOrUpdate(@Valid SystemMessageTemplateAddDTO requestDTO, ShiroUser user);

    /**
     * 删除模板
     * @param id id
     * @param user 当前登录用户
     * @return 删除结果
     */
    Long deleteTemplate(Long id, ShiroUser user);

    /**
     *  分页查询
     * @param requestDTO 请求参数
     * @param user 当前登录用户
     * @return  结果
     */
    LayuiPageInfo<SysMessageTemplateListDTO> listByPage(SysMessageTemplateListRequestDTO requestDTO, ShiroUser user);

    /**
     * 详情
     * @param requestDTO 请求参数
     * @param user 当前登录用户
     * @return 详情
     */
    SysMessageTemplateDetailDTO detail(Long requestDTO, ShiroUser user);

    /**
     * 查询所有书记
     * @param keyword 关键字
     * @param user 当前登录用户
     * @return 查询结果
     */
    List<SysMessageTemplateListDTO>listAll(String keyword, ShiroUser user);
}
