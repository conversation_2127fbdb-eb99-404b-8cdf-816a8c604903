<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.influcer.bizInflucerProject.dao.BizInflucerProjectMapper">
    <select id="getProjectByInflucerIdAndProjectIds" resultType="com.th.cms.modular.bizClue.dto.MyInflucerProjectDTO">
        select
        bip.influcer_id as influcerId,
        sp.project_name as projectName,
        sp.id as projectId
        from biz_influcer_project bip  ,settle_projects sp
        where bip.project_id = sp.id and bip.influcer_id  = #{influcerId} and sp.id in
            <foreach collection="prjectIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
    <select id="getPlatfromByInflucerId" resultType="com.th.cms.modular.bizClue.dto.MyInflucerCooperationPlatformDTO"
            parameterType="java.lang.Long">
        select distinct bip.platform_id as platformId  ,bp.icon_pic as icon,bp.plat_name as platformName
        from biz_influcer_project bip ,biz_platform bp where bip.platform_id = bp.id and bip.influcer_id = #{influcerId};
    </select>
    <select id="myInflucerDetailProjectsPage" resultType="com.th.cms.modular.bizClue.dto.MyInflucerDetailProjectDTO"
            parameterType="com.th.cms.modular.bizClue.dto.MyInflucerDetailProjectRequestDTO">
        select
            sp.project_name,
            sp.platform,
            sp.project_type_name,
            sp.project_starttime,
            sp.project_endtime,
            sp.start_date,
            sp.project_status,
            sp.business_owner_name,
            sp.project_status_name
        from biz_influcer_project bip ,settle_projects sp
        where
            bip.project_id = sp.id
          and bip.influcer_id = #{influencerId}
    </select>
</mapper>