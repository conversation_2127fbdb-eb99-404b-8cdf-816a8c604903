package com.th.cms.modular.settle.settleAccount.controller;

import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import com.th.cms.modular.influcer.bizInflucer.service.BizInflucerService;
import com.th.cms.modular.oss.service.AliUploadOssImg;
import com.th.cms.modular.settle.settleAccount.model.SettleAccount;
import com.th.cms.modular.settle.settleAccount.model.SettleWithdrawAccount;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountService;
import com.th.cms.modular.settle.settleAccount.service.SettleWithdrawAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.beans.factory.annotation.Autowired;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.common.annotion.Permission;
import org.springframework.web.bind.annotation.RequestParam;
import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.settle.settleAccount.model.SettleAccountLog;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountLogService;
import com.th.cms.modular.settle.settleAccount.service.SettleAccountLogExtService;
import com.th.cms.modular.settle.settleAccount.model.reqparam.SettleAccountLogListParam;

import java.util.List;

/**
 * 账户流水控制器
 *
 * <AUTHOR>
 * @Date 2025-04-15 14:22:04
 */
@Controller
@RequestMapping("/settleAccountLog")
@Slf4j
public class SettleAccountLogController extends BaseController {

    private String PREFIX = "/modular/settleAccount/settleAccountLog/";
    @Autowired
    SettleAccountLogExtService settleAccountLogExtService;
    @Autowired
    private SettleAccountLogService settleAccountLogService;
    @Autowired
    BizInflucerService bizInflucerService;
    @Autowired
    SettleAccountService settleAccountService;
    @Autowired
    SettleWithdrawAccountService settleWithdrawAccountService;
    /**
     * 跳转到账户流水首页
     */
    @RequestMapping("")
    public String index(@RequestParam  String accountNo, Model model) {
        //accountNo
        List<SettleAccount> settleAccountList = settleAccountService.lambdaQuery().eq(SettleAccount::getAccountNo,accountNo).list();

        if(settleAccountList.size()>0){
            SettleAccount settleAccount = settleAccountList.get(0);
            Long userId = settleAccount.getUserId();

            BizInflucer bizInflucer = bizInflucerService.getById(userId);
            if(bizInflucer!=null){
                if(StringUtils.isNotBlank(bizInflucer.getIcon())){
                    String icUrl = AliUploadOssImg.getExpireUrl(bizInflucer.getIcon(), null);
                    bizInflucer.setIcon(icUrl);
                }
                // 手机号脱敏
                String maskedPhone = StringUtils.overlay(bizInflucer.getLoginTel(), "****", 3, 7);
                // 身份证脱敏
                String maskedIdCard = StringUtils.overlay(bizInflucer.getCard(), "********", 6, 14);
                bizInflucer.setLoginTel(maskedPhone);
                bizInflucer.setCard(maskedIdCard);
                model.addAttribute("bizInflucer",bizInflucer);
            }else{
                model.addAttribute("bizInflucer",new BizInflucer());
            }

            SettleWithdrawAccount settleWithdrawAccount = settleWithdrawAccountService.queryByUid(userId);
            if(settleWithdrawAccount!=null){
                String maskedZhifubao =StringUtils.overlay(settleWithdrawAccount.getZhifuAccount(), "****", 3, 7);
                settleWithdrawAccount.setZhifuAccount(maskedZhifubao);
                model.addAttribute("settleWithdrawAccount",settleWithdrawAccount);
            }else{
                model.addAttribute("settleWithdrawAccount",new SettleWithdrawAccount());
            }
            model.addAttribute("settleAccount",settleAccount);
        }else {
            model.addAttribute("settleWithdrawAccount",new SettleWithdrawAccount());
            model.addAttribute("settleAccount",new SettleAccount());
            model.addAttribute("bizInflucer",new BizInflucer());
        }

        return PREFIX + "settleAccountLog.html";
    }

    /**
     * 跳转到添加账户流水
     */
    @Permission
    @RequestMapping("/settleAccountLog_add")
    public String settleAccountLogAdd() {
        return PREFIX + "settleAccountLog_add.html";
    }

    /**
     * 跳转到修改账户流水
     */
    @Permission
    @RequestMapping("/settleAccountLog_update")
    public String settleAccountLogUpdate(@RequestParam  Integer settleAccountLogId, Model model) {
        SettleAccountLog settleAccountLog = settleAccountLogExtService.queryById(settleAccountLogId);
        model.addAttribute("item",settleAccountLog);
        LogObjectHolder.me().set(settleAccountLog);
        return PREFIX + "settleAccountLog_edit.html";
    }
    @RequestMapping("/settleAccountLog_detail")
    public String settleAccountLogDetail(@RequestParam Integer settleAccountLogId, Model model) {
        SettleAccountLog settleAccountLog = settleAccountLogExtService.queryById(settleAccountLogId);
        model.addAttribute("item",settleAccountLog);
        LogObjectHolder.me().set(settleAccountLog);
        return PREFIX + "settleAccountLog_detail.html";
    }
    /**
     * 获取账户流水列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(SettleAccountLogListParam settleAccountLogParam) {
        return settleAccountLogService.findPageBySpec(settleAccountLogParam);
    }

    /**
     * 新增账户流水
     */
    @RequestMapping(value = "/add")
    @RequiresPermissions("/settleAccountLog/settleAccountLog_add")
    @ResponseBody
    public ResponseData add(SettleAccountLog settleAccountLog) {
         settleAccountLogExtService.save(settleAccountLog);
         return ResponseData.success();
    }

    /**
     * 删除账户流水
     */
    @RequestMapping(value = "/delete")
    @Permission
    @ResponseBody
    public ResponseData delete(@RequestParam Integer  settleAccountLogId) {
        settleAccountLogExtService.removeById(settleAccountLogId);
         return ResponseData.success();
    }

    /**
     * 修改账户流水
     */
    @RequestMapping(value = "/update")
    @RequiresPermissions("/settleAccountLog/settleAccountLog_update")
    @ResponseBody
    public ResponseData update(SettleAccountLog settleAccountLog) {
        settleAccountLogExtService.updateById(settleAccountLog);
        return ResponseData.success();
    }

    /**
     * 账户流水详情
     */
    @RequestMapping(value = "/detail")
    @ResponseBody
    public Object detail(@RequestParam Integer  settleAccountLogId) {
       SettleAccountLog detail = settleAccountLogExtService.queryById(settleAccountLogId);
       return ResponseData.success(detail);
    }
}
