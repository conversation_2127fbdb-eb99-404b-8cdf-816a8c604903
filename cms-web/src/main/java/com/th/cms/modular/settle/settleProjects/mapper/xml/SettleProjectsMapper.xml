<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.settle.settleProjects.mapper.SettleProjectsMapper">
    <select id="queryProjectByPage" resultType="com.th.cms.modular.settle.settleProjects.entity.SettleProjects">
        SELECT id, serial_number, project_name, project_code, project_type_id, project_type_name, commission_rate, commission_rate_id, commission_to_daren, commission_to_comp, revenue_type, revenue_type_name, plat_lst_name, platform_id, platform, yiji_platform, erji_platform, sanji_platform, quant_target, settlement_cycle, settlement_cycle_name, start_date, project_starttime, project_endtime, agency_revenue, business_owner, business_owner_name, created_by, project_status, project_status_name, operations, project_desc, beijing_file, project_zhixing_fa, zhixing_files, jigou_name, comp_name, comp_name_id, create_time, update_time, create_id, create_name, dept_id, dept_name, company_id, company_name
        FROM mingyuehao.settle_projects where 1=1
        <if test="record.projectName!=null and record.projectName!=''">
            project_name like concat('%',#{record.projectName},'%')
        </if>

    </select>
    <select id="testRole" resultType="com.th.cms.modular.settle.settleProjects.entity.SettleProjects">
        SELECT id, serial_number, project_name, project_code, project_type_id, project_type_name, commission_rate, commission_rate_id, commission_to_daren, commission_to_comp, revenue_type, revenue_type_name, plat_lst_name, platform_id, platform, yiji_platform, erji_platform, sanji_platform, quant_target, settlement_cycle, settlement_cycle_name, start_date, project_starttime, project_endtime, agency_revenue, business_owner, business_owner_name, created_by, project_status, project_status_name, operations, project_desc, beijing_file, project_zhixing_fa, zhixing_files, jigou_name, comp_name, comp_name_id, create_time, update_time, create_id, create_name, dept_id, dept_name, company_id, company_name
        FROM mingyuehao.settle_projects where 1=1
        <if test="record.projectName!=null and record.projectName!=''">
            project_name like concat('%',#{record.projectName},'%')
        </if>
    </select>
    <select id="testDept" resultType="com.th.cms.modular.settle.settleProjects.entity.SettleProjects">
        SELECT id, serial_number, project_name, project_code, project_type_id, project_type_name, commission_rate, commission_rate_id, commission_to_daren, commission_to_comp, revenue_type, revenue_type_name, plat_lst_name, platform_id, platform, yiji_platform, erji_platform, sanji_platform, quant_target, settlement_cycle, settlement_cycle_name, start_date, project_starttime, project_endtime, agency_revenue, business_owner, business_owner_name, created_by, project_status, project_status_name, operations, project_desc, beijing_file, project_zhixing_fa, zhixing_files, jigou_name, comp_name, comp_name_id, create_time, update_time, create_id, create_name, dept_id, dept_name, company_id, company_name
        FROM mingyuehao.settle_projects where 1=1
        <if test="record.projectName!=null and record.projectName!=''">
            project_name like concat('%',#{record.projectName},'%')
        </if>
    </select>
    <select id="testSubordinate" resultType="com.th.cms.modular.settle.settleProjects.entity.SettleProjects">
        SELECT id, serial_number, project_name, project_code, project_type_id, project_type_name, commission_rate, commission_rate_id, commission_to_daren, commission_to_comp, revenue_type, revenue_type_name, plat_lst_name, platform_id, platform, yiji_platform, erji_platform, sanji_platform, quant_target, settlement_cycle, settlement_cycle_name, start_date, project_starttime, project_endtime, agency_revenue, business_owner, business_owner_name, created_by, project_status, project_status_name, operations, project_desc, beijing_file, project_zhixing_fa, zhixing_files, jigou_name, comp_name, comp_name_id, create_time, update_time, create_id, create_name, dept_id, dept_name, company_id, company_name
        FROM mingyuehao.settle_projects where 1=1
        <if test="record.projectName!=null and record.projectName!=''">
            project_name like concat('%',#{record.projectName},'%')
        </if>
    </select>
</mapper>