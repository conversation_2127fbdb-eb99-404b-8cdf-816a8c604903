package com.th.cms.modular.im.bizImLk.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.bizNomalEnums.model.BizNomalEnums;
import com.th.cms.modular.bizNomalEnums.service.BizNomalEnumsService;
import com.th.cms.modular.customer.bizCustomer.dao.BizCustomerMapper;
import com.th.cms.modular.customer.bizCustomer.model.BizCustomer;
import com.th.cms.modular.customer.bizCustomer.model.CustomerType;
import com.th.cms.modular.customer.bizCustomerIm.dao.BizCustomerImMapper;
import com.th.cms.modular.customer.bizCustomerIm.model.BizCustomerIm;
import com.th.cms.modular.im.ImChatType;
import com.th.cms.modular.im.YxImService;
import com.th.cms.modular.im.bizImLk.dao.BizImLkMapper;
import com.th.cms.modular.im.bizImLk.model.*;
import com.th.cms.modular.im.bizImLk.model.reqparam.BizImLkListParam;
import com.th.cms.modular.im.bizImLkEvaluate.model.BizImLkEvaluate;
import com.th.cms.modular.im.bizImLkEvaluate.service.BizImLkEvaluateService;
import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizImLkService extends ServiceImpl<BizImLkMapper, BizImLk> implements IService<BizImLk> {
    @Resource
    private BizCustomerMapper bizCustomerMapper;
    @Resource
    private YxImService yxImService;
    @Resource
    private BizCustomerImMapper bizCustomerImMapper;
    @Resource
    private BizImLkEvaluateService bizImLkEvaluateService;
    @Resource
    private BizNomalEnumsService bizNomalEnumsService;
    @Autowired
    private BizImLkMapper bizImLkMapper;

    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizImLkListParam param) {

        QueryWrapper<BizImLk> objectQueryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<BizImLk> lambda = objectQueryWrapper.lambda();

        if (!StringUtils.isEmpty(param.getCondition())) {

            lambda.and(wq -> wq
                    .like(BizImLk::getInflcName, "%" + param.getCondition() + "%")
                    .or()
                    .like(BizImLk::getCustomerName, "%" + param.getCondition() + "%"));
        }

        //达人信息不能为空，为空时，视为群聊情况
        lambda.isNotNull(BizImLk::getInflcAccid).orderByDesc(BizImLk::getCreateTime);

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);

        List<BizImLk> data = layuiPageInfo.getData();

        setGroupCustomerInfo(data);

        setEvaluateInfo(data);

        layuiPageInfo.setData(data);
        return layuiPageInfo;
    }

    //针对群聊，customer 为空的情况
    private void setGroupCustomerInfo(List<BizImLk> data) {
        if (!CollectionUtils.isEmpty(data)) {

            for (BizImLk datum : data) {
                //群组条件
                if (StringUtils.isEmpty(datum.getCustomerAccid())) {

                    String[] split = datum.getConversionId().split("\\|");
                    String groupConversionId = split[2];

                    QueryWrapper<BizImLk> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda()
                            .isNull(BizImLk::getInflcAccid)
                            .like(BizImLk::getConversionId, "%" + groupConversionId);
                    List<BizImLk> bizImLks = bizImLkMapper.selectList(queryWrapper);

                    if (!CollectionUtils.isEmpty(bizImLks)) {
                        String customerNames = bizImLks.stream().map(BizImLk::getCustomerName).collect(Collectors.joining(","));
                        datum.setCustomerName(customerNames);
                    }
                }
            }
        }
    }

    private void setEvaluateInfo(List<BizImLk> data) {

        if (!CollectionUtils.isEmpty(data)) {
            //设置评价信息
            List<Long> linkIds = data.stream().map(BizImLk::getId).collect(Collectors.toList());

            QueryWrapper<BizImLkEvaluate> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(BizImLkEvaluate::getLkId, linkIds);
            List<BizImLkEvaluate> list = bizImLkEvaluateService.list(queryWrapper);
            Map<Long, List<BizImLkEvaluate>> lkEvaluateGroup = list.stream().collect(Collectors.groupingBy(BizImLkEvaluate::getLkId));

            List<BizNomalEnums> reasons = bizNomalEnumsService.getReasons("客服评价不满意");
            Map<String, String> evaluateMap = reasons.stream()
                    .collect(Collectors.toMap(BizNomalEnums::getKeyValue, BizNomalEnums::getKeyDesc));

            for (BizImLk imLk : data) {
                if (lkEvaluateGroup.containsKey(imLk.getId())) {

                    List<BizImLkEvaluate> bizImLkEvaluates = lkEvaluateGroup.get(imLk.getId());

                    if (!CollectionUtils.isEmpty(bizImLkEvaluates)) {

                        BizImLkEvaluate bizImLkEvaluate = bizImLkEvaluates.get(0);
                        if (bizImLkEvaluate.getSatisfied() != null) {
                            if (bizImLkEvaluate.getSatisfied()) {
                                imLk.setSatisfied("满意");
                            } else {
                                imLk.setSatisfied("不满意");
                                imLk.setPingjia(evaluateMap.get(String.valueOf(bizImLkEvaluate.getReason())));
                            }
                        }
                    }
                }
            }
        }
    }


    public SwitchLinkVO switchLink(SwitchLinkDTO switchLinkDTO) {

        String lkId = switchLinkDTO.getLkId();
        String targetCustomerAccId = switchLinkDTO.targetCustomerId;

        positionAccId(switchLinkDTO, lkId);

        BizImLk oldBizImLk = getImLink(switchLinkDTO.customerAccId, switchLinkDTO.influcerAccId, ImLkStatus.fuwuzhong.value);

        if (oldBizImLk != null) {

            //判断新开启的lk 是否存在，不存在再添加
            BizImLk historyBizImLk = getImLink(switchLinkDTO.targetCustomerId, switchLinkDTO.influcerAccId, null);

            //判断是否开启过云信会话
            if (historyBizImLk == null) {
                yxImService.createConversion(switchLinkDTO.targetCustomerId, ImChatType.danliao.value, oldBizImLk.getInflcAccid());
            }

            String conversionId = yxImService.conversionId(targetCustomerAccId, ImChatType.danliao.value, switchLinkDTO.getInflucerAccId());
            //记录本地新会话记录
            QueryWrapper<BizCustomerIm> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("customer_accid", targetCustomerAccId);
            BizCustomerIm bizCustomerIm = bizCustomerImMapper.selectOne(queryWrapper);

            Integer linkStatus = getLinkStatus(targetCustomerAccId);
            BizImLk newBizImLk = addBizImLk(bizCustomerIm, oldBizImLk, conversionId, linkStatus);

            //客户主动关闭
            oldBizImLk.setZixunStatus(ImLkStatus.cstmclosed.value);
            oldBizImLk.setCloseTime(new Date());
            baseMapper.updateById(oldBizImLk);

            return SwitchLinkVO.builder()
                    .lkId(conversionId)
                    .customerId(newBizImLk.getCustomerId())
                    .customerAccid(newBizImLk.getCustomerAccid())
                    .inflcId(newBizImLk.getInflcId())
                    .inflcAccid(newBizImLk.getInflcAccid())
                    .build();
        }
        return null;
    }

    private Integer getLinkStatus(String targetCustomerAccId) {

        QueryWrapper<BizCustomerIm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_accid", targetCustomerAccId);
        BizCustomerIm bizCustomerIm = bizCustomerImMapper.selectOne(queryWrapper);

        BizCustomer bizCustomer = bizCustomerMapper.selectById(bizCustomerIm.getCustomerId());

        if (StringUtils.isEmpty(bizCustomer.getRoleName())) {
            throw new RuntimeException("无效的用户角色！");
        }

        if (!StringUtils.isEmpty(bizCustomer.getRoleName()) &&
                CustomerType.BUSINESS.name.equals(bizCustomer.getRoleName())) {
            return ImLkStatus.fuwuzhong.value;
        }

        QueryWrapper<BizImLk> bizImLkQueryWrapper = new QueryWrapper<>();
        bizImLkQueryWrapper.eq("customer_accid", targetCustomerAccId)
                .eq("zixun_status", ImLkStatus.fuwuzhong.value);
        Integer count = baseMapper.selectCount(bizImLkQueryWrapper);

        if (count < bizCustomer.getJiedaiMaxnum()) {
            return ImLkStatus.fuwuzhong.value;
        } else {
            return ImLkStatus.waiting.value;
        }
    }

    private BizImLk getImLink(String customerAccId, String influcerAccId, Integer lkStatus) {

        QueryWrapper<BizImLk> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BizImLk::getCustomerAccid, customerAccId)
                .eq(BizImLk::getInflcAccid, influcerAccId)
                .eq(null != lkStatus, BizImLk::getZixunStatus, lkStatus);
        List<BizImLk> bizImLks = baseMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(bizImLks)) {
            return bizImLks.get(0);
        }

        return null;
    }

    /**
     * 定位那个是客服accid 那个是达人accid
     *
     * @param switchLinkDTO
     * @param lkId
     */
    private void positionAccId(SwitchLinkDTO switchLinkDTO, String lkId) {
        String[] split = lkId.split("\\|");

        if (split.length != 3) {
            throw new RuntimeException("无效的会话id");
        }

        String accId1 = split[0];
        String accId2 = split[2];

        QueryWrapper<BizCustomerIm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_accid", accId1);
        int count = bizCustomerImMapper.selectCount(queryWrapper);

        if (count > 0) {
            switchLinkDTO.setCustomerAccId(accId1);
            switchLinkDTO.setInflucerAccId(accId2);
        } else {
            switchLinkDTO.setCustomerAccId(accId2);
            switchLinkDTO.setInflucerAccId(accId1);
        }

    }

    public BizImLk addBizImLk(BizCustomerIm bizCustomerIm, BizImLk oldBizImLk, String conversionId, Integer linkStatus) {

        BizImLk bizImLk = new BizImLk();

        bizImLk.setInflcId(oldBizImLk.getInflcId());
        bizImLk.setInflcName(oldBizImLk.getInflcName());
        bizImLk.setInflcAccid(oldBizImLk.getInflcAccid());
        bizImLk.setInflcToken(oldBizImLk.getInflcToken());
        if (bizCustomerIm != null) {
            bizImLk.setCustomerId(Long.valueOf(bizCustomerIm.getCustomerId()));
            bizImLk.setCustomerName(bizCustomerIm.getCustomerName());
            bizImLk.setCustomerAccid(bizCustomerIm.getCustomerAccid());
            bizImLk.setCutomerToken(bizCustomerIm.getImToken());
        }
        bizImLk.setZixunStatus(linkStatus);
        bizImLk.setCreateTime(new Date());
        bizImLk.setUpdateTime(new Date());
        bizImLk.setGroupChat(ImChatType.danliao.value);
        bizImLk.setConversionId(conversionId);
        save(bizImLk);
        return bizImLk;
    }

    public List<ConversionVO> linkConversionList(String customerAccid, Integer lkStatus) {

        QueryWrapper<BizImLk> lkQueryWrapper = new QueryWrapper<>();
        List<BizImLk> bizImLks = null;
        if (ImLkStatus.fuwuzhong.value.equals(lkStatus) || ImLkStatus.waiting.value.equals(lkStatus)) {
            lkQueryWrapper.lambda()
                    .select(BizImLk::getId, BizImLk::getConversionId,
                            BizImLk::getCustomerAccid, BizImLk::getInflcAccid,
                            BizImLk::getInflcName, BizImLk::getCooperation,
                            BizImLk::getGroupChat, BizImLk::getGroupName)
                    .eq(BizImLk::getCustomerAccid, customerAccid)
                    .eq(BizImLk::getZixunStatus, lkStatus);
            bizImLks = baseMapper.selectList(lkQueryWrapper);
        }

        if (0 > lkStatus) {
            lkQueryWrapper.lambda()
                    .select(BizImLk::getId, BizImLk::getConversionId,
                            BizImLk::getCustomerAccid, BizImLk::getInflcAccid,
                            BizImLk::getInflcName, BizImLk::getCooperation,
                            BizImLk::getGroupChat, BizImLk::getGroupName)
                    .eq(BizImLk::getCustomerAccid, customerAccid)
                    .in(BizImLk::getZixunStatus, ImLkStatus.cstmclosed.value, ImLkStatus.tmotclosed.value, ImLkStatus.infclose.value);
            bizImLks = baseMapper.selectList(lkQueryWrapper);
        }

        List<String> conversionIds = new ArrayList<>();
        List<ConversionVO> conversionVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bizImLks)) {

            //解决多个历史会话情况，显示最新的历史会话
            bizImLks.sort((l1, l2) -> {
                if (l2.getId() > l1.getId()) {
                    return 1;
                } else {
                    return -1;
                }
            });

            for (BizImLk bizImLk : bizImLks) {

                if (!StringUtils.isEmpty(bizImLk.getConversionId())) {
                    if (!conversionIds.contains(bizImLk.getConversionId())) {
                        conversionIds.add(bizImLk.getConversionId());

                        ConversionVO build = ConversionVO.builder()
                                .lkId(bizImLk.getConversionId())
                                .cooperation(StringUtils.isEmpty(bizImLk.getCooperation()) ? 0 : bizImLk.getCooperation())
                                .influcerAccId(bizImLk.getInflcAccid())
                                .influcerName(bizImLk.getInflcName())
                                .build();
                        if (bizImLk.getGroupChat() == 2) {
                            String conversionId = bizImLk.getConversionId();
                            String[] split = conversionId.split("\\|");
                            build.setInflucerAccId(split[2]);
                            build.setInflucerName(bizImLk.getGroupName());
                        }

                        conversionVOS.add(build);
                    }
                    continue;
                }

                if (null == bizImLk.getGroupChat() || ImChatType.danliao.value.equals(bizImLk.getGroupChat())) {

                    if (!StringUtils.isEmpty(bizImLk.getCustomerAccid()) &&
                            !StringUtils.isEmpty(bizImLk.getInflcAccid())) {

                        String cid = yxImService.conversionId(bizImLk.getCustomerAccid(),
                                ImChatType.danliao.value, bizImLk.getInflcAccid());

                        if (!conversionIds.contains(cid)) {
                            conversionIds.add(cid);
                            conversionVOS.add(ConversionVO.builder()
                                    .lkId(cid)
                                    .cooperation(StringUtils.isEmpty(bizImLk.getCooperation()) ? 0 : bizImLk.getCooperation())
                                    .influcerAccId(bizImLk.getInflcAccid())
                                    .influcerName(bizImLk.getInflcName())
                                    .build());
                        }
                    }
                }
            }
        }


        return conversionVOS;
    }

    public Boolean cooperation(SwitchCooperationDTO dto) {

        List<BizImLk> bizImLks = getImLksByCid(dto.getBizImLkId());

        BizImLk bizImLk = bizImLks.get(0);

        if (null == bizImLk) {
            throw new RuntimeException("不存在的会话" + dto.getBizImLkId());
        }

        UpdateWrapper<BizImLk> updateWrapper = new UpdateWrapper<>();
        if (bizImLk.getCooperation() != null && bizImLk.getCooperation() == 1) {
            updateWrapper.set("cooperation", 0)
                    .eq("customer_accid", bizImLk.getCustomerAccid())
                    .eq("inflc_accid", bizImLk.getInflcAccid());
        } else {
            updateWrapper.set("cooperation", 1)
                    .eq("customer_accid", bizImLk.getCustomerAccid())
                    .eq("inflc_accid", bizImLk.getInflcAccid());
        }
        baseMapper.update(null, updateWrapper);

        return true;
    }

    private List<BizImLk> getImLksByCid(String bizImLkId) {

        SwitchLinkDTO build = SwitchLinkDTO.builder().build();
        positionAccId(build, bizImLkId);

        QueryWrapper<BizImLk> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(BizImLk::getId, BizImLk::getCooperation, BizImLk::getCustomerAccid, BizImLk::getInflcAccid)
                .eq(BizImLk::getCustomerAccid, build.getCustomerAccId())
                .eq(BizImLk::getInflcAccid, build.getInflucerAccId())
                .in(BizImLk::getZixunStatus, ImLkStatus.fuwuzhong.value, ImLkStatus.waiting.value)
                .orderByDesc(BizImLk::getId);
        List<BizImLk> bizImLks = baseMapper.selectList(queryWrapper);

        if (!CollectionUtils.isEmpty(bizImLks)) {
            return bizImLks;
        }

        return hisBizImLks(build);
    }

    private List<BizImLk> hisBizImLks(SwitchLinkDTO build) {

        QueryWrapper<BizImLk> hisQueryWrapper = new QueryWrapper<>();
        hisQueryWrapper.lambda().select(BizImLk::getId, BizImLk::getCooperation, BizImLk::getCustomerAccid, BizImLk::getInflcAccid)
                .eq(BizImLk::getCustomerAccid, build.getCustomerAccId())
                .eq(BizImLk::getInflcAccid, build.getInflucerAccId())
                .in(BizImLk::getZixunStatus, ImLkStatus.cstmclosed.value, ImLkStatus.tmotclosed.value, ImLkStatus.infclose.value)
                .orderByDesc(BizImLk::getId);

        List<BizImLk> hisBizImLks = baseMapper.selectList(hisQueryWrapper);

        if (CollectionUtils.isEmpty(hisBizImLks)) {
            return new ArrayList<>();
        }
        return hisBizImLks;
    }

    public BizImLk detail(Integer bizImLkId) {

        QueryWrapper<BizImLkEvaluate> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizImLkEvaluate::getLkId, bizImLkId);
        List<BizImLkEvaluate> list = bizImLkEvaluateService.list(queryWrapper);

        BizImLk bizImLk = getById(bizImLkId);

        if (!CollectionUtils.isEmpty(list)) {

            BizImLkEvaluate bizImLkEvaluate = list.get(0);

            if (bizImLkEvaluate.getSatisfied() != null) {
                if (bizImLkEvaluate.getSatisfied()) {
                    bizImLk.setSatisfied("满意");
                } else {
                    bizImLk.setSatisfied("不满意");
                    List<BizNomalEnums> reasons = bizNomalEnumsService.getReasons("客服评价不满意");

                    for (BizNomalEnums reason : reasons) {
                        if (reason.getKeyValue().equals(String.valueOf(bizImLkEvaluate.getReason()))) {
                            bizImLk.setPingjia(reason.getKeyDesc());
                        }
                    }
                }
            }
        }

        return bizImLk;
    }

    public void confirmBizImLk(BizCustomerIm bizCustomerIm, BizInflucer bizInflucer) {

        String fromAccId = bizCustomerIm.getCustomerAccid();
        String toAccId = bizInflucer.getAccid();

        BizImLk one = lambdaQuery().eq(BizImLk::getCustomerAccid, fromAccId)
                .eq(BizImLk::getInflcAccid, toAccId)
                .eq(BizImLk::getZixunContent, ImLkStatus.fuwuzhong.value).one();

        if (one == null) {

            BizImLk bizImLk = new BizImLk()
                    .setZixunStatus(ImLkStatus.fuwuzhong.value)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date())
                    .setConversionId(yxImService.conversionId(fromAccId, ImChatType.danliao.value, toAccId))
                    .setGroupChat(ImChatType.danliao.value);

            //达人信息
            bizImLk.setInflcId(bizInflucer.getId())
                    .setInflcName(StringUtils.isEmpty(bizInflucer.getName()) ? bizInflucer.getNikeName() : bizInflucer.getName())
                    .setInflcAccid(bizInflucer.getAccid())
                    .setInflcToken(bizInflucer.getAcctoken());
            //客服信息
            bizImLk.setCustomerId((long) bizCustomerIm.getCustomerId())
                    .setCustomerName(bizCustomerIm.getCustomerName())
                    .setCustomerAccid(bizCustomerIm.getCustomerAccid())
                    .setCutomerToken(bizCustomerIm.getImToken());

            save(bizImLk);
        }
    }
}
