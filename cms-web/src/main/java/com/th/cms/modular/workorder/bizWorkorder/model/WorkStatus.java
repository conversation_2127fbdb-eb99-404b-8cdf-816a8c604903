package com.th.cms.modular.workorder.bizWorkorder.model;

public enum WorkStatus {
    /**
     * 2: 事故维修
     */
    daichuli(1, "待处理"),
    /**
     * 3: 维修
     */
    chulizhong(2, "处理中"),
    closed(3, "已关闭"),
    ;

    public Integer value;
    public String name;

    WorkStatus(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static WorkStatus getType(Integer value) {
        WorkStatus[] carLeaveTypeList = WorkStatus.values();
        for (WorkStatus carLeaveType : carLeaveTypeList) {
            if (carLeaveType.value.equals(value)) {
                return carLeaveType;
            }
        }
        return null;
    }
}
