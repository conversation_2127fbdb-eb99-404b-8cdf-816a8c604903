package com.th.cms.modular.policy.bizAppPolicy.model;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value="BizAppPolicy对象", description="")
public class BizAppPolicy implements Serializable {


    @ApiModelProperty(value = "编号")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "政策名称")
    @TableField("policy")
    private String policy;

    @ApiModelProperty(value = "政策状态")
    @TableField("policy_status")
    private Integer policyStatus;

    @ApiModelProperty(value = "排序字段")
    @TableField("sort_num")
    private Integer sortNum;

    @ApiModelProperty(value = "添加人")
    @TableField("create_id")
    private Long createId;

    @ApiModelProperty(value = "添加人")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "部门名称")
    @TableField("dept_name")
    private String deptName;

    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private Long deptId;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private Integer companyId;

    @ApiModelProperty(value = "公司名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "删除")
    @TableField("del_flag")
    private Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ExcelField(title="编号",dictType="", align=2, sort=0)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    @ExcelField(title="政策名称",dictType="", align=2, sort=1)
    public String getPolicy() {
        return policy;
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }
    @ExcelField(title="政策状态",dictType="", align=2, sort=2)
    public Integer getPolicyStatus() {
        return policyStatus;
    }

    public void setPolicyStatus(Integer policyStatus) {
        this.policyStatus = policyStatus;
    }
    @ExcelField(title="排序字段",dictType="", align=2, sort=3)
    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }
    @ExcelField(title="添加人",dictType="", align=2, sort=4)
    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }
    @ExcelField(title="添加人",dictType="", align=2, sort=5)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    @ExcelField(title="部门名称",dictType="", align=2, sort=6)
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    @ExcelField(title="部门ID",dictType="", align=2, sort=7)
    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    @ExcelField(title="公司ID",dictType="", align=2, sort=8)
    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }
    @ExcelField(title="公司名称",dictType="", align=2, sort=9)
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    @ExcelField(title="删除",dictType="", align=2, sort=10)
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
    @ExcelField(title="创建时间",dictType="", align=2, sort=11)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    @ExcelField(title="更新时间",dictType="", align=2, sort=12)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BizAppPolicy{" +
        "id=" + id +
        ", policy=" + policy +
        ", policyStatus=" + policyStatus +
        ", sortNum=" + sortNum +
        ", createId=" + createId +
        ", createName=" + createName +
        ", deptName=" + deptName +
        ", deptId=" + deptId +
        ", companyId=" + companyId +
        ", companyName=" + companyName +
        ", delFlag=" + delFlag +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
