package com.th.cms.modular.policy.bizAppSlideshow.service;

import com.th.cms.modular.policy.bizAppSlideshow.model.BizAppSlideshow;
import com.th.cms.modular.policy.bizAppSlideshow.dao.BizAppSlideshowMapper;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.policy.bizAppSlideshow.model.enums.DELFLAG;
import com.th.cms.modular.policy.bizAppSlideshow.model.reqparam.BizAppSlideshowListParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizAppSlideshowService extends ServiceImpl<BizAppSlideshowMapper, BizAppSlideshow> implements IService<BizAppSlideshow> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizAppSlideshowListParam param) {
        QueryWrapper<BizAppSlideshow> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("del_flag", DELFLAG.weishanchu.value);
        objectQueryWrapper.orderByAsc("sort_num");
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;

    }
}
