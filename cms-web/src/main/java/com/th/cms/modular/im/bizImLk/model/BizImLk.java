package com.th.cms.modular.im.bizImLk.model;

import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.stylefeng.roses.core.excel.annotation.ExcelField;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BizImLk对象", description = "")
public class BizImLk implements Serializable {


    @ApiModelProperty(value = "序号")
    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "达人id")
    @TableField("inflc_id")
    private Long inflcId;

    @ApiModelProperty(value = "达人名称")
    @TableField("inflc_name")
    private String inflcName;

    @ApiModelProperty(value = "达人accid")
    @TableField("inflc_accid")
    private String inflcAccid;

    @ApiModelProperty(value = "达人token")
    @TableField("inflc_token")
    private String inflcToken;

    @ApiModelProperty(value = "客服id")
    @TableField("customer_id")
    private Long customerId;

    @ApiModelProperty(value = "客服名称")
    @TableField("customer_name")
    private String customerName;

    @ApiModelProperty(value = "客服accid")
    @TableField("customer_accid")
    private String customerAccid;

    @ApiModelProperty(value = "客服token")
    @TableField("cutomer_token")
    private String cutomerToken;

    @ApiModelProperty(value = "咨询状态")
    @TableField("zixun_status")
    private Integer zixunStatus;

    @ApiModelProperty(value = "咨询内容")
    @TableField("zixun_content")
    private String zixunContent;

    @ApiModelProperty(value = "是否满意")
    @TableField(exist = false)
    private String satisfied;

    @ApiModelProperty(value = "评价")
    @TableField("pingjia")
    private String pingjia;

    @ApiModelProperty(value = "得分")
    @TableField("st_starts")
    private Integer stStarts;

    @ApiModelProperty(value = "关闭时间")
    @TableField("close_time")
    private Date closeTime;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "意向合作")
    @TableField("cooperation")
    private Integer cooperation;

    @ApiModelProperty(value = "消息id")
    @TableField("message_server_id")
    private Long messageServerId;

    @ApiModelProperty(value = "评价创建时间")
    @TableField("message_time")
    private Long messageTime;

    @ApiModelProperty(value = "禁止自动回复")
    @TableField("auto_reply_disable")
    private Integer autoReplyDisable;

    @TableField("conversion_id")
    private String conversionId;

    @TableField("backup_conversion_id")//d-c
    private String backupConversionId;

    @TableField("group_chat")
    private Integer groupChat;

    @TableField("group_name")
    private String groupName;

    @TableField("del_flag")
    private Boolean delFlag;


}
