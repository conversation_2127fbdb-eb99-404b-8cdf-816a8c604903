package com.th.cms.modular.wf.wfInflucerSubmitApprove.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitApproveRequestDTO {

    //审批单id
    private Long id;

    //审批记录id
    private Long recordId;

    //审批结果
    private String eventType;

    //审批记录id  3 通过  4 拒绝 5撤销
    private Integer approveStatus;

    //拒绝原因
    private String approvalComment;

    //表单信息
    List<SubmitFormFieldDTO> formFields;

    //绑定，运营绑定跳过表单数据校验，只操作提审记录和审批记录
    private Boolean skipCheckForm;
}
