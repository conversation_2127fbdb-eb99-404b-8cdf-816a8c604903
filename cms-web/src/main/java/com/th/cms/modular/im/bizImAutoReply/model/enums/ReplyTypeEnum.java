package com.th.cms.modular.im.bizImAutoReply.model.enums;

public enum ReplyTypeEnum {

    SAY_HELLO(1, "打招呼"),
    OFFLINE(2, "离线"),
    WAITING(3, "等待中"),
    PROLOGUE(4, "开场白"),
    CONCLUSION(5, "结束语"),
    GUIDE(9, "引导语"),
    ;

    public Integer value;
    public String name;

    ReplyTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }


    public static ReplyTypeEnum getType(Integer value) {
        ReplyTypeEnum[] statusList = ReplyTypeEnum.values();
        for (ReplyTypeEnum STATUS : statusList) {
            if (STATUS.value.equals(value)) {
                return STATUS;
            }
        }
        return null;
    }
}
