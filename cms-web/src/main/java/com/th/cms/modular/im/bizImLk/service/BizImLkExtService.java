package com.th.cms.modular.im.bizImLk.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.shiro.DataAuthService;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.customer.bizCustomer.model.BizCustomer;
import com.th.cms.modular.customer.bizCustomer.service.BizCustomerService;
import com.th.cms.modular.im.bizImLk.model.BizImLk;
import com.th.cms.modular.im.bizImLk.model.ImLkStatus;
import com.th.cms.modular.im.bizImRecord.model.BizImRecord;
import com.th.cms.modular.im.bizImRecord.service.BizImRecordExtService;
import com.th.cms.modular.im.MessageSendType;
import com.th.cms.modular.im.YxImService;
import com.th.cms.modular.im.bizImAutoReply.dao.BizImAutoReplyMapper;
import com.th.cms.modular.im.bizImAutoReply.model.BizImAutoReply;
import com.th.cms.modular.im.bizImAutoReply.model.enums.ReplyTypeEnum;
import com.th.cms.modular.im.bizImAutoReply.model.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BizImLkExtService {
    @Autowired
    BizImLkService bizImLkService;
    @Autowired
    BizImAutoReplyMapper bizImAutoReplyMapper;
    @Autowired
    YxImService yxImService;
    @Autowired
    BizImRecordExtService bizImRecordExtService;

    public void save(BizImLk bizImLk) {
        setEnumsName(bizImLk);
        ShiroUtils.setAddAuthInfo(bizImLk);
        bizImLkService.save(bizImLk);
    }

    public void updateById(BizImLk bizImLk) {
        setEnumsName(bizImLk);
        DataAuthService.checkPermision(bizImLkService.getById(bizImLk.getId()), DataPermisionTypeEnum.ByComp);
        ShiroUtils.setUpdateAuthInfo(bizImLk);
        BizImLk bizImLkDb = queryById(bizImLk.getId());

        bizImLkService.updateById(bizImLk);
    }

    public BizImLk queryById(Serializable id) {
        BizImLk bizImLk = bizImLkService.getById(id);
        DataAuthService.checkPermision(bizImLk, DataPermisionTypeEnum.ByComp);

        return bizImLk;
    }

    public void removeById(Serializable id) {
        BizImLk bizImLk = bizImLkService.getById(id);
        DataAuthService.checkPermision(bizImLk, DataPermisionTypeEnum.ByComp);
        BizImLk bizImLkRecord = new BizImLk();
        bizImLkRecord.setId(bizImLk.getId());

        bizImLkService.updateById(bizImLkRecord);
    }

    private void setEnumsName(BizImLk bizImLk) {
    }

    @Autowired
    BizCustomerService bizCustomerService;

    public Long countCtNum(List<ImLkStatus> imLkStatusLst, Long userId) {
        BizCustomer bizCustomer = bizCustomerService.queryByUserId(userId + "");

        List<Integer> dlist = imLkStatusLst.stream().map(i -> i.value).collect(Collectors.toList());

        List<BizImLk> list = bizImLkService.lambdaQuery()
                .eq(BizImLk::getCustomerId, bizCustomer.getId())
                .in(BizImLk::getZixunStatus, dlist)
                .isNull(BizImLk::getDelFlag)
                .list();

        List<BizImLk> groupLink = list.stream().filter(f -> null == f.getInflcAccid()).collect(Collectors.toList());

        Map<String, List<BizImLk>> collect = list.stream()
                .filter(f -> null != f.getInflcAccid())
                .collect(Collectors.groupingBy(BizImLk::getInflcAccid));

        int size = collect.size();
        if (!CollectionUtils.isEmpty(groupLink)) {
            return (long) size + groupLink.size();
        }

        return (long) collect.size();
    }

    public void closeTimeOutLk() {
        int timeOutMs = 20;
        Date date = new Date(System.currentTimeMillis() - DateTimeConstants.MILLIS_PER_MINUTE * 20);
        //最后一条消息
        List<BizImLk> bizImLks = bizImLkService.lambdaQuery()
                .le(BizImLk::getCreateTime, date)
                .eq(BizImLk::getZixunStatus, ImLkStatus.fuwuzhong.value).list();

        for (BizImLk bizImLk : bizImLks) {
            try {
                BizImRecord bizImRecord = bizImRecordExtService.queryImOneRcdLk(bizImLk);
                if (bizImRecord != null) {
                    long mints = getMinuteDifference(bizImRecord.getCreateTime(), new Date());
                    if (mints > timeOutMs) {
                        //关闭超时单
                        closeTmLK(bizImLk);
                    }
                } else {
                    //无消息 直接关闭
                    closeTmLK(bizImLk);
                }
            } catch (Exception e) {
                log.error("关闭订单异常", e);
            }
        }
    }

    public void closeTmLK(BizImLk bizImLk) {
        BizImLk bizImLkRd = new BizImLk();
        bizImLkRd.setId(bizImLk.getId());
        bizImLkRd.setCloseTime(new Date());
        bizImLkRd.setZixunStatus(ImLkStatus.tmotclosed.value);

        //发送结束语
        closeAutoReply(bizImLk);

        //发送自定义评价
        JSONObject messageData = yxImService.sendConversion(bizImLk.getCustomerAccid(), 1, bizImLk.getInflcAccid(),
                MessageSendType.CUSTOMER.value, "text", "评价", null);
        if (messageData != null) {
            bizImLkRd.setMessageServerId(messageData.getLong("message_server_id"));
            bizImLkRd.setMessageTime(messageData.getLong("create_time"));
        }

        bizImLkService.updateById(bizImLkRd);

        log.info("关闭咨询单成功 " + bizImLk.getId() + " " + bizImLk.getCustomerName() + " " + bizImLk.getInflcName());
    }

    private void closeAutoReply(BizImLk bizImLk) {

        List<BizImAutoReply> bizImAutoReplies = autoReplyRecords(ReplyTypeEnum.CONCLUSION.value);

        if (!CollectionUtils.isEmpty(bizImAutoReplies)) {

            //发送关单回复语
            bizImAutoReplies.forEach(replyContent -> {
                yxImService.sendConversion(bizImLk.getCustomerAccid(), 1, bizImLk.getInflcAccid(),
                        MessageSendType.CUSTOMER.value, "text", replyContent.getReplyContent(), null);
            });
        }
    }


    private List<BizImAutoReply> autoReplyRecords(Integer replyType) {
        //查询服务中 打招呼语  ReplyTypeEnum
        QueryWrapper<BizImAutoReply> autoReplyQueryWrapper = new QueryWrapper<>();
        autoReplyQueryWrapper.lambda().eq(BizImAutoReply::getStatus, StatusEnum.ENABLE.value)
                .eq(BizImAutoReply::getReplyType, replyType)
                .orderByAsc(BizImAutoReply::getSortNum);

        return bizImAutoReplyMapper.selectList(autoReplyQueryWrapper);
    }

    public static long getMinuteDifference(Date startDate, Date endDate) {
        long milliseconds = endDate.getTime() - startDate.getTime();
        return milliseconds / (60 * 1000); // 1分钟=60秒*1000毫秒
    }


}
