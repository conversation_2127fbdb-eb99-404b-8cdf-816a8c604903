package com.th.cms.modular.settle.contract.controller;


import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 合同信息页面控制器
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@Controller
@RequestMapping("/settleContractPage")
public class SettleContractPageController {
    private String PREFIX = "/modular/settleContractPage/";
    /**
     * 新增合同
     */
    @RequestMapping(value = "")
    public String index() {
        return PREFIX+"list.html";
    }
    /**
     * 新增合同
     */
    @RequestMapping(value = "/add")
    public String add() {
        return PREFIX+"add.html";
    }
  /**
     * 更新合同
     */
    @RequestMapping(value = "/update")
    public String update() {
        return PREFIX+"update.html";
    }
    /**
     * 查询合同
     */
    @RequestMapping(value = "/detail")
    public String detail() {
        return PREFIX+"detail.html";
    }

}
