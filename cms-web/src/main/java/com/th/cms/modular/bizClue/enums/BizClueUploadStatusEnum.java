package com.th.cms.modular.bizClue.enums;

public enum BizClueUploadStatusEnum {

    WATING("1", "待处理"),

    APPROVED("2", "已处理"),
    REJECTED("3", "已拒绝"),
    EDIT("4", "已修改"),

    ;
    private String status;

    private String desc;

    BizClueUploadStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }
    public String getStatus() {
        return status;
    }
    public String getDesc() {
        return desc;
    }


}
