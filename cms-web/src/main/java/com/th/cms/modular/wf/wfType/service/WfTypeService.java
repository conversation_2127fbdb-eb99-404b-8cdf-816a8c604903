package com.th.cms.modular.wf.wfType.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.config.web.UserContextHolder;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.modular.enums.ApprovalBillType;
import com.th.cms.modular.enums.ApprovalStatus;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.mapper.SettleProjectsMapper;
import com.th.cms.modular.wf.bizCustomizeForm.dao.BizCustomizeFormMapper;
import com.th.cms.modular.wf.bizCustomizeForm.model.BizCustomizeForm;
import com.th.cms.modular.wf.bizCustomizeForm.service.BizCustomizeFormService;
import com.th.cms.modular.wf.bizCustomizeRecord.dao.BizCustomizeRecordMapper;
import com.th.cms.modular.wf.bizCustomizeRecord.service.BizCustomizeRecordService;
import com.th.cms.modular.wf.wfApprovalRecord.model.WfApprovalRecord;
import com.th.cms.modular.wf.wfApprovalRecord.service.WfApprovalRecordService;
import com.th.cms.modular.wf.wfApprovalRequest.model.WfApprovalRequest;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestService;
import com.th.cms.modular.wf.wfApprovalRequestView.service.WfApprovalRequestViewService;
import com.th.cms.modular.wf.wfStep.model.*;
import com.th.cms.modular.wf.wfStep.service.WfStepConfigService;
import com.th.cms.modular.wf.wfStep.service.WfStepService;
import com.th.cms.modular.wf.wfType.dao.WfTypeMapper;
import com.th.cms.modular.wf.wfType.model.*;
import com.th.cms.modular.wf.wfType.model.reqparam.WfTypeListParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class WfTypeService extends ServiceImpl<WfTypeMapper, WfType> implements IService<WfType> {


    @Resource
    private WfStepService wfStepService;//审批节点

    @Resource
    private BizCustomizeFormService bizCustomizeFormService;//审批节点表单
    @Resource
    private BizCustomizeFormMapper bizCustomizeFormMapper;

    @Resource
    private BizCustomizeRecordService bizCustomizeRecordService;
    @Resource
    private BizCustomizeRecordMapper bizCustomizeRecordMapper;

    @Resource
    private SettleProjectsMapper settleProjectsMapper;
    @Resource
    private WfStepConfigService wfStepConfigService;
    @Resource
    private WfApprovalRequestService wfApprovalRequestService;
    @Resource
    private WfApprovalRecordService wfApprovalRecordService;
    @Autowired
    private WfApprovalRequestViewService wfApprovalRequestViewService;
    @Resource
    private WfTypeMapper wfTypeMapper;


    public List<WfType> flowList() {
        QueryWrapper<WfType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WfType::getClassifyType, ApprovalBillType.SubmitApprove.getClassifyName())
                .eq(WfType::getStatus, ApprovalStatus.APPROVED.getCode());
        return list(queryWrapper);
    }

    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(WfTypeListParam param) {
        QueryWrapper<WfType> objectQueryWrapper = new QueryWrapper<>();

        objectQueryWrapper.lambda()
                .eq(!StringUtils.isEmpty(param.getClassifyType()), WfType::getClassifyType, param.getClassifyType())
                .eq(!StringUtils.isEmpty(param.getType()), WfType::getType, param.getType())
                .ne(!StringUtils.isEmpty(param.getUnEqualType()), WfType::getType, param.getUnEqualType())
                .like(!StringUtils.isEmpty(param.getTypeName()), WfType::getTypeName, "%" + param.getTypeName() + "%")
                .like(!StringUtils.isEmpty(param.getUserName()), WfType::getCreateName, "%" + param.getUserName() + "%")
                .like(!StringUtils.isEmpty(param.getTypeNo()), WfType::getTypeNo, "%" + param.getTypeNo() + "%")
                .orderByDesc(WfType::getUpdateTime);

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;
    }


    //创建审批流
    @Transactional(rollbackFor = Exception.class)
    public Long addApprove(WfTypeDTO dto) {

        //保存主表
        WfType wfType = new WfType();
        wfType.setTypeName(dto.getTypeName());
        wfType.setDescription(dto.getDescription());

        if (UserContextHolder.getUserId() != null) {
            wfType.setCreateId(UserContextHolder.getUserId());
            wfType.setCreateName(UserContextHolder.getUserName());
        } else {
            wfType.setCreateId(1L);
            wfType.setCreateName("ABC");
        }
        wfType.setType("提审审核");
        wfType.setStatus(0);//0待审核 1审核通过  -1 不通过 2,3,4xxx
        wfType.setCreateTime(new Date());
        wfType.setIsActive(1);
        save(wfType);

        //保存节点列表
        List<WfTypeStepDTO> stepDtoList = dto.getStepList();
        if (!CollectionUtils.isEmpty(stepDtoList)) {

            List<BizCustomizeForm> formList = new ArrayList<>();
            stepDtoList.forEach(step -> {

                //构建审批流步骤节点对象
                WfStep build = WfStep.builder()
                        .typeId(wfType.getId())
                        .typeName(wfType.getTypeName())
                        .stepName(step.getStepName())
                        .shortName(step.getShortName())
                        .stepOrder(step.getStepOrder())
                        .stepType(step.getStepType())// 开始，中间，结束
                        .approveType(step.getApproveType())//或签，会签，依次审批
                        .assignType(step.getAssignType())//
                        .assignId(String.join(",", step.getAssignNameList()))
                        .assignNames(String.join(",", step.getAssignIds()))
                        .assignPermission(step.getAssignPermission())
                        .copyId(String.join(",", step.getCopyIds()))
                        .copyNames(String.join(",", step.getCopyNameList()))
                        .build();
                wfStepService.save(build);

                List<StepForm> formValues = step.getFormValues();

                if (!CollectionUtils.isEmpty(formValues)) {

                    //保存步骤审核表单-配置信息
                    JSONObject formConfig = step.getFormConfig();
                    if (formConfig != null && !formConfig.isEmpty()) {
                        wfStepConfigService.save(
                                new WfStepConfig().setStepId(build.getId()).setIsEdit(StepEditType.APPRO.getCode())
                                        .setStepConfig(formConfig.toJSONString())
                        );
                    }

                    //保存步骤编辑表单-配置信息
                    JSONObject editFormConfig = step.getEditFormConfig();
                    if (editFormConfig != null && !editFormConfig.isEmpty()) {
                        wfStepConfigService.save(
                                new WfStepConfig().setStepId(build.getId()).setIsEdit(StepEditType.EDIT.getCode())
                                        .setStepConfig(editFormConfig.toJSONString())
                        );
                    }

                    List<StepForm> editFormValues = step.getEditFormValues();

                    formValues.forEach(form -> {

                        //设置编辑字段标记位
                        if (!CollectionUtils.isEmpty(editFormValues)) {
                            for (StepForm editFormValue : editFormValues) {
                                if (editFormValue.getType().equals(form.getType()) && editFormValue.getName().equals(form.getName())) {
                                    form.setIsEdit(StepEditType.ALL.getCode());
                                    form.setSkip(true);
                                }
                            }
                        }

                        //审核字段
                        formList.add(BizCustomizeForm.builder()
                                .stepId(build.getId())
                                .level(step.getStepOrder())
                                .code(form.getCode())
                                .name(form.getName())
                                .type(form.getType())
                                .createTime(new Date())
                                .isEdit(null != form.getIsEdit() ? form.getIsEdit() : StepEditType.APPRO.getCode())
                                .build());

                        //编辑字段逻辑
                        for (StepForm editFormValue : editFormValues) {
                            if (editFormValue.getSkip() == null) {
                                formList.add(BizCustomizeForm.builder()
                                        .stepId(build.getId())
                                        .level(step.getStepOrder())
                                        .code(editFormValue.getCode())
                                        .name(editFormValue.getName())
                                        .type(editFormValue.getType())
                                        .createTime(new Date())
                                        .isEdit(StepEditType.EDIT.getCode())
                                        .build());
                            }
                        }
                    });
                }
            });

            if (!CollectionUtils.isEmpty(formList)) {
                bizCustomizeFormService.saveBatch(formList);
            }
        }

        return wfType.getId();
    }

    public WfType getApproveDetail(Long id, String requestId) {

        //审批流记录
        WfType wfType = getById(id);

        //审批流节点
        QueryWrapper<WfStep> stepQueryWrapper = new QueryWrapper<>();
        stepQueryWrapper.lambda().eq(WfStep::getTypeId, id).orderByAsc(WfStep::getStepOrder);

        List<WfStep> stepList = wfStepService.list(stepQueryWrapper);

        if (!CollectionUtils.isEmpty(stepList)) {

            List<Long> stepIds = stepList.stream().map(WfStep::getId).collect(Collectors.toList());

            QueryWrapper<BizCustomizeForm> formQueryWrapper = new QueryWrapper<>();
            formQueryWrapper.lambda().in(BizCustomizeForm::getStepId, stepIds);
            List<BizCustomizeForm> formList = bizCustomizeFormService.list(formQueryWrapper);
            Map<Long, List<BizCustomizeForm>> formGroup = formList.stream().collect(Collectors.groupingBy(BizCustomizeForm::getStepId));

            stepList.forEach(step -> {

                step.setAssignIds(StringUtils.isEmpty(step.getAssignId()) ? null : Arrays.asList(step.getAssignId().split(",")));
                step.setAssignNameList(StringUtils.isEmpty(step.getAssignNames()) ? null : Arrays.asList(step.getAssignNames().split(",")));
                step.setCopyIds(StringUtils.isEmpty(step.getCopyId()) ? null : Arrays.asList(step.getCopyId().split(",")));
                step.setCopyNameList(StringUtils.isEmpty(step.getCopyNames()) ? null : Arrays.asList(step.getCopyNames().split(",")));


                List<BizCustomizeForm> allForms = formGroup.get(step.getId());

                if (!CollectionUtils.isEmpty(allForms)) {

                    QueryWrapper<WfStepConfig> stepConfigQueryWrapper = new QueryWrapper<>();
                    stepConfigQueryWrapper.lambda().eq(WfStepConfig::getStepId, step.getId());
                    List<WfStepConfig> list = wfStepConfigService.list(stepConfigQueryWrapper);

                    //0是公共数据
                    //小于对于0，审核数据
                    List<BizCustomizeForm> appFormList = allForms.stream()
                            .filter(f -> f.getIsEdit() <= StepEditType.ALL.getCode())
                            .collect(Collectors.toList());
                    step.setFormValues(appFormList);

                    //审批配置
                    Optional<WfStepConfig> approConfig = list.stream()
                            .filter(f -> Objects.equals(f.getIsEdit(), StepEditType.APPRO.getCode()))
                            .findFirst();
                    approConfig.ifPresent(wfStepConfig -> step.setFormConfig(JSONObject.parseObject(wfStepConfig.getStepConfig())));

                    //大于对于0，编辑数据
                    List<BizCustomizeForm> editFormList = allForms.stream()
                            .filter(f -> f.getIsEdit() >= StepEditType.ALL.getCode())
                            .collect(Collectors.toList());
                    step.setEditFormValues(editFormList);

                    //编辑配置
                    Optional<WfStepConfig> editConfig = list.stream()
                            .filter(f -> Objects.equals(f.getIsEdit(), StepEditType.EDIT.getCode()))
                            .findFirst();
                    editConfig.ifPresent(wfStepConfig -> step.setEditFormConfig(JSONObject.parseObject(wfStepConfig.getStepConfig())));
                }
            });
        }

        //审批流表单
        wfType.setStepList(stepList);

        //****注意****
        //上面是待审批的审批流
        //下面是审批的审批流
        buildApproveRecordDetail(requestId, wfType);

        return wfType;
    }

    public void buildApproveRecordDetail(String requestId, WfType wfType) {

        // requestId
        wfType.setOperateFlag(AssignPermissonType.Nothing.getCode());

        //审批流本身审批流程的 审批信息
        if (!StringUtils.isEmpty(requestId)) {

            //查询审批记录
            QueryWrapper<WfApprovalRecord> recordQueryWrapper = new QueryWrapper<>();
            recordQueryWrapper.lambda()
                    .eq(WfApprovalRecord::getRequestId, requestId)
                    .eq(WfApprovalRecord::getHistory, 0);
            List<WfApprovalRecord> approvalRecords = wfApprovalRecordService.list(recordQueryWrapper);

            if (!CollectionUtils.isEmpty(approvalRecords)) {

                //审批节点信息
                List<WfStepVO> approveRecordList = new ArrayList<>();
                //待审批人列表
                List<WfApprovalRecord> reviewList = new ArrayList<>();

                Map<Integer, List<WfApprovalRecord>> stepGroup = approvalRecords.stream()
                        .collect(Collectors.groupingBy(WfApprovalRecord::getStepOrder));

                List<Integer> approveSortList = new ArrayList<>(stepGroup.keySet());
                approveSortList.sort(Comparator.comparingInt(o -> o));

                //待审批节点----旨在判断，当前人是否有审批操作
                WfStep approvalingStep = null;
                WfStep rejectedStep = null;//拒绝节点
                WfStep preStep = null;//上一个节点
                for (Integer approveSort : approveSortList) {

                    List<WfApprovalRecord> approvalRecordList = stepGroup.get(approveSort);
                    WfStep step = wfStepService.getById(approvalRecordList.get(0).getStepId());
                    step.setPreStep(preStep);

                    List<ApproveVO> approveList = new ArrayList<>();
                    //获取当前节点 审批状态
                    Integer approveStatus = buildApproveList(reviewList, approveList, step, approvalRecordList);

                    //待审批时，
                    if (ApprovalStatus.UNDER_REVIEW.getCode().equals(approveStatus) && approvalingStep == null) {
                        approvalingStep = step;
                    }
                    if (ApprovalStatus.REJECTED.getCode().equals(approveStatus) && rejectedStep == null) {
                        wfType.setStatus(ApprovalStatus.REJECTED.getCode());
                        rejectedStep = step;
                    }

                    approveRecordList.add(WfStepVO.builder()
                            .typeId(step.getTypeId())
                            .typeName(step.getTypeName())
                            .stepId(step.getId())
                            .stepName(step.getStepName())
                            .stepOrder(step.getStepOrder())
                            .status(approveStatus)
                            .approveList(approveList)
                            .build());
                    preStep = step;
                }
                //TODO 添加最后一个节点显示逻辑
                if (!CollectionUtils.isEmpty(approveRecordList)) {

                    WfStepVO lastStep = approveRecordList.get(approveRecordList.size() - 1);


                    QueryWrapper<WfStep> endStepQueryWrapper = new QueryWrapper<>();
                    endStepQueryWrapper.lambda()
                            .eq(WfStep::getTypeId, lastStep.getTypeId())
                            .eq(WfStep::getStepType, StepNodeType.EndNode.getCode());
                    List<WfStep> endSteps = wfStepService.list(endStepQueryWrapper);

                    if (!CollectionUtils.isEmpty(endSteps)) {
                        WfStep wfStep = endSteps.get(0);
                        approveRecordList.add(WfStepVO.builder()
                                .typeId(wfStep.getTypeId())
                                .typeName(wfStep.getTypeName())
                                .stepId(wfStep.getId())
                                .stepName(wfStep.getStepName())
                                .stepOrder(wfStep.getStepOrder())
                                .approveType(wfStep.getApproveType())
                                .status(lastStep.getStatus().equals(ApprovalStatus.APPROVED.getCode()) ?
                                        ApprovalStatus.UNDER_REVIEW.getCode() : ApprovalStatus.DRAFT.getCode())
                                .build());
                    }

                }

                Long userId = UserContextHolder.getUserId();
                if (null != userId) {

                    if (null != approvalingStep) {

                        //待审批 显示逻辑
                        //TODO 只考虑人员审批，未考虑部门审批，角色审批
                        Optional<WfApprovalRecord> myRecord = reviewList.stream()
                                .filter(f -> userId.equals(f.getApproverId()) &&
                                        f.getApprovalStatus().equals(ApprovalStatus.UNDER_REVIEW.getCode())
                                )
                                .findFirst();

                        if (myRecord.isPresent()) {
                            WfApprovalRecord wfApprovalRecord = myRecord.get();
                            wfType.setOperateFlag(AssignPermissonType.AgreeApprove.getCode());
                            wfType.setRecordId(wfApprovalRecord.getId());
                        } else {
                            //撤销显示逻辑-优先显示待审批情况
                            List<WfApprovalRecord> approvaledRecords = revokeApprove(approvalingStep, stepGroup, approveSortList);

                            if (!CollectionUtils.isEmpty(approvaledRecords)) {
                                Optional<WfApprovalRecord> myApproveledRecord = approvaledRecords.stream()
                                        .filter(f -> f.getApproverId().equals(userId))
                                        .findFirst();

                                if (myApproveledRecord.isPresent()) {
                                    wfType.setRecordId(myApproveledRecord.get().getId());
                                    wfType.setOperateFlag(AssignPermissonType.RevokeApprove.getCode());
                                }
                            }
                        }
                    } else {
                        //TODO 审批结束
                        //撤销显示逻辑

                        //撤销显示逻辑
                        List<WfApprovalRecord> approvaledRecords = revokeApprove(null, stepGroup, approveSortList);

                        if (!CollectionUtils.isEmpty(approvaledRecords)) {
                            Optional<WfApprovalRecord> myApproveledRecord = approvaledRecords.stream()
                                    .filter(f -> f.getApproverId().equals(userId))
                                    .min((o1, o2) -> (int) (o2.getId() - o1.getId()));

                            if (myApproveledRecord.isPresent()) {
                                wfType.setRecordId(myApproveledRecord.get().getId());
                                wfType.setOperateFlag(AssignPermissonType.RevokeApprove.getCode());
                            }
                        }
                    }

                    //重新提交显示逻辑
                    if (ApprovalStatus.REJECTED.getCode().equals(wfType.getStatus()) &&
                            wfType.getCreateId().equals(userId)) {
                        if (null != wfType.getId()) {
                            wfType.setOperateFlag(AssignPermissonType.ReSubmit.getCode());
                        } else {
                            //rejectedStep;
                            WfStep rejectPreStep = rejectedStep.getPreStep();
                            if (null != rejectPreStep) {
                                if (rejectPreStep.getStepType().equals(String.valueOf(StepNodeType.StartNode.getCode()))) {
                                    wfType.setOperateFlag(AssignPermissonType.ReSubmit.getCode());
                                }
                            }
                        }
                    }
                }

                wfType.setApproveRecordList(approveRecordList);
            }
        }
    }

    private List<WfApprovalRecord> revokeApprove(WfStep approvalingStep,
                                                 Map<Integer, List<WfApprovalRecord>> stepGroup,
                                                 List<Integer> approveSortList) {

        //审批结束
        if (null == approvalingStep) {
            return stepGroup.get(approveSortList.get(approveSortList.size() - 1));
        } else {
            //审批未结束
            Integer stepOrder = approvalingStep.getStepOrder();
            if (stepGroup.containsKey(stepOrder)) {

                //审批类型
                //正在审批的节点，审批记录
                //节点为或签，返回上一个节点审批记录
                //节点为会签，返回本节点审批记录，若均未审批，返回上一个节点审批记录
                //节点为依次审批，返回本节点审批记录，若均未审批，返回上一个节点审批记录
                ApproveType approveTypeEnum = ApproveType.fromCode(approvalingStep.getApproveType());

                switch (Objects.requireNonNull(approveTypeEnum)) {

                    case OrApprove:
                        return perApprovaledRecord(stepOrder, stepGroup, approveSortList);
                    case AndApprove:
                    case SortApprove:
                        List<WfApprovalRecord> approvalRecords = stepGroup.get(stepOrder);
                        List<WfApprovalRecord> approvaledRecords = approvalRecords.stream()
                                .filter(f -> ApprovalStatus.APPROVED.getCode().equals(f.getApprovalStatus()) ||
                                        ApprovalStatus.REJECTED.getCode().equals(f.getApprovalStatus())).collect(Collectors.toList());

                        if (!CollectionUtils.isEmpty(approvaledRecords)) {
                            return approvaledRecords;
                        } else {
                            return perApprovaledRecord(stepOrder, stepGroup, approveSortList);
                        }
                }
            }
        }

        return null;
    }

    private List<WfApprovalRecord> perApprovaledRecord(Integer stepOrder, Map<Integer, List<WfApprovalRecord>> stepGroup,
                                                       List<Integer> approveSortList) {

        int preStepOrder = getPreStepOrder(stepOrder, approveSortList);
        if (preStepOrder != 0) {
            return stepGroup.get(preStepOrder);
        }

        return null;
    }

    private int getPreStepOrder(Integer stepOrder, List<Integer> approveSortList) {

        for (int i = 0; i < approveSortList.size(); i++) {
            if (stepOrder.equals(approveSortList.get(i))) {
                if (0 != i) {
                    return approveSortList.get(i - 1);
                } else {
                    return i;
                }
            }
        }

        return 0;
    }

    private Integer buildApproveList(List<WfApprovalRecord> reviewList,
                                     List<ApproveVO> approveList, WfStep step,
                                     List<WfApprovalRecord> approvalRecordList) {

        if (!CollectionUtils.isEmpty(approvalRecordList)) {

            //根据审批记录的审批状态，推导审批节点的审批状态
            Integer approveStatus = ApprovalStatus.DRAFT.getCode();

            //判断节点类型 开始-审批-结束
            String stepType = step.getStepType();
            if (isNumeric(stepType)) {

                StepNodeType stepNodeType = StepNodeType.fromCode(Integer.parseInt(stepType));

                switch (Objects.requireNonNull(stepNodeType)) {
                    //开始节点为发起提审等等数据提提交操作
                    case StartNode:
                        approveStatus = buildStartStatus(reviewList, approveList, step, approvalRecordList);
                        break;
                    case ProcessNode:
                        //审批类型
                        ApproveType approveTypeEnum = ApproveType.fromCode(step.getApproveType());

                        switch (Objects.requireNonNull(approveTypeEnum)) {

                            case OrApprove:
                                approveStatus = getOrApproveStatus(reviewList, approveList, step, approvalRecordList);
                                break;

                            case AndApprove:
                                //有一个不同意，节点为不同意
                                approveStatus = getAndApproveStatus(reviewList, approveList, step, approvalRecordList);
                                break;
                            case SortApprove:
                                //有一个不同意，节点为不同意
                                approveStatus = getSortApproveStatus(reviewList, approveList, step, approvalRecordList);
                                break;
                        }
                        break;
                }
            }

            return approveStatus;
        }

        return ApprovalStatus.DRAFT.getCode();
    }

    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 匹配整数或小数（包括负数）
        return str.matches("-?\\d+(\\.\\d+)?");
    }

    private Integer buildStartStatus(List<WfApprovalRecord> reviewList, List<ApproveVO> approveList,
                                     WfStep step, List<WfApprovalRecord> approvalRecordList) {

        for (WfApprovalRecord approveRecord : approvalRecordList) {

            StringBuilder builder = new StringBuilder();
            builder.append(approveRecord.getApproverName())//who
                    .append(step.getShortName())//操作名称
                    .append("了")
                    .append(step.getTypeName());//审批流名称

            approveList.add(ApproveVO.builder()
                    .approveId(approveRecord.getApproverId())
                    .approveName(approveRecord.getApproverName())
                    .approveDesc(builder.toString())
                    .approvalTime(approveRecord.getUpdateTime())
                    .build());

            reviewList.add(approveRecord);
        }

        return ApprovalStatus.APPROVED.getCode();
    }

    private Integer getOrApproveStatus(List<WfApprovalRecord> reviewList, List<ApproveVO> approveList,
                                       WfStep step, List<WfApprovalRecord> approvalRecordList) {

        //有一个同意，节点为同意
        List<WfApprovalRecord> agreeRecords = approvalRecordList.stream()
                .filter(f -> f.getApprovalStatus().equals(ApprovalStatus.APPROVED.getCode()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(agreeRecords)) {
            for (WfApprovalRecord agreeRecord : agreeRecords) {
                approveList.add(buildApproveVO(agreeRecord, step, ApprovalStatus.APPROVED.getCode()));
            }

            return ApprovalStatus.APPROVED.getCode();
        }

        //驳回
        List<WfApprovalRecord> rejectRecords = approvalRecordList.stream()
                .filter(f -> f.getApprovalStatus().equals(ApprovalStatus.REJECTED.getCode()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(rejectRecords)) {

            for (WfApprovalRecord rejectRecord : rejectRecords) {
                approveList.add(buildApproveVO(rejectRecord, step, ApprovalStatus.REJECTED.getCode()));
            }

            return ApprovalStatus.REJECTED.getCode();
        }

        //待审批
        List<WfApprovalRecord> reviewRecords = approvalRecordList.stream()
                .filter(f -> f.getApprovalStatus().equals(ApprovalStatus.UNDER_REVIEW.getCode()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(reviewRecords)) {

            for (WfApprovalRecord reviewRecord : reviewRecords) {
                reviewList.add(reviewRecord);
            }

            return ApprovalStatus.UNDER_REVIEW.getCode();
        }

        //待审批
        List<WfApprovalRecord> confirmRecords = approvalRecordList.stream()
                .filter(f -> f.getApprovalStatus().equals(ApprovalStatus.PENDING_SUBMIT.getCode()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(confirmRecords)) {

            for (WfApprovalRecord reviewRecord : confirmRecords) {
                reviewList.add(reviewRecord);
            }

            return ApprovalStatus.PENDING_SUBMIT.getCode();
        }

        //未开始
        return ApprovalStatus.DRAFT.getCode();
    }

    /**
     * 有一拒绝 为拒绝
     * 所有通过为通过
     * 存在 通过，待审批
     * 不存在通过 未开始
     *
     * @param approveList
     * @param step
     * @param approvalRecordList
     * @return
     */
    private Integer getAndApproveStatus(List<WfApprovalRecord> reviewList, List<ApproveVO> approveList,
                                        WfStep step, List<WfApprovalRecord> approvalRecordList) {

        List<WfApprovalRecord> approvedRecords = approvalRecordList.stream()
                .filter(f -> f.getApprovalStatus().equals(ApprovalStatus.REJECTED.getCode()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(approvedRecords)) {

            for (WfApprovalRecord approveRecord : approvalRecordList) {

                //历史审批通过
                if (approveRecord.getApprovalStatus().equals(ApprovalStatus.APPROVED.getCode())) {
                    approveList.add(buildApproveVO(approveRecord, step, ApprovalStatus.APPROVED.getCode()));
                }

                //审批不通过
                if (approveRecord.getApprovalStatus().equals(ApprovalStatus.REJECTED.getCode())) {
                    approveList.add(buildApproveVO(approveRecord, step, ApprovalStatus.REJECTED.getCode()));
                }
            }

            return ApprovalStatus.REJECTED.getCode();
        }

        List<WfApprovalRecord> agreeRecords = approvalRecordList.stream()
                .filter(f -> f.getApprovalStatus().equals(ApprovalStatus.APPROVED.getCode()))
                .collect(Collectors.toList());

        //存在审批通过的情况
        if (!CollectionUtils.isEmpty(agreeRecords)) {

            for (WfApprovalRecord agreeRecord : agreeRecords) {
                approveList.add(buildApproveVO(agreeRecord, step, ApprovalStatus.APPROVED.getCode()));
            }
            if (agreeRecords.size() == approvalRecordList.size()) {
                return ApprovalStatus.APPROVED.getCode();
            } else {

                for (WfApprovalRecord wfApprovalRecord : approvalRecordList) {
                    if (wfApprovalRecord.getApprovalStatus().equals(ApprovalStatus.UNDER_REVIEW.getCode())) {
                        reviewList.add(wfApprovalRecord);
                    }
                }
                return ApprovalStatus.UNDER_REVIEW.getCode();
            }
        } else {
            //未开始/审批中
            WfApprovalRecord wfApprovalRecord = approvalRecordList.get(0);

            for (WfApprovalRecord war : approvalRecordList) {
                if (war.getApprovalStatus().equals(ApprovalStatus.UNDER_REVIEW.getCode())) {
                    reviewList.add(war);
                }
            }
            return wfApprovalRecord.getApprovalStatus();
        }
    }


    private Integer getSortApproveStatus(List<WfApprovalRecord> reviewList, List<ApproveVO> approveList,
                                         WfStep step, List<WfApprovalRecord> approvalRecordList) {

        List<WfApprovalRecord> approvedRecords = approvalRecordList.stream()
                .filter(f -> f.getApprovalStatus().equals(ApprovalStatus.REJECTED.getCode()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(approvedRecords)) {

            for (WfApprovalRecord approveRecord : approvalRecordList) {

                //历史审批通过
                if (approveRecord.getApprovalStatus().equals(ApprovalStatus.APPROVED.getCode())) {
                    approveList.add(buildApproveVO(approveRecord, step, ApprovalStatus.APPROVED.getCode()));
                }

                //审批不通过
                if (approveRecord.getApprovalStatus().equals(ApprovalStatus.REJECTED.getCode())) {
                    approveList.add(buildApproveVO(approveRecord, step, ApprovalStatus.REJECTED.getCode()));
                }
            }

            return ApprovalStatus.REJECTED.getCode();
        }

        List<WfApprovalRecord> agreeRecords = approvalRecordList.stream()
                .filter(f -> f.getApprovalStatus().equals(ApprovalStatus.APPROVED.getCode()))
                .collect(Collectors.toList());

        //存在审批通过的情况
        if (!CollectionUtils.isEmpty(agreeRecords)) {

            for (WfApprovalRecord agreeRecord : agreeRecords) {
                approveList.add(buildApproveVO(agreeRecord, step, ApprovalStatus.APPROVED.getCode()));
            }
            if (agreeRecords.size() == approvalRecordList.size()) {
                return ApprovalStatus.APPROVED.getCode();
            } else {

                for (WfApprovalRecord wfApprovalRecord : approvalRecordList) {
                    if (wfApprovalRecord.getApprovalStatus().equals(ApprovalStatus.UNDER_REVIEW.getCode())) {
                        reviewList.add(wfApprovalRecord);
                        return ApprovalStatus.UNDER_REVIEW.getCode();
                    }
                }
                return ApprovalStatus.UNDER_REVIEW.getCode();
            }
        } else {
            //未开始/审批中
            WfApprovalRecord wfApprovalRecord = approvalRecordList.get(0);

            for (WfApprovalRecord war : approvalRecordList) {
                if (war.getApprovalStatus().equals(ApprovalStatus.UNDER_REVIEW.getCode())) {
                    reviewList.add(war);
                    return ApprovalStatus.UNDER_REVIEW.getCode();
                }
            }
            return wfApprovalRecord.getApprovalStatus();
        }
    }


    /**
     * @param approveRecord 审批记录
     * @param step          审批节点
     * @param approveStatus 审批状态
     * @return
     */
    private ApproveVO buildApproveVO(WfApprovalRecord approveRecord, WfStep step, Integer approveStatus) {

        String approveDesc;

        if (step.getPreStep() != null &&
                String.valueOf(StepNodeType.StartNode.getCode()).equals(step.getPreStep().getStepType())) {
            if (ApprovalStatus.APPROVED.getCode().equals(approveStatus)) {
                approveDesc = approveRecord.getApproverName() + "完成了" + step.getStepName() + "操作";
            } else {
                approveDesc = approveRecord.getApproverName() + "驳回了" + step.getStepName() + "操作";
            }
        }else {
            if (ApprovalStatus.APPROVED.getCode().equals(approveStatus)) {
                approveDesc = approveRecord.getApproverName() + "审批通过了" + step.getStepName() + "审批";
            } else {
                approveDesc = approveRecord.getApproverName() + "驳回了" + step.getStepName() + "审批";
            }
        }

        return ApproveVO.builder()
                .approveId(approveRecord.getApproverId())
                .approveName(approveRecord.getApproverName())
                .approveDesc(approveDesc)
                .approvalTime(approveRecord.getUpdateTime())
                .approvalComment(approveRecord.getApprovalComment())
                .build();
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean editApprove(WfType wfType) {

        //步骤表
        List<WfStep> stepList = wfType.getStepList();

        if (!CollectionUtils.isEmpty(stepList)) {
            stepList.forEach(step -> {

                if (step.getId() == null) {
                    wfStepService.save(step);
                }

                if (!CollectionUtils.isEmpty(step.getAssignIds())) {
                    step.setAssignId(String.join(",", step.getAssignIds()));
                }
                if (!CollectionUtils.isEmpty(step.getCopyIds())) {
                    step.setCopyId(String.join(",", step.getCopyIds()));
                }

                //审核form
                List<BizCustomizeForm> formValues = step.getFormValues();
                if (!CollectionUtils.isEmpty(formValues)) {

                    //删除历史表单-全量新增
                    QueryWrapper<BizCustomizeForm> formQueryWrapper = new QueryWrapper<>();
                    formQueryWrapper.lambda().eq(BizCustomizeForm::getStepId, step.getId());
                    bizCustomizeFormService.remove(formQueryWrapper);

                    //删除步骤配置-全量新增
                    QueryWrapper<WfStepConfig> formConfigQueryWrapper = new QueryWrapper<>();
                    formConfigQueryWrapper.lambda().eq(WfStepConfig::getStepId, step.getId());
                    wfStepConfigService.remove(formConfigQueryWrapper);

                    //保存步骤表单-配置信息
                    JSONObject formConfig = step.getFormConfig();
                    if (formConfig != null && !formConfig.isEmpty()) {
                        wfStepConfigService.save(
                                new WfStepConfig().setStepId(step.getId()).setIsEdit(StepEditType.APPRO.getCode())
                                        .setStepConfig(formConfig.toJSONString())
                        );
                    }

                    JSONObject editFormConfig = step.getEditFormConfig();
                    if (editFormConfig != null && !editFormConfig.isEmpty()) {
                        wfStepConfigService.save(
                                new WfStepConfig().setStepId(step.getId()).setIsEdit(StepEditType.EDIT.getCode())
                                        .setStepConfig(editFormConfig.toJSONString())
                        );
                    }

                    List<BizCustomizeForm> customizeFormList = new ArrayList<>();
                    for (BizCustomizeForm form : formValues) {

                        //编辑form
                        List<BizCustomizeForm> editFormValues = step.getEditFormValues();

                        //设置编辑字段标记位
                        if (!CollectionUtils.isEmpty(editFormValues)) {
                            for (BizCustomizeForm editFormValue : editFormValues) {
                                if (editFormValue.getType().equals(form.getType()) && editFormValue.getName().equals(form.getName())) {
                                    form.setIsEdit(StepEditType.ALL.getCode());
                                    form.setSkip(true);
                                }
                            }
                        }

                        //审核字段
                        customizeFormList.add(BizCustomizeForm.builder()
                                .stepId(step.getId())
                                .level(step.getStepOrder())
                                .name(form.getName())
                                .type(form.getType())
                                .createTime(new Date())
                                .isEdit(null != form.getIsEdit() ? form.getIsEdit() : StepEditType.APPRO.getCode())
                                .build());

                        //编辑字段逻辑
                        for (BizCustomizeForm editFormValue : editFormValues) {
                            if (editFormValue.getSkip() == null) {
                                customizeFormList.add(BizCustomizeForm.builder()
                                        .stepId(step.getId())
                                        .level(step.getStepOrder())
                                        .name(editFormValue.getName())
                                        .type(editFormValue.getType())
                                        .createTime(new Date())
                                        .isEdit(StepEditType.EDIT.getCode())
                                        .build());
                            }
                        }
                    }

                    if (!CollectionUtils.isEmpty(customizeFormList)) {
                        bizCustomizeFormService.saveBatch(customizeFormList);
                    }
                }

                wfStepService.updateById(step);
            });
        }

        //更新主表
        return updateById(wfType);
    }


    /**
     * @param id
     * @return null/"" 表示删除成功
     */
    @Transactional
    public String delApprove(Long id) {

        //判断是否有项目正在引用
        QueryWrapper<SettleProjects> proQueryWrapper = new QueryWrapper<>();
        proQueryWrapper.lambda().eq(SettleProjects::getProjectTypeId, id);
        List<SettleProjects> projects = settleProjectsMapper.selectList(proQueryWrapper);

        if (!CollectionUtils.isEmpty(projects)) {
            return "当前审批流已绑定项目，请勿删除！";
        }

        //删除步骤，删除审批单，删除审批记录，删除审批单可见表

        //查询流程步骤
        QueryWrapper<WfStep> stepQueryWrapper = new QueryWrapper<>();
        stepQueryWrapper.lambda().select(WfStep::getId).eq(WfStep::getTypeId, id);
        List<WfStep> stepList = wfStepService.list(stepQueryWrapper);

        if (!CollectionUtils.isEmpty(stepList)) {

            List<Long> stepIds = stepList.stream().map(WfStep::getId).collect(Collectors.toList());

            //查询审批记录
            QueryWrapper<WfApprovalRecord> approveRecordQueryWrapper = new QueryWrapper<>();
            approveRecordQueryWrapper.lambda()
                    .select(WfApprovalRecord::getRequestId, WfApprovalRecord::getId)
                    .in(WfApprovalRecord::getStepId, stepIds);

            List<WfApprovalRecord> approvalRecords = wfApprovalRecordService.list(approveRecordQueryWrapper);

            //审批单id
            List<Long> requestIds = approvalRecords.stream().map(WfApprovalRecord::getRequestId).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(requestIds)) {

                wfApprovalRequestService.removeByIds(requestIds);
                wfApprovalRecordService.removeByIds(requestIds);
                wfApprovalRequestViewService.removeByIds(requestIds);
            }

            //删除步骤
            wfStepService.remove(stepQueryWrapper);
        }

        removeById(id);

        return "";
    }

    public WfTypeStepVO modifyApproveStep(WfTypeStepDTO step) {

        if (step.getId() == null) {
            return WfTypeStepVO.builder().message("当前节点id请勿为空！").build();
        }
        if (step.getTypeId() == null) {
            return WfTypeStepVO.builder().message("当前审批流id请勿为空！").build();
        }

        //步骤-配置信息
        buildStepConfig(step);

        List<BizCustomizeForm> formList = new ArrayList<>();
        List<StepForm> formValues = step.getFormValues();

        if (!CollectionUtils.isEmpty(formValues)) {

            //删除历史form信息
            QueryWrapper<BizCustomizeForm> formQueryWrapper = new QueryWrapper<>();
            formQueryWrapper.lambda().eq(BizCustomizeForm::getStepId, step.getId());
            bizCustomizeFormService.remove(formQueryWrapper);

            List<StepForm> editFormValues = step.getEditFormValues();

            formValues.forEach(form -> {

                //设置编辑字段标记位
                if (!CollectionUtils.isEmpty(editFormValues)) {
                    for (StepForm editFormValue : editFormValues) {
                        if (editFormValue.getType().equals(form.getType()) && editFormValue.getName().equals(form.getName())) {
                            form.setIsEdit(StepEditType.ALL.getCode());
                            form.setSkip(true);
                        }
                    }
                }

                //审核字段
                formList.add(BizCustomizeForm.builder()
                        .stepId(step.getId())
                        .level(step.getStepOrder())
                        .code(form.getCode())
                        .name(form.getName())
                        .type(form.getType())
                        .createTime(new Date())
                        .isEdit(null != form.getIsEdit() ? form.getIsEdit() : StepEditType.APPRO.getCode())
                        .build());

                //编辑字段逻辑
                if (!CollectionUtils.isEmpty(editFormValues)) {
                    for (StepForm editFormValue : editFormValues) {
                        if (editFormValue.getSkip() == null) {
                            formList.add(BizCustomizeForm.builder()
                                    .stepId(step.getId())
                                    .level(step.getStepOrder())
                                    .code(editFormValue.getCode())
                                    .name(editFormValue.getName())
                                    .type(editFormValue.getType())
                                    .createTime(new Date())
                                    .isEdit(StepEditType.EDIT.getCode())
                                    .build());
                        }
                    }
                }
            });
        }

        if (!CollectionUtils.isEmpty(formList)) {
            bizCustomizeFormService.saveBatch(formList);
        }

        return WfTypeStepVO.builder().typeId(step.getTypeId()).id(step.getId()).build();
    }

    private void buildStepConfig(WfTypeStepDTO step) {
        //删除步骤审核表单-配置信息
        QueryWrapper<WfStepConfig> configQueryWrapper = new QueryWrapper<>();
        configQueryWrapper.lambda().eq(WfStepConfig::getStepId, step.getId());
        wfStepConfigService.remove(configQueryWrapper);

        //保存步骤审核表单-配置信息
        JSONObject formConfig = step.getFormConfig();
        if (formConfig != null && !formConfig.isEmpty()) {
            wfStepConfigService.save(
                    new WfStepConfig().setStepId(step.getId()).setIsEdit(StepEditType.APPRO.getCode())
                            .setStepConfig(formConfig.toJSONString())
            );
        }

        //保存步骤编辑表单-配置信息
        JSONObject editFormConfig = step.getEditFormConfig();
        if (editFormConfig != null && !editFormConfig.isEmpty()) {
            wfStepConfigService.save(
                    new WfStepConfig().setStepId(step.getId()).setIsEdit(StepEditType.EDIT.getCode())
                            .setStepConfig(editFormConfig.toJSONString())
            );
        }
    }

    public WfTypeStepVO modifyApproveFlow(WfType dto) {

        if (dto.getId() == null) {

            Long userId = UserContextHolder.getUserId();
            if (null != userId) {
                dto.setCreateId(userId);
                dto.setCreateName(UserContextHolder.getUserName());
            } else {
                dto.setCreateId(1L);
                dto.setCreateName("ABC");
            }

            ApprovalBillType approvalBillType = ApprovalBillType.fromCode(dto.getType());

            if (approvalBillType == null) {
                throw new RuntimeException("所属分类有误，请重新选择！");
            }

            dto.setTypeNo(genTypeNo());

            dto.setClassifyType(approvalBillType.getClassifyName());
            dto.setStatus(ApprovalStatus.PENDING_SUBMIT.getCode());
            dto.setType(dto.getType());
            dto.setCreateTime(new Date());
            dto.setUpdateTime(new Date());
            dto.setIsActive(1);
            save(dto);
        }

        WfTypeStepVO vo = WfTypeStepVO.builder().typeId(dto.getId()).build();

        //步骤表
        List<WfStep> stepList = dto.getStepList();

        if (!CollectionUtils.isEmpty(stepList)) {

            stepList.forEach(step -> {

                if (step.getId() == null) {

                    //构建审批流步骤节点对象
                    WfStep build = WfStep.builder()
                            .typeId(dto.getId())
                            .typeName(dto.getTypeName())
                            .stepName(step.getStepName())
                            .shortName(step.getStepName())
                            .shortName(step.getShortName())
                            .stepOrder(step.getStepOrder())
                            .stepType(step.getStepType())// 开始，中间，结束
                            .approveType(step.getApproveType())//或签，会签，依次审批
                            .assignType(step.getAssignType())
                            .assignId(!CollectionUtils.isEmpty(step.getAssignIds()) ? String.join(",", step.getAssignIds()) : null)
                            .assignNames(!CollectionUtils.isEmpty(step.getAssignNameList()) ? String.join(",", step.getAssignNameList()) : null)
                            .assignPermission(step.getAssignPermission())
                            .copyId(!CollectionUtils.isEmpty(step.getCopyIds()) ? String.join(",", step.getCopyIds()) : null)
                            .copyNames(!CollectionUtils.isEmpty(step.getCopyNameList()) ? String.join(",", step.getCopyNameList()) : null)
                            .build();
                    wfStepService.save(build);
                    vo.setId(build.getId());

                    //增加插入节点排序值逻辑 后面若有节点，排序值+1
                    modifyStepOrder(dto.getId(), step.getStepOrder(), 1);

                } else {

                    if (!CollectionUtils.isEmpty(step.getAssignIds())) {
                        step.setAssignId(String.join(",", step.getAssignIds()));
                    }
                    if (!CollectionUtils.isEmpty(step.getAssignNameList())) {
                        step.setAssignNames(String.join(",", step.getAssignNameList()));
                    }
                    if (!CollectionUtils.isEmpty(step.getCopyIds())) {
                        step.setCopyId(String.join(",", step.getCopyIds()));
                    }
                    if (!CollectionUtils.isEmpty(step.getCopyNameList())) {
                        step.setCopyNames(String.join(",", step.getCopyNameList()));
                    }
                    step.setShortName(step.getStepName());
                    step.setUpdateTime(new Date());

                    wfStepService.updateById(step);
                    vo.setId(step.getId());
                }
            });
        }

        //更新主表
        return vo;
    }

    private String genTypeNo() {

        String typeNo = wfTypeMapper.getMaxTypeNo();
        if (!StringUtils.isEmpty(typeNo)) {
            int i = Integer.parseInt(typeNo);
            return String.format("%04d", i + 1);
        }

        return String.format("%04d", 1);
    }


    /**
     * 提交审批流，进入审批流的审核流程
     *
     * @param id
     * @return
     */
    public Boolean submitApprove(Long id) {

        //当前审批流
        WfType wfType = getById(id);
        if (wfType == null) {
            throw new RuntimeException("无效的审批流！");
        }
        wfType.setStatus(ApprovalStatus.UNDER_REVIEW.getCode());//进入审批中


        //获取审批流信息
        QueryWrapper<WfType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .ne(WfType::getClassifyType, ApprovalBillType.SubmitApprove.getClassifyName())
                .eq(WfType::getType, ApprovalBillType.SubmitApprove.getName());
        List<WfType> list = list(queryWrapper);

        //负责审批当前审批流的审批流
        WfType approveType = null;
        if (!CollectionUtils.isEmpty(list)) {
            approveType = list.get(0);
        }

        if (approveType == null) {
            return false;
        }

        //创建审批单
        WfApprovalRequest approvalRequest = new WfApprovalRequest()
                .setTitle(approveType.getTypeName())
                .setApplicantId("1")
                .setApplicantName("ABCD")
                .setBillType(approveType.getType())
                .setContent(approveType.getDescription())
                .setBillName(ApprovalBillType.SubmitApprove.getName())
                .setBillNo("TH-" + ApprovalBillType.SubmitApprove.getName() + "-" + id)
                .setRequestStatus(ApprovalStatus.UNDER_REVIEW.getCode())
                .setRequestStatusName(ApprovalStatus.UNDER_REVIEW.getDescription())
                .setCreateTime(new Date());

        Long userId = UserContextHolder.getUserId();
        if (userId != null) {
            approvalRequest.setApplicantId(String.valueOf(userId)).setApplicantName(UserContextHolder.getUserName());
        }

        //保存审批单记录
        wfApprovalRequestService.save(approvalRequest);

        //生成待审批记录
        wfStepService.generateApprovalRecords(null, approvalRequest, approveType.getId());

        wfType.setRequestId(approvalRequest.getId());
        wfType.setUpdateTime(new Date());

        return updateById(wfType);
    }

    public Boolean delApproveStep(Long id) {

        WfStep wfStep = wfStepService.getById(id);

        //当前删除节点后面的节点 -1
        modifyStepOrder(wfStep.getTypeId(), wfStep.getStepOrder(), -1);

        wfStepService.removeById(id);
        return true;
    }

    private void modifyStepOrder(Long typeId, Integer stepOrder, int modifyValue) {
        QueryWrapper<WfStep> stepQueryWrapper = new QueryWrapper<>();
        if (modifyValue > 0) {
            stepQueryWrapper.lambda().eq(WfStep::getTypeId, typeId).ge(WfStep::getStepOrder, stepOrder);
        } else {
            stepQueryWrapper.lambda().eq(WfStep::getTypeId, typeId).gt(WfStep::getStepOrder, stepOrder);
        }

        List<WfStep> backStepList = wfStepService.list(stepQueryWrapper);

        if (!CollectionUtils.isEmpty(backStepList)) {
            backStepList.forEach(step -> {
                step.setStepOrder(step.getStepOrder() + modifyValue);
                wfStepService.updateById(step);
            });
        }
    }


    /**
     * 高级审批流-提交
     *
     * @param id
     * @return
     */
    public Boolean simpleSubmit(Long id) {

        WfType wfType = getById(id);
        if (wfType == null) {
            throw new RuntimeException("无效的审批流！");
        }
        wfType.setStatus(ApprovalStatus.APPROVED.getCode());//进入审批中

        return updateById(wfType);
    }
}
