package com.th.cms.modular.wf.wechatContact.model.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 创建企微建联参数
 */
@Data
public class WechatContactSaveReq {

    /** 达人ID*/
    @NotNull(message = "达人Id为空!")
    private Long influencerId;

    /** 达人明月号昵称*/
    @NotNull(message = "达人昵称为空!")
    private String nickName;

    /** 达人手机号*/
    @NotBlank(message = "达人手机号为空!")
    private String phone;

    /** 达人类型*/
    @NotNull(message = "达人类型为空!")
    private Integer influencerType;

    /** 提审记录id*/
    @NotNull(message = "提审记录id为空!")
    private Long submitApproveId;

    /** 商务客服部门id*/
    @NotNull(message = "商务客服部门id为空!")
    private Long customerDeptId;

    /** 商务客服部门名称*/
    @NotBlank(message = "商务客服部门名称为空!")
    private String customerDeptName;

    /** 商务客服ID*/
    @NotNull(message = "商务客服id为空!")
    private Long customerId;

    /** 商务姓名*/
    @NotBlank(message = "商务客服姓名为空!")
    private String customerName;

    /** 合作平台id*/
    @NotNull(message = "合作平台为空!")
    private Long platformId;

    /** 合作平台名称*/
    @NotBlank(message = "合作平台名称为空!")
    private String platformName;

    /** 合作项目id*/
    @NotNull(message = "合作项目id为空!")
    private Long projectId;

    /** 合作项目名称*/
    @NotBlank(message = "合作项目名称为空!")
    private String projectName;

    /** 达人企业微信*/
    @NotBlank(message = "达人企业微信id为空!")
    private String comWechatId;

    /** 认证图片url*/
    @NotBlank(message = "认证图片为空!")
    private String authImg;

    /** 认证视频url*/
    @NotBlank(message = "认证视频为空!")
    private String authVideo;

    /** 审核状态*/
    private Integer auditStatus;

    /** 创建人ID*/
    private Long createId;

    /** 创建时间*/
    private Date createTime;

    /** 更新时间*/
    private Date updateTime;
}
