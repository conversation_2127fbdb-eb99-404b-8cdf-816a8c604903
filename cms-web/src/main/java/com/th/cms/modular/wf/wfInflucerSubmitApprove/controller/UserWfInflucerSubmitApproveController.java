package com.th.cms.modular.wf.wfInflucerSubmitApprove.controller;

import cn.stylefeng.roses.core.base.controller.BaseController;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.enums.ProjectStatus;
import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.service.BizPlatformService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.*;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums.InflucerType;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.model.reqparam.WfInflucerSubmitApproveListParam;
import com.th.cms.modular.wf.wfInflucerSubmitApprove.service.WfInflucerSubmitApproveService;
import com.th.cms.modular.wf.wfStep.model.StepNodeType;
import com.th.cms.modular.wf.wfStep.model.WfStep;
import com.th.cms.modular.wf.wfStep.model.WfStepConfig;
import com.th.cms.modular.wf.wfStep.service.WfStepConfigService;
import com.th.cms.modular.wf.wfStep.service.WfStepService;
import com.th.cms.modular.wf.wfType.model.StepTypeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * wfInflucerSubmitApprove控制器
 *
 * <AUTHOR>
 * @Date 2025-05-27 14:53:59
 */
@Controller
@RequestMapping("/user/submitApprove")
public class UserWfInflucerSubmitApproveController extends BaseController {

    @Autowired
    private WfInflucerSubmitApproveService wfInflucerSubmitApproveService;
    @Resource
    private SettleProjectsService settleProjectsService;
    @Resource
    private BizPlatformService bizPlatformService;
    @Autowired
    private WfStepService wfStepService;
    @Autowired
    private WfStepConfigService wfStepConfigService;


    /**
     * 获取wfInflucerSubmitApprove列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public LayuiPageInfo list(WfInflucerSubmitApproveListParam wfInflucerSubmitApproveParam) {
        return wfInflucerSubmitApproveService.findPageBySpec(wfInflucerSubmitApproveParam);
    }

    /**
     * 达人类型
     *
     * @return
     */
    @GetMapping(value = "/influcerType")
    @ResponseBody
    public ResponseData influcerType() {

        List<StepTypeVO> vos = new ArrayList<>();
        InflucerType[] values = InflucerType.values();
        for (InflucerType value : values) {
            vos.add(StepTypeVO.builder().name(value.name).value(value.value).build());
        }

        return ResponseData.success(vos);
    }

    /**
     * 外站平台
     * 取第二级，没有第二级取第一级
     *
     * @return
     */
    @GetMapping(value = "/outPlatform")
    @ResponseBody
    public ResponseData outPlatform() {

        QueryWrapper<BizPlatform> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizPlatform::getLevels, 2);
        List<BizPlatform> list = bizPlatformService.list(queryWrapper);

        QueryWrapper<BizPlatform> firstQueryWrapper = new QueryWrapper<>();
        firstQueryWrapper.lambda().eq(BizPlatform::getLevels, 1);
        List<BizPlatform> firstList = bizPlatformService.list(firstQueryWrapper);

        List<BizPlatform> collect = firstList.stream().filter(f -> {
            for (BizPlatform bizPlatform : list) {
                if (bizPlatform.getParentId().equals(f.getId())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(collect)) {
            list.addAll(collect);
        }

        return ResponseData.success(list);
    }


    /**
     * 合作平台
     * 二、三级平台列表
     *
     * @return
     */
    @GetMapping(value = "/platformList")
    @ResponseBody
    public ResponseData platformList() {

        QueryWrapper<BizPlatform> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().gt(BizPlatform::getLevels, 1);
        List<BizPlatform> list = bizPlatformService.list(queryWrapper);
        return ResponseData.success(list);
    }

    /**
     * 根据合作平台查询项目列表
     *
     * @param platformId
     * @return
     */
    @GetMapping(value = "/projectList")
    @ResponseBody
    public ResponseData projectList(@RequestParam(required = false) Long platformId) {

        QueryWrapper<SettleProjects> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(SettleProjects::getId, SettleProjects::getProjectName,
                        SettleProjects::getPlatformId, SettleProjects::getPlatform,
                        SettleProjects::getFlowId)
                .eq(null != platformId, SettleProjects::getPlatformId, platformId)
                .eq(SettleProjects::getProjectStatus, ProjectStatus.APPROVED.value);
        List<SettleProjects> list = settleProjectsService.list(queryWrapper);
        return ResponseData.success(list);
    }

    /**
     * 提审表单页配置
     *
     * @param flowId
     * @return
     */
    @GetMapping(value = "/submitForm")
    @ResponseBody
    public ResponseData submitForm(@RequestParam Long flowId) {

        if (flowId != null) {

            QueryWrapper<WfStep> stepQueryWrapper = new QueryWrapper<>();
            stepQueryWrapper.lambda().eq(WfStep::getTypeId, flowId).eq(WfStep::getStepType, StepNodeType.StartNode.getCode());
            List<WfStep> startSteps = wfStepService.list(stepQueryWrapper);

            if (!CollectionUtils.isEmpty(startSteps)) {

                QueryWrapper<WfStepConfig> configQueryWrapper = new QueryWrapper<>();
                configQueryWrapper.lambda().eq(WfStepConfig::getStepId, startSteps.get(0).getId());
                WfStepConfig stepConfig = wfStepConfigService.getOne(configQueryWrapper);
                return ResponseData.success(stepConfig);
            }
        }

        return ResponseData.success();
    }

    /**
     * 提审操作
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/submit")
    @ResponseBody
    public ResponseData submitApprove(@RequestBody UploadSubmitApproveDTO dto) throws IllegalAccessException {
        String message = wfInflucerSubmitApproveService.submitApprove(dto);
        if (!StringUtils.isEmpty(message)) {
            return ResponseData.error(message);
        }
        return ResponseData.success();
    }

    /**
     * 提审详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/detail")
    @ResponseBody
    public ResponseData approveDetail(@RequestParam Long id) {

        SubmitApproveVO submitApproveVO = wfInflucerSubmitApproveService.approveDetail(id);

        if (!StringUtils.isEmpty(submitApproveVO.getMessage())) {
            return ResponseData.error(submitApproveVO.getMessage());
        }

        return ResponseData.success(submitApproveVO);
    }

    @DeleteMapping(value = "/delete")
    @ResponseBody
    public ResponseData approveDelete(@RequestParam Long id) {
        return ResponseData.success(wfInflucerSubmitApproveService.removeById(id));
    }

    /**
     * 审核
     * 提审单（WfInflucerSubmitApprove）
     * 审批单（WfApprovalRequest）
     * 审批记录（WfApprovalRecord）
     */
    @PostMapping(value = "/approve")
    @ResponseBody
    public ResponseData approve(@RequestBody SubmitApproveRequestDTO dto) {
        String message = wfInflucerSubmitApproveService.approve(dto);
        if (!StringUtils.isEmpty(message)) {
            return ResponseData.error(message);
        }
        return ResponseData.success();
    }

    /**
     * 绑定、运营绑定操作-不是审核-
     * @param dto
     * @return
     * @throws IllegalAccessException
     */
    @PostMapping(value = "/operateRecord")
    @ResponseBody
    public ResponseData operateRecord(@RequestBody OperateRecordDTO dto) throws IllegalAccessException {
        String message = wfInflucerSubmitApproveService.operateRecord(dto);
        if (!StringUtils.isEmpty(message)) {
            return ResponseData.error(message);
        }
        return ResponseData.success();
    }

    /**
     * 驳回指定节点
     *
     * @param wfInflucerSubmitApprove
     * @return
     */
    //TODO 驳回指定节点
    @PostMapping(value = "/revoked")
    @ResponseBody
    public ResponseData revoked(WfInflucerSubmitApprove wfInflucerSubmitApprove) {

        //驳回
//        {
//                  "id": "数据id-详情中取",
//                  "recordId": "记录id-详情中取",
//                  "stepId": "被驳回到节点id",
//                  "approvalComment": "拒绝原因"
//        }

        return ResponseData.success();
    }


}
