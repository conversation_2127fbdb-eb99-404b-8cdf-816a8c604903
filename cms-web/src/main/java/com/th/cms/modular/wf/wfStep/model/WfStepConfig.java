package com.th.cms.modular.wf.wfStep.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@ApiModel(value = "WfStep配置对象", description = "")
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class WfStepConfig implements Serializable {

    @TableField("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "关联流程类型ID")
    @TableField("step_id")
    private Long stepId;

    @ApiModelProperty(value = "是否编辑配置  0：审核  1编辑")
    @TableField("is_edit")
    private Integer isEdit;

    @ApiModelProperty(value = "步骤配置")
    @TableField("step_config")
    private String stepConfig;

}
