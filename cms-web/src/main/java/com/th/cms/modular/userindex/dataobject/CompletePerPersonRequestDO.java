package com.th.cms.modular.userindex.dataobject;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CompletePerPersonRequestDO {
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 是否要全部
     */
    private Boolean isAll;
    /**
     * 部门id
     */
    private List<Long> deptIds;
    /**
     * 1 按月统计 2 按日统计
     */

    private Integer queryType=0;
}
