package com.th.cms.modular.influcer.cooperationPlatform.convert;

import cn.stylefeng.roses.kernel.model.enums.YesOrNotEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuth;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuthAppConfig;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuthAppConfigField;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuthAppConfigFieldRel;
import com.th.cms.modular.bizPlatform.mapper.BizPlatformAuthAppConfigFieldMapper;
import com.th.cms.modular.bizPlatform.mapper.BizPlatformAuthAppConfigFieldRelMapper;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformAddRequestDTO;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformAuthDTO;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformDTO;
import com.th.cms.modular.influcer.cooperationPlatform.dto.BasePlatformAddRequestDTO;
import com.th.cms.util.UserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class AuthPlatformConvert {
    @Autowired
    private BizPlatformAuthAppConfigFieldRelMapper bizPlatformAuthAppConfigFieldRelMapper;
    @Autowired
    private BizPlatformAuthAppConfigFieldMapper bizPlatformAuthAppConfigFieldMapper;

    public static BizPlatformAuth convertBizPlatformAuth(BasePlatformAddRequestDTO paramDTO, UserDTO userNotNull) {
        BizPlatformAuth bizPlatformAuth = new BizPlatformAuth();
        bizPlatformAuth.setId(paramDTO.getId());
        bizPlatformAuth.setPlatName(paramDTO.getPlatName());
        bizPlatformAuth.setIconPic(paramDTO.getIconPic());
        bizPlatformAuth.setJiancheng(paramDTO.getJiancheng());
        bizPlatformAuth.setCreateTime(new Date());
        bizPlatformAuth.setUpdateTime(new Date());
        bizPlatformAuth.setStatus(YesOrNotEnum.Y.getCode());
        return bizPlatformAuth;


    }

    public static BizPlatformAuthAppConfigFieldRel convertFieldRel(AppPlatformAddRequestDTO.FieldDTO fieldDTO, BizPlatformAuthAppConfig record, UserDTO userNotNull) {
        BizPlatformAuthAppConfigFieldRel bizPlatformAuthAppConfigFieldRel = new BizPlatformAuthAppConfigFieldRel();
        bizPlatformAuthAppConfigFieldRel.setFiledId(fieldDTO.getFieldId());
        bizPlatformAuthAppConfigFieldRel.setAuthConfigId(record.getId());
        bizPlatformAuthAppConfigFieldRel.setAuthPlatId(record.getAuthPlatId());
        bizPlatformAuthAppConfigFieldRel.setStatus(YesOrNotEnum.Y.getCode());
        bizPlatformAuthAppConfigFieldRel.setCreateId(userNotNull.getUserId());
        bizPlatformAuthAppConfigFieldRel.setCreateName(userNotNull.getName());
        bizPlatformAuthAppConfigFieldRel.setCreateTime(new Date());
        bizPlatformAuthAppConfigFieldRel.setUpdateId(userNotNull.getUserId());
        bizPlatformAuthAppConfigFieldRel.setUpdateName(userNotNull.getName());
        bizPlatformAuthAppConfigFieldRel.setUpdateTime(new Date());
        return bizPlatformAuthAppConfigFieldRel;


    }

    public AppPlatformDTO convertAppPlatformDTO(AppPlatformDTO t) {
        AppPlatformDTO appPlatformDTO = new AppPlatformDTO();
        appPlatformDTO.setId(t.getId());
        appPlatformDTO.setPlatId(t.getPlatId());
        appPlatformDTO.setPlatName(t.getPlatName());
        appPlatformDTO.setIconPic(t.getIconPic());
        appPlatformDTO.setStatus(t.getStatus() == null ? YesOrNotEnum.N.getCode() : t.getStatus());
        appPlatformDTO.setFieldList(queryFieldList(t.getId()));
        return appPlatformDTO;


    }

    private List<AppPlatformDTO.FieldDTO> queryFieldList(Long id) {
        List<BizPlatformAuthAppConfigFieldRel> bizPlatformAuthAppConfigFieldRels = bizPlatformAuthAppConfigFieldRelMapper.selectList(new LambdaQueryWrapper<BizPlatformAuthAppConfigFieldRel>()
                .eq(BizPlatformAuthAppConfigFieldRel::getAuthConfigId, id));
        List<BizPlatformAuthAppConfigField> bizPlatformAuthAppConfigFields = bizPlatformAuthAppConfigFieldMapper.selectList(null);
        return bizPlatformAuthAppConfigFields.stream().map(t -> {
            AppPlatformDTO.FieldDTO fieldDTO = new AppPlatformDTO.FieldDTO();
            fieldDTO.setFieldId(t.getId());
            fieldDTO.setFieldName(t.getFiledName());
            fieldDTO.setUseField(bizPlatformAuthAppConfigFieldRels.stream().anyMatch(r -> r.getFiledId().equals(t.getId())) ? YesOrNotEnum.Y.getCode() : YesOrNotEnum.N.getCode());
            return fieldDTO;
        }).collect(Collectors.toList());

    }

    public BizPlatformAuthAppConfig convertBizPlatformAppConfig(AppPlatformAddRequestDTO paramDTO, UserDTO userNotNull) {
        BizPlatformAuthAppConfig bizPlatformAuthAppConfig = new BizPlatformAuthAppConfig();
        bizPlatformAuthAppConfig.setId(paramDTO.getId());
        bizPlatformAuthAppConfig.setAuthPlatId(paramDTO.getAuthPlatId());
        bizPlatformAuthAppConfig.setAuthPlatName(paramDTO.getAuthPlatName());
        bizPlatformAuthAppConfig.setStatus(paramDTO.getStatus());
        bizPlatformAuthAppConfig.setAuthAccount(paramDTO.getAuthAccount());
        bizPlatformAuthAppConfig.setAuthContent(paramDTO.getAuthContent());
        bizPlatformAuthAppConfig.setCreateId(userNotNull.getUserId());
        bizPlatformAuthAppConfig.setCreateName(userNotNull.getName());
        bizPlatformAuthAppConfig.setCreateTime(new Date());
        bizPlatformAuthAppConfig.setUpdateId(userNotNull.getUserId());
        bizPlatformAuthAppConfig.setUpdateName(userNotNull.getName());
        bizPlatformAuthAppConfig.setUpdateTime(new Date());
        return bizPlatformAuthAppConfig;


    }

    public List<AppPlatformDTO.FieldDTO> fieldList() {
        List<BizPlatformAuthAppConfigField> bizPlatformAuthAppConfigFields = bizPlatformAuthAppConfigFieldMapper.selectList(null);
        return bizPlatformAuthAppConfigFields.stream().map(t -> {
            AppPlatformDTO.FieldDTO fieldDTO = new AppPlatformDTO.FieldDTO();
            fieldDTO.setFieldId(t.getId());
            fieldDTO.setFieldName(t.getFiledName());
            fieldDTO.setUseField(YesOrNotEnum.N.getCode());
            return fieldDTO;
        }).collect(Collectors.toList());

    }



    public List<AppPlatformAuthDTO.FieldDTO> fauthFieldList() {
        List<BizPlatformAuthAppConfigField> bizPlatformAuthAppConfigFields = bizPlatformAuthAppConfigFieldMapper.selectList(null);
        return bizPlatformAuthAppConfigFields.stream().map(t -> {
            AppPlatformAuthDTO.FieldDTO fieldDTO = new AppPlatformAuthDTO.FieldDTO();
            fieldDTO.setFieldId(t.getId());
            fieldDTO.setFieldName(t.getFiledName());
            fieldDTO.setUseField(YesOrNotEnum.N.getCode());
            return fieldDTO;
        }).collect(Collectors.toList());

    }
    private List<AppPlatformAuthDTO.FieldDTO> queryAuthFieldList(Long id) {
        List<BizPlatformAuthAppConfigFieldRel> bizPlatformAuthAppConfigFieldRels = bizPlatformAuthAppConfigFieldRelMapper.selectList(new LambdaQueryWrapper<BizPlatformAuthAppConfigFieldRel>()
                .eq(BizPlatformAuthAppConfigFieldRel::getAuthConfigId, id));
        List<BizPlatformAuthAppConfigField> bizPlatformAuthAppConfigFields = bizPlatformAuthAppConfigFieldMapper.selectList(null);
        return bizPlatformAuthAppConfigFields.stream().map(t -> {
            AppPlatformAuthDTO.FieldDTO fieldDTO = new AppPlatformAuthDTO.FieldDTO();
            fieldDTO.setFieldId(t.getId());
            fieldDTO.setFieldName(t.getFiledName());
            fieldDTO.setUseField(bizPlatformAuthAppConfigFieldRels.stream().anyMatch(r -> r.getFiledId().equals(t.getId())) ? YesOrNotEnum.Y.getCode() : YesOrNotEnum.N.getCode());
            return fieldDTO;
        }).collect(Collectors.toList());

    }
    public AppPlatformAuthDTO convertAppAuthPlatformDTO(AppPlatformAuthDTO t) {
        AppPlatformAuthDTO appPlatformAuthDTO = new AppPlatformAuthDTO();
        appPlatformAuthDTO.setId(t.getId());
        appPlatformAuthDTO.setAuthPlatId(t.getAuthPlatId());
        appPlatformAuthDTO.setAuthPlatName(t.getAuthPlatName());
        appPlatformAuthDTO.setStatus(t.getStatus());
        appPlatformAuthDTO.setAuthAccount(t.getAuthAccount());
        appPlatformAuthDTO.setAuthContent(t.getAuthContent());
        appPlatformAuthDTO.setFieldList(queryAuthFieldList(t.getId()));
        return appPlatformAuthDTO;


    }
}
