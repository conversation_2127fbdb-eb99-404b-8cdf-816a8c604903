package com.th.cms.modular.settle.settleBatchConfirm.controller;


import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.th.cms.core.log.LogObjectHolder;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.modular.enums.ApprovalBillType;
import com.th.cms.modular.enums.ApprovalStatus;
import com.th.cms.modular.oss.util.OssConfig;
import com.th.cms.modular.settle.settleBatch.model.SettleBatch;
import com.th.cms.modular.settle.settleBatch.service.SettleBatchExtService;
import com.th.cms.modular.settle.settleBatchConfirm.model.SettleBatchConfirm;
import com.th.cms.modular.settle.settleBatchConfirm.model.dto.SettleBatchConfirmApprovalDTO;
import com.th.cms.modular.settle.settleBatchConfirm.service.SettleBatchConfirmService;
import com.th.cms.modular.wf.wfApprovalRecord.model.WfApprovalRecord;
import com.th.cms.modular.wf.wfApprovalRequest.model.WfRequestRsp;
import com.th.cms.modular.wf.wfApprovalRequest.service.WfApprovalRequestExtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>
 * 结算批次确认
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */

@Controller
@RequestMapping("/settleBatchConfirm")
public class SettleBatchConfirmController {

    private String PREFIX = "/modular/settleBatchConfirm/settleBatchConfirm/";
    @Autowired
    SettleBatchConfirmService settleBatchConfirmService;
    @Autowired
    SettleBatchExtService settleBatchExtService;
    @Autowired
    WfApprovalRequestExtService wfApprovalRequestExtService;

    @RequestMapping("/getSettleBatchConfirm")
    @ResponseBody
    public ResponseData<SettleBatchConfirm> getSettleBatchConfirm(@RequestParam Integer settleBatchId) {
        SettleBatchConfirm settleBatchConfirm = settleBatchConfirmService.lambdaQuery().eq(SettleBatchConfirm::getSettleBatchId, settleBatchId).one();
        if(settleBatchConfirm != null) {
            if(StringUtils.isNotEmpty(settleBatchConfirm.getBusinessFile())) {
                settleBatchConfirm.setBusinessFile(OssConfig.hostPre + settleBatchConfirm.getBusinessFile());
            }
            if(StringUtils.isNotEmpty(settleBatchConfirm.getFinanceFile())) {
                settleBatchConfirm.setFinanceFile(OssConfig.hostPre + settleBatchConfirm.getFinanceFile());
            }
        }
        return ResponseData.success(settleBatchConfirm);
    }
    
    @RequestMapping("/confirmView")
    public String confirmView(@RequestParam Integer settleBatchId, @RequestParam Integer type, Model model) {
        SettleBatch settleBatch = settleBatchExtService.queryById(settleBatchId);
        model.addAttribute("item", settleBatch);
        SettleBatchConfirm settleBatchConfirm = settleBatchConfirmService.lambdaQuery().eq(SettleBatchConfirm::getSettleBatchId, settleBatch.getId()).one();
        model.addAttribute("item2", settleBatchConfirm);

        WfRequestRsp<SettleBatchConfirm> wfRequestRsp = wfApprovalRequestExtService.queryReqAuditInfos(settleBatch.getBatchNo(), ApprovalBillType.SettleOrderConfirm, ShiroKit.getUser().getId() + "");
        model.addAttribute("wfRequestRsp", wfRequestRsp);
        model.addAttribute("authuser", ShiroKit.getUser());

        boolean showFlag = false;

        for(WfApprovalRecord wfApprovalRecord:wfRequestRsp.getWfApprovalRecordList()){
            if (wfApprovalRecord.getApproverId().equals(ShiroKit.getUser().getId())) {
                model.addAttribute("wfApprovalRecord", wfApprovalRecord);
                ApprovalStatus approvalStatus = ApprovalStatus.fromCode(wfApprovalRecord.getApprovalStatus());
                if(ApprovalStatus.UNDER_REVIEW.equals(approvalStatus)){
                    showFlag = true;
                }
            }
        }
        
        model.addAttribute("showFlag", showFlag);
        LogObjectHolder.me().set(settleBatch);
        if(type == 1){
            //业务审批页面
            return PREFIX + "settleBatchConfirmBusiness.html";
        }else{
            //财务审批页面
            return PREFIX + "settleBatchConfirmFinance.html";
        }
    }
    
    @RequestMapping("/businessApproval")
    @ResponseBody
    public String businessApproval(SettleBatchConfirmApprovalDTO settleBatchConfirmApprovalDTO, Model model) {
        settleBatchConfirmService.businessApproval(settleBatchConfirmApprovalDTO);
        return confirmView(settleBatchConfirmApprovalDTO.getSettleBatchId(), 1, model);
    }

    @RequestMapping("/financeApproval")
    @ResponseBody
    public String financeApproval(SettleBatchConfirmApprovalDTO settleBatchConfirmApprovalDTO, Model model) {
        settleBatchConfirmService.financeApproval(settleBatchConfirmApprovalDTO);
        return confirmView(settleBatchConfirmApprovalDTO.getSettleBatchId(), 1, model);
    }
}
