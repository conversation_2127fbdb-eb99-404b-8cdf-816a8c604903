package com.th.cms.modular.bizOperateLog.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.bizOperateLog.entity.BizOperateLog;
import com.th.cms.modular.bizOperateLog.mapper.BizOperateLogMapper;
import com.th.cms.modular.bizOperateLog.model.reqparam.BizOperateLogListParam;
import com.th.cms.core.util.ShiroUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class BizOperateLogService extends ServiceImpl<BizOperateLogMapper, BizOperateLog> implements IService<BizOperateLog> {

    /**
     * 查询分页数据，Specification模式
     *
     * @param param 查询参数
     * @return 分页数据
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizOperateLogListParam param) {
        QueryWrapper<BizOperateLog> objectQueryWrapper = new QueryWrapper<>();

        // 按创建时间倒序排列，最新的在前面
        objectQueryWrapper.lambda().orderByDesc(BizOperateLog::getCreateTime);

        // 根据操作人名称模糊查询
        if (StringUtils.isNotBlank(param.getStaffName())) {
            objectQueryWrapper.lambda().like(BizOperateLog::getStaffName, "%" + param.getStaffName() + "%");
        }

        // 根据操作人登录账号模糊查询
        if (StringUtils.isNotBlank(param.getStaffLoginName())) {
            objectQueryWrapper.lambda().like(BizOperateLog::getStaffLoginName, "%" + param.getStaffLoginName() + "%");
        }

        // 根据模块名称模糊查询
        if (StringUtils.isNotBlank(param.getModule())) {
            objectQueryWrapper.lambda().like(BizOperateLog::getModule, "%" + param.getModule() + "%");
        }

        // 根据操作类型查询
        if (param.getOperationType() != null) {
            objectQueryWrapper.lambda().eq(BizOperateLog::getOperationType, param.getOperationType());
        }

        // 根据操作对象名称模糊查询
        if (StringUtils.isNotBlank(param.getTargetName())) {
            objectQueryWrapper.lambda().like(BizOperateLog::getTargetName, "%" + param.getTargetName() + "%");
        }

        // 根据一级菜单模糊查询
        if (StringUtils.isNotBlank(param.getMenu1())) {
            objectQueryWrapper.lambda().like(BizOperateLog::getMenu1, "%" + param.getMenu1() + "%");
        }

        // 根据二级菜单模糊查询
        if (StringUtils.isNotBlank(param.getMenu2())) {
            objectQueryWrapper.lambda().like(BizOperateLog::getMenu2, "%" + param.getMenu2() + "%");
        }

        // 根据三级菜单模糊查询
        if (StringUtils.isNotBlank(param.getMenu3())) {
            objectQueryWrapper.lambda().like(BizOperateLog::getMenu3, "%" + param.getMenu3() + "%");
        }

        // 根据时间范围查询
        if (StringUtils.isNotBlank(param.getStartTime())) {
            try {
                LocalDateTime startTime = LocalDateTime.parse(param.getStartTime() + " 00:00:00",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                objectQueryWrapper.lambda().ge(BizOperateLog::getCreateTime, startTime);
            } catch (Exception e) {
                // 时间格式错误时忽略该条件
            }
        }

        if (StringUtils.isNotBlank(param.getEndTime())) {
            try {
                LocalDateTime endTime = LocalDateTime.parse(param.getEndTime() + " 23:59:59",
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                objectQueryWrapper.lambda().le(BizOperateLog::getCreateTime, endTime);
            } catch (Exception e) {
                // 时间格式错误时忽略该条件
            }
        }

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;
    }
}
