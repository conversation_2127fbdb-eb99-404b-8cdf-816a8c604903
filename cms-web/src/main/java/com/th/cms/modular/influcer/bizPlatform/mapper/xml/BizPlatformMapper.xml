<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.influcer.bizPlatform.mapper.BizPlatformMapper">

    <select id="queryCooperationPlatformList"
            resultType="com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform">
        SELECT a.id, a.plat_name, a.icon_pic, a.parent_id, a.levels, a.jiancheng, a.create_time, a.update_time
        FROM biz_platform a
        WHERE a.levels = 2 AND a.parent_id <![CDATA[ <> ]]> 0
        UNION ALL
        SELECT a.id, a.plat_name, a.icon_pic, a.parent_id, a.levels, a.jiancheng, a.create_time, a.update_time
        FROM biz_platform a
        LEFT JOIN biz_platform b ON a.id = b.parent_id AND b.levels = 2
        WHERE a.levels = 1 AND b.id IS NULL
        LIMIT #{page}, #{size}
    </select>
    
    <select id="queryCooperationPlatformCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM (
            SELECT 1
            FROM biz_platform a
            WHERE a.levels = 2 AND a.parent_id <![CDATA[ <> ]]> 0
            UNION ALL
            SELECT 1
            FROM biz_platform a
            LEFT JOIN biz_platform b ON a.id = b.parent_id AND b.levels = 2
            WHERE a.levels = 1 AND b.id IS NULL
        ) a 
    </select>
</mapper>
