package com.th.cms.modular.settle.contract.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ContractExcelDTO {
    /**
     * 序号
     */
    @ExcelProperty("序号")
    private Integer index;
    /**
     * 达人（签约人）的唯一标识ID
     */
    @ExcelProperty({"达人信息","达人ID"})
    private String influencerIdStr;

    /**
     * 达人的昵称
     */
    @ExcelProperty({"达人信息","达人昵称"})
    private String influencerNickname;
    /**
     * 达人实名
     */
    @ExcelProperty({"达人信息","达人实名"})
    private String influencerName;
    /**
     * 达人的联系电话
     */
    @ExcelProperty({"达人信息","达人手机号"})
    private String influencerPhoneNumber;

    /**
     * 达人的身份证号码
     */
    @ExcelProperty({"达人信息","达人身份证号码"})
    private String influencerIdCardNumber;

    /**
     * 合同编号，用于唯一标识一份合同
     */
    @ExcelProperty({"合同信息","合同编号"})
    private String contractNumber;

    /**
     * 合同发送日期
     */
    @ExcelProperty({"合同信息","合同发送"})
    private String contractSentDate;

    /**
     * 合同签订日期
     */
    @ExcelProperty({"合同信息","合同签订"})
    private String contractSignedDate;

    /**
     * 合同期限，
     */
    @ExcelProperty({"合同信息","合同周期"})
    private String contractTerm;



    /**
     * 合同状态：有效期内（可垫付），即将到期（剩余小于等于7天开始提醒），失效（不可垫付）
     */
    @ExcelProperty({"合同信息","合同状态"})
    private String contractStatusDesc;



    /**
     * 合作平台，表示达人与公司合作的具体平台名称
     */
    @ExcelProperty({"合同信息","合作平台"})
    private String cooperationPlatform;

    /**
     * 工区，表示所属的工作区域或部门
     */
    @ExcelProperty({"对接信息","工区"})
    private String workArea;

    /**
     * 小组，表示工区内负责此合同的具体小组
     */
    @ExcelProperty({"对接信息","小组"})
    private String team;

    /**
     * 商务人员，负责该合同的商务对接人姓名
     */
    @ExcelProperty({"对接信息","商务人员"})
    private String businessPerson;
    /**
     * 分成比例，达人与公司之间的收入分成百分比
     */
    @ExcelProperty({"财务信息","分成比例（%）"})
    private BigDecimal revenueShareRatio;

    /**
     * 垫付比例，公司为达人垫付金额的比例
     */
    @ExcelProperty({"财务信息","垫付比例（%）"})
    private BigDecimal advancePaymentRatio;

    /**
     * 垫付周期，单位为天，表示垫付资金的结算周期
     */
    @ExcelProperty({"财务信息","垫付周期（天）"})
    private Integer advancePaymentCycle;


}
