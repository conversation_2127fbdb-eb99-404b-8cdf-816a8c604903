package com.th.cms.modular.settle.settleCore.excelTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MeiTuanSettleExcel {

    @ExcelProperty(value = "达人昵称")
    private String influencerName;

    @ExcelProperty(value = "结算金额")
    private BigDecimal amount;

    @ExcelProperty(value = "机构名称")
    private String companyName;

    @ExcelProperty(value = "手机号")
    private String influencerPhone;

    //达人ID
    private String influencerId;
}
