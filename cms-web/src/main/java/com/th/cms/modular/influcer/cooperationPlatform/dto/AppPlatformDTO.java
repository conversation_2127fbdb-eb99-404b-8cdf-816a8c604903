package com.th.cms.modular.influcer.cooperationPlatform.dto;

import lombok.Data;

import java.util.List;

@Data
public class AppPlatformDTO {
    /**
     * id
     */
    private Long id;
    /**
     * 平台名称
     */
    private Long platId;
    /**
     * 平台名称
     */
    private String platName;
    /**
     * 图标
     */
    private String iconPic;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 认证用户
     */
    private String authAccount;

    /**
     * 认证内容
     */
    private String authContent;
    /**
     * 字段列表
     */
    private List<FieldDTO> fieldList;

    @Data
    public static class FieldDTO {
        /**
         * 字段ID
         */
        private Long fieldId;
        /**
         * 字段名称
         */
        private String fieldName;
        /**
         * 是否使用
         */
        private Integer useField;
    }

}

