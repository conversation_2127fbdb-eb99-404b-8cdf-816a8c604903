package com.th.cms.modular.enums;

public enum SettleBatchStatus {

    /**
     * <option value="1">起点</option>
     * <option value="2">过程</option>
     * <option value="3">结束</option>
     */
    <PERSON><PERSON><PERSON><PERSON><PERSON>(2003, "审批中"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(2004, "审批完成"),

    <PERSON><PERSON><PERSON>(3004, "已驳回"),
    <PERSON><PERSON><PERSON>(3005, "已撤回"),

    <PERSON><PERSON>(2005,"金额发放中"),
    <PERSON><PERSON>(2006,"金额已发放"),

    ;
    /**
     * 状态码（持久化到数据库的值）
     */
    private final Integer code;

    /**
     * 状态描述（前端展示用）
     */
    private final String name;

    SettleBatchStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态码
     * @return 对应的枚举实例，未找到时返回null
     */
    public static SettleBatchStatus fromCode(Integer code) {
        for (SettleBatchStatus status : SettleBatchStatus.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

}
