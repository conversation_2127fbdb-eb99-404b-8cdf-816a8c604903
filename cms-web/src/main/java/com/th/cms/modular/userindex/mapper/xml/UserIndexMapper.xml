<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.userindex.mapper.UserIndexMapper">


    <select id="authenticationStatusPerPerson"
            resultType="com.th.cms.modular.userindex.dataobject.AuthenticationStatusPerPersonDO">
        select
        t.group_name group_name,
        t.dept_id ,
        t.cn,
        a.user_count
        from
        ( select
        su.dept_name group_name,
        su.dept_id ,
        count(bip.influcer_id) cn
        from
        biz_influcer_platform bip
        left join sys_user su on
        bip.business_id = su.user_id
        where
        1 = 1
        and su.dept_id is not null
        and su.dept_name is not null
        <if test="record.isAll==null or !record.isAll">
            <if test="record.deptIds!=null and record.deptIds.size()>0">
                and su.dept_id in
                <foreach item="item" collection="record.deptIds" separator="," open="(" close=")" index="">
                #{item}
                </foreach>
            </if>
        </if>
        <if test="record.startDate != null ">
            and bip.approve_time >= #{record.startDate}
        </if>
        <if test="record.endDate != null ">
            and bip.approve_time <![CDATA[
            <=
            ]]> #{record.endDate}
        </if>
        group by
        su.dept_name,
        su.dept_id
        ) t
        left join (
        select
        count(user_id) user_count,
        dept_id
        from
        sys_user
        group by
        dept_id
        )a on
        t.dept_id = a.dept_id


    </select>
    <select id="completePerPerson"
            resultType="com.th.cms.modular.userindex.dataobject.CompletePerPersonDO">
        select
        *
        from
        (
        select
        su.dept_name,
        su.dept_id,
        <if test="record.queryType==1">
            date_format(wp.update_time, '%Y-%m') update_time,
        </if>
        <if test="record.queryType==2">
            date_format(wp.update_time, '%Y-%m-%d') update_time,
        </if>
        count(wp.id) cn
        from
        wf_influcer_submit_approve wp
        left join sys_user su on
        wp.create_id = su.user_id
        where
        su.dept_name is not null and wp.complete=1
        <if test="record.isAll==null or !record.isAll">
            <if test="record.deptIds!=null and record.deptIds.size()>0">
                and su.dept_id in
                <foreach item="item" collection="record.deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="record.startDate != null ">
            and wp.update_time >= #{record.startDate}
        </if>
        <if test="record.endDate != null">
            and wp.update_time <![CDATA[
            <=
            ]]> #{record.endDate}
        </if>
        group by
        su.dept_name,
        su.dept_id
        <if test="record.queryType==1">
            ,date_format(wp.update_time, '%Y-%m')
        </if>
        <if test="record.queryType==2">
            ,date_format(wp.update_time, '%Y-%m-%d')
        </if>
        ) t
        left join (
        select
        count(user_id) user_count,
        dept_id
        from
        sys_user
        group by
        dept_id
        )a on
        t.dept_id = a.dept_id
    </select>
    <select id="queryTodayAudit" resultType="java.lang.Long">
        select count(1)cn  from wf_approval_record   wf
        where approver_id =#{userId}
        and approval_status in(3,4)
        and update_time >= CURDATE()
        AND update_time <![CDATA[
            <
        ]]> CURDATE() + INTERVAL 1 DAY
        group by wf.approver_id

    </select>
    <select id="rankList" resultType="com.th.cms.modular.userindex.dto.BusinessRankDTO">
        select
        syu.user_id,syu.name userName,syu.dept_id,syu.dept_name,a.cn submit_count,syu.avatar
        from
        (
        select
        bcu.create_id ,
        count(distinct bcu.influcer_id) cn
        from
        wf_influcer_submit_approve   bcu
        where
        create_id is not null
        <if test="record.startDate != null ">
            and bcu.update_time >= #{record.startDate}
        </if>
        <if test="record.endDate != null ">
            and bcu.update_time <![CDATA[
            <=
            ]]> #{record.endDate}
        </if>


        group by
        bcu.create_id)a
        left join sys_user syu on a.create_id = syu.user_id
        order by a.cn desc


    </select>
    <select id="queryWaitAudit" resultType="java.lang.Long">
        select count(1)cn  from wf_approval_record   wf
        where approver_id =#{userId}
        and approval_status =2
        group by wf.approver_id
    </select>
</mapper>