package com.th.cms.modular.system.service;

import com.th.cms.modular.system.model.BizLog;
import com.th.cms.modular.system.dao.BizLogMapper;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.system.model.reqparam.BizLogListParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Service;
/**
 * <p>
 * 导游车 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizLogService extends ServiceImpl<BizLogMapper, BizLog> implements IService<BizLog> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizLogListParam param) {
        QueryWrapper<BizLog> objectQueryWrapper = new QueryWrapper<>();
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);
        return layuiPageInfo;

    }
}
