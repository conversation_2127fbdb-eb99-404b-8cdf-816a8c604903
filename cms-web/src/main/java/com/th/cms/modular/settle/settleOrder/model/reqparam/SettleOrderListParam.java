package com.th.cms.modular.settle.settleOrder.model.reqparam;

import cn.stylefeng.roses.kernel.model.validator.BaseValidatingParam;
import lombok.Data;

import java.io.Serializable;


/**
 * <p>
 * 达人结算订单表 列表查询实体
 * </p>
 */
@Data
public class SettleOrderListParam  implements Serializable, BaseValidatingParam  {
     private static final long serialVersionUID = 1L;

     /**
      * 项目简称
      */
     private String projectName;

     /**
      * 项目负责人
      */
     private String businessContact;

     /**
      * 项目类型
      */
     private String projectTypeName;

     /**
      * 分成比例
      */
     private String commissionRate;

     /**
      * 营收金额范围
      */
     private String settleStatus;

     /**
      * 一级平台
      */
     private String platform1;

     /**
      * 二级平台
      */
     private String platform2;

     /**
      * 三级平台
      */
     private String platform3;

     @Override
     public String checkParam() {
         return "";
     }
}
