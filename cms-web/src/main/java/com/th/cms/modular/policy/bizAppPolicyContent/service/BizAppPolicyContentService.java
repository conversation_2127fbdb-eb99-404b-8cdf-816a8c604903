package com.th.cms.modular.policy.bizAppPolicyContent.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.th.cms.modular.policy.bizAppPolicyContent.model.BizAppPolicyContent;
import com.th.cms.modular.policy.bizAppPolicyContent.dao.BizAppPolicyContentMapper;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.policy.bizAppPolicyContent.model.enums.DELFLAG;
import com.th.cms.modular.policy.bizAppPolicyContent.model.reqparam.BizAppPolicyContentListParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizAppPolicyContentService extends ServiceImpl<BizAppPolicyContentMapper, BizAppPolicyContent> implements IService<BizAppPolicyContent> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizAppPolicyContentListParam param) {
        QueryWrapper<BizAppPolicyContent> objectQueryWrapper = new QueryWrapper<>();

        String condition = param.getCondition();

        LambdaQueryWrapper<BizAppPolicyContent> lambda = objectQueryWrapper.lambda();

        if (!StringUtils.isEmpty(condition)) {

            lambda.and(la -> la
                    .like(BizAppPolicyContent::getPolicyName,"%"+condition+"%")
                    .or()
                    .like(BizAppPolicyContent::getPlatformName,"%"+condition+"%")
                    .or()
                    .like(BizAppPolicyContent::getTitle,"%"+condition+"%")
            );
        }
        lambda.orderByAsc(BizAppPolicyContent::getSortNum);

        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this, objectQueryWrapper);
        return layuiPageInfo;

    }
}
