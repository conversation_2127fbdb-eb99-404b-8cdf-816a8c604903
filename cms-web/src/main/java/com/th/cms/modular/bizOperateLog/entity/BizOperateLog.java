package com.th.cms.modular.bizOperateLog.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizOperateLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级菜单
     */
    private String menu1;

    /**
     * 二级菜单
     */
    private String menu2;

    /**
     * 三级菜单
     */
    private String menu3;

    /**
     * 一级功能操作
     */
    private String operation1;

    /**
     * 二级功能操作
     */
    private String operation2;

    /**
     * 三级功能操作
     */
    private String operation3;

    /**
     * 四级功能操作
     */
    private String operation4;

    /**
     * 操作人id
     */
    private Long staffId;

    /**
     * 操作人名称
     */
    private String staffName;

    /**
     * 操作人登录账号
     */
    private String staffLoginName;

    /**
     * 模块名称
     */
    private String module;

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 操作类型名称
     */
    private String operationTypeName;

    /**
     * 操作对象
     */
    private Integer targetType;

    /**
     * 操作对象名称
     */
    private String targetName;

    /**
     * 操作详情
     */
    private String operationDetail;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作者ip
     */
    private String ip;

    /**
     * 浏览器及版本
     */
    private String ext;

}
