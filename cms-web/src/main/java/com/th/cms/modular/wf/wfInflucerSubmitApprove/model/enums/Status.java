package com.th.cms.modular.wf.wfInflucerSubmitApprove.model.enums;

public enum Status {
    /**
     * 1: 启用
     */
    ENABLE("ENABLE", "启用"),
    /**
     * 1: 禁用
     */
    DISABLE("DISABLE", "禁用"),
    ;

    public String value;
    public String name;

    Status(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static Status getType(String value) {
        Status[] statusList = Status.values();
        for (Status status : statusList) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return null;
    }
}
