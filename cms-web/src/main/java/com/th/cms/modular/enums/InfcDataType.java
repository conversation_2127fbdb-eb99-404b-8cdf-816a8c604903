package com.th.cms.modular.enums;

public enum InfcDataType {

    /**
     * 草稿（用户可编辑未提交的申请）
     */
    TM(1, "timer"),
    UC(2, "usercreate"),

    ;
    /**
     * 状态码（持久化到数据库的值）
     */
    private final Integer code;

    /**
     * 状态描述（前端展示用）
     */
    private final String name;

    InfcDataType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态码
     * @return 对应的枚举实例，未找到时返回null
     */
    public static InfcDataType fromCode(Integer code) {
        for (InfcDataType status : InfcDataType.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

}
