package com.th.cms.modular.userindex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.th.cms.modular.userindex.dataobject.*;
import com.th.cms.modular.userindex.dto.BusinessRankDTO;
import com.th.cms.util.UserDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserIndexMapper extends BaseMapper {
    List<AuthenticationStatusPerPersonDO> authenticationStatusPerPerson(@Param("record") AuthenticationStatusPerPersonRequestDO requestDTO);

    List<CompletePerPersonDO> completePerPerson(@Param("record") CompletePerPersonRequestDO requestDTO);


    Long queryTodayAudit(@Param("userId") Long userId);

    List<BusinessRankDTO> rankList(@Param("record") BusinessRankRequestDO requestDTO, @Param("user") UserDTO userNotNull);

    Long queryWaitAudit(@Param("userId") Long userId);
}
