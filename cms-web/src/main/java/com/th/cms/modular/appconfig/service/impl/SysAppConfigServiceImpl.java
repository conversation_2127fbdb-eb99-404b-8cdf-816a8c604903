package com.th.cms.modular.appconfig.service.impl;

import com.th.cms.modular.appconfig.entity.SysAppConfig;
import com.th.cms.modular.appconfig.mapper.SysAppConfigMapper;
import com.th.cms.modular.appconfig.service.ISysAppConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <p>
 * APP端我的配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
@Primary
public class SysAppConfigServiceImpl extends ServiceImpl<SysAppConfigMapper, SysAppConfig> implements ISysAppConfigService {

}
