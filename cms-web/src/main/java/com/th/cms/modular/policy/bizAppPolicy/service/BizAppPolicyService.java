package com.th.cms.modular.policy.bizAppPolicy.service;

import com.th.cms.modular.policy.bizAppPolicy.model.BizAppPolicy;
import com.th.cms.modular.policy.bizAppPolicy.dao.BizAppPolicyMapper;
import com.th.cms.core.util.ShiroUtils;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.modular.policy.bizAppPolicy.model.reqparam.BizAppPolicyListParam;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Service;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class BizAppPolicyService extends ServiceImpl<BizAppPolicyMapper, BizAppPolicy> implements IService<BizAppPolicy> {
    /**
     * 查询分页数据，Specification模式
     *
     * <AUTHOR>
     */
    public LayuiPageInfo findPageBySpec(BizAppPolicyListParam param) {
        QueryWrapper<BizAppPolicy> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.orderByAsc("sort_num");
        LayuiPageInfo layuiPageInfo = ShiroUtils.findPageBySpec(this,objectQueryWrapper);
        return layuiPageInfo;

    }
}
