package com.th.cms.modular.settle.settleProjects.convert;

import cn.stylefeng.roses.kernel.model.enums.YesOrNotEnum;
import com.google.common.collect.Lists;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.core.util.OptionalUtil;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjectsConfigOption;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjectsField;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjectsFieldOption;
import com.th.cms.modular.settle.settleProjects.model.ProjectDetailDTO;
import com.th.cms.modular.settle.settleProjects.model.reqparam.SettleProjectAddRequestDTO;
import com.th.cms.modular.settle.settleProjects.model.reqparam.SettleProjectFieldOptionRequestDTO;
import com.th.cms.modular.settle.settleProjects.model.reqparam.SettleProjectFieldRequestDTO;

import java.util.Date;
import java.util.List;
import java.util.function.Function;

public class ProjectConvert {
    public static SettleProjects convertSettleProjects(SettleProjectAddRequestDTO requestDTO) {
        SettleProjects settleProjects = new SettleProjects();
        settleProjects.setId(requestDTO.getId());
        settleProjects.setSerialNumber(requestDTO.getSerialNumber());
        settleProjects.setProjectName(requestDTO.getProjectName());
        settleProjects.setProjectCode(requestDTO.getProjectCode());
        settleProjects.setProjectTypeId(requestDTO.getProjectTypeId());
        settleProjects.setProjectTypeName(requestDTO.getProjectTypeName());
        settleProjects.setCommissionRate(requestDTO.getCommissionRate());
        settleProjects.setCommissionRateId(requestDTO.getCommissionRateId());
        settleProjects.setCommissionToDaren(requestDTO.getCommissionToDaren());
        settleProjects.setCommissionToComp(requestDTO.getCommissionToComp());
        settleProjects.setRevenueType(requestDTO.getRevenueType());
        settleProjects.setRevenueTypeName(requestDTO.getRevenueTypeName());
        settleProjects.setPlatLstName(requestDTO.getPlatLstName());
        settleProjects.setFlowId(requestDTO.getFlowId());
        settleProjects.setPlatformId(requestDTO.getPlatformId());
        settleProjects.setPlatform(requestDTO.getPlatform());
        settleProjects.setYijiPlatform(requestDTO.getYijiPlatform());
        settleProjects.setErjiPlatform(requestDTO.getErjiPlatform());
        settleProjects.setSanjiPlatform(requestDTO.getSanjiPlatform());
        settleProjects.setQuantTarget(requestDTO.getQuantTarget());
        settleProjects.setSettlementCycle(requestDTO.getSettlementCycle());
        settleProjects.setSettlementCycleName(requestDTO.getSettlementCycleName());
        settleProjects.setStartDate(requestDTO.getStartDate());
        settleProjects.setProjectStarttime(requestDTO.getProjectStarttime());
        settleProjects.setProjectEndtime(requestDTO.getProjectEndtime());
        settleProjects.setAgencyRevenue(requestDTO.getAgencyRevenue());
        settleProjects.setBusinessOwner(requestDTO.getBusinessOwner());
        settleProjects.setBusinessOwnerName(requestDTO.getBusinessOwnerName());
        settleProjects.setCreatedBy(requestDTO.getCreatedBy());
        settleProjects.setProjectStatus(requestDTO.getProjectStatus());
        settleProjects.setProjectStatusName(requestDTO.getProjectStatusName());
        settleProjects.setOperations(requestDTO.getOperations());
        settleProjects.setProjectDesc(requestDTO.getProjectDesc());
        settleProjects.setBeijingFile(requestDTO.getBeijingFile());
        settleProjects.setProjectZhixingFa(requestDTO.getProjectZhixingFa());
        settleProjects.setZhixingFiles(requestDTO.getZhixingFiles());
        settleProjects.setJigouName(requestDTO.getJigouName());
        settleProjects.setCompName(requestDTO.getCompName());
        settleProjects.setCompNameId(requestDTO.getCompNameId());
        settleProjects.setCreateTime(requestDTO.getCreateTime());
        settleProjects.setUpdateTime(requestDTO.getUpdateTime());
        settleProjects.setCreateId(requestDTO.getCreateId());
        settleProjects.setCreateName(requestDTO.getCreateName());
        settleProjects.setDeptId(requestDTO.getDeptId());
        settleProjects.setDeptName(requestDTO.getDeptName());
        settleProjects.setCompanyId(requestDTO.getCompanyId());
        settleProjects.setCompanyName(requestDTO.getCompanyName());
        return settleProjects;
    }

    public static SettleProjectsField convertProjectFiled(SettleProjectFieldRequestDTO fieldRequestDTO, ShiroUser user, Integer projectId) {
        SettleProjectsField settleProjectsField = new SettleProjectsField();
        settleProjectsField.setId(fieldRequestDTO.getId());
        settleProjectsField.setProjectId(projectId);
        settleProjectsField.setConfigId(fieldRequestDTO.getConfigId());
        settleProjectsField.setFieldName(fieldRequestDTO.getFieldName());
        settleProjectsField.setFieldType(fieldRequestDTO.getFieldType());
        settleProjectsField.setFieldRemark(fieldRequestDTO.getFieldRemark());
        settleProjectsField.setFieldValue(fieldRequestDTO.getFieldValue());
        settleProjectsField.setOrderWith(fieldRequestDTO.getOrderWith());
        settleProjectsField.setStatus(YesOrNotEnum.Y.getCode());
        settleProjectsField.setCreateId(user.getId());
        settleProjectsField.setCreateName(user.getName());
        settleProjectsField.setCreateTime(new Date());
        settleProjectsField.setUpdateId(user.getId());
        settleProjectsField.setUpdateName(user.getName());
        settleProjectsField.setUpdateTime(new Date());
        return settleProjectsField;
    }

    public static ProjectDetailDTO convertDetail(SettleProjects detail, List<SettleProjectsField> settleProjectsFields, Function<SettleProjectsField,List<SettleProjectsFieldOption>>function) {
        ProjectDetailDTO projectDetailDTO = new ProjectDetailDTO();
        projectDetailDTO.setId(detail.getId());
        projectDetailDTO.setSerialNumber(detail.getSerialNumber());
        projectDetailDTO.setProjectName(detail.getProjectName());
        projectDetailDTO.setProjectCode(detail.getProjectCode());
        projectDetailDTO.setProjectTypeId(detail.getProjectTypeId());
        projectDetailDTO.setProjectTypeName(detail.getProjectTypeName());
        projectDetailDTO.setCommissionRate(detail.getCommissionRate());
        projectDetailDTO.setCommissionRateId(detail.getCommissionRateId());
        projectDetailDTO.setCommissionToDaren(detail.getCommissionToDaren());
        projectDetailDTO.setCommissionToComp(detail.getCommissionToComp());
        projectDetailDTO.setRevenueType(detail.getRevenueType());
        projectDetailDTO.setRevenueTypeName(detail.getRevenueTypeName());
        projectDetailDTO.setPlatLstName(detail.getPlatLstName());
        projectDetailDTO.setFlowId(detail.getFlowId());
        projectDetailDTO.setPlatformId(detail.getPlatformId());
        projectDetailDTO.setPlatform(detail.getPlatform());
        projectDetailDTO.setYijiPlatform(detail.getYijiPlatform());
        projectDetailDTO.setErjiPlatform(detail.getErjiPlatform());
        projectDetailDTO.setSanjiPlatform(detail.getSanjiPlatform());
        projectDetailDTO.setQuantTarget(detail.getQuantTarget());
        projectDetailDTO.setSettlementCycle(detail.getSettlementCycle());
        projectDetailDTO.setSettlementCycleName(detail.getSettlementCycleName());
        projectDetailDTO.setStartDate(detail.getStartDate());
        projectDetailDTO.setProjectStarttime(detail.getProjectStarttime());
        projectDetailDTO.setProjectEndtime(detail.getProjectEndtime());
        projectDetailDTO.setAgencyRevenue(detail.getAgencyRevenue());
        projectDetailDTO.setBusinessOwner(detail.getBusinessOwner());
        projectDetailDTO.setBusinessOwnerName(detail.getBusinessOwnerName());
        projectDetailDTO.setCreatedBy(detail.getCreatedBy());
        projectDetailDTO.setProjectStatus(detail.getProjectStatus());
        projectDetailDTO.setProjectStatusName(detail.getProjectStatusName());
        projectDetailDTO.setOperations(detail.getOperations());
        projectDetailDTO.setProjectDesc(detail.getProjectDesc());
        projectDetailDTO.setBeijingFile(detail.getBeijingFile());
        projectDetailDTO.setProjectZhixingFa(detail.getProjectZhixingFa());
        projectDetailDTO.setZhixingFiles(detail.getZhixingFiles());
        projectDetailDTO.setJigouName(detail.getJigouName());
        projectDetailDTO.setCompName(detail.getCompName());
        projectDetailDTO.setCompNameId(detail.getCompNameId());
        projectDetailDTO.setCreateTime(detail.getCreateTime());
        projectDetailDTO.setUpdateTime(detail.getUpdateTime());
        projectDetailDTO.setCreateId(detail.getCreateId());
        projectDetailDTO.setCreateName(detail.getCreateName());
        projectDetailDTO.setDeptId(detail.getDeptId());
        projectDetailDTO.setDeptName(detail.getDeptName());
        projectDetailDTO.setCompanyId(detail.getCompanyId());
        projectDetailDTO.setCompanyName(detail.getCompanyName());
        projectDetailDTO.setProjectStatusInt(detail.getProjectStatus());
        projectDetailDTO.setFieldRequest(convertDetailProjectFiled(settleProjectsFields,function));
        return projectDetailDTO;
    }

    private static List<SettleProjectFieldRequestDTO> convertDetailProjectFiled(List<SettleProjectsField> settleProjectsFields, Function<SettleProjectsField, List<SettleProjectsFieldOption>> function) {
        List<SettleProjectFieldRequestDTO> settleProjectFieldRequestDTOlist=Lists.newArrayList();
        for (SettleProjectsField settleProjectsField : OptionalUtil.defaultList(settleProjectsFields)) {
        	settleProjectFieldRequestDTOlist.add(convertFromSettleProjectsField(settleProjectsField,function));
        }
        return settleProjectFieldRequestDTOlist;

    }

    private static SettleProjectFieldRequestDTO convertFromSettleProjectsField(SettleProjectsField settleProjectsField, Function<SettleProjectsField, List<SettleProjectsFieldOption>> function) {
        SettleProjectFieldRequestDTO settleProjectFieldRequestDTO = new SettleProjectFieldRequestDTO();
        settleProjectFieldRequestDTO.setId(settleProjectsField.getId());
        settleProjectFieldRequestDTO.setFieldValue(settleProjectsField.getFieldValue());
        settleProjectFieldRequestDTO.setConfigId(settleProjectsField.getConfigId());
        settleProjectFieldRequestDTO.setFieldName(settleProjectsField.getFieldName());
        settleProjectFieldRequestDTO.setFieldRemark(settleProjectsField.getFieldRemark());
        settleProjectFieldRequestDTO.setFieldType(settleProjectsField.getFieldType());
        settleProjectFieldRequestDTO.setOrderWith(settleProjectsField.getOrderWith());
        settleProjectFieldRequestDTO.setOptions(convertProjectFiledOptionDTO(function.apply(settleProjectsField)));
        return settleProjectFieldRequestDTO;
    }

    private static List<SettleProjectFieldOptionRequestDTO> convertProjectFiledOptionDTO(List<SettleProjectsFieldOption> apply) {
        List<SettleProjectFieldOptionRequestDTO> settleProjectFieldOptionRequestDTOlist=Lists.newArrayList();
        for (SettleProjectsFieldOption settleProjectsFieldOption :OptionalUtil.defaultList(apply)) {
        	settleProjectFieldOptionRequestDTOlist.add(convertFromSettleProjectsFieldOption(settleProjectsFieldOption));
        }
        return settleProjectFieldOptionRequestDTOlist;
    }

    private static SettleProjectFieldOptionRequestDTO convertFromSettleProjectsFieldOption(SettleProjectsFieldOption settleProjectsFieldOption) {
        SettleProjectFieldOptionRequestDTO settleProjectFieldOptionRequestDTO = new SettleProjectFieldOptionRequestDTO();
        settleProjectFieldOptionRequestDTO.setId(settleProjectsFieldOption.getId());
        settleProjectFieldOptionRequestDTO.setOptionValue(settleProjectsFieldOption.getOptionValue());
        settleProjectFieldOptionRequestDTO.setOrderWith(settleProjectsFieldOption.getOrderWith());
        return settleProjectFieldOptionRequestDTO;
    }


    public static SettleProjectsFieldOption convertProjectFiledOption(SettleProjectFieldOptionRequestDTO option, ShiroUser user, SettleProjectsField settleProjectsField) {
        SettleProjectsFieldOption settleProjectsFieldOption = new SettleProjectsFieldOption();
        settleProjectsFieldOption.setId(option.getId());
        settleProjectsFieldOption.setProjectId(settleProjectsField.getProjectId());
        settleProjectsFieldOption.setConfigId(settleProjectsField.getConfigId());
        settleProjectsFieldOption.setOptionValue(option.getOptionValue());
        settleProjectsFieldOption.setOrderWith(option.getOrderWith());
        settleProjectsFieldOption.setStatus(YesOrNotEnum.Y.getCode());
        settleProjectsFieldOption.setCreateId(user.getId());
        settleProjectsFieldOption.setCreateName(user.getName());
        settleProjectsFieldOption.setCreateTime(new Date());
        settleProjectsFieldOption.setUpdateId(user.getId());
        settleProjectsFieldOption.setUpdateName(user.getName());
        settleProjectsFieldOption.setUpdateTime(new Date());
        settleProjectsFieldOption.setProjectFieldId(settleProjectsField.getId());
        return settleProjectsFieldOption;
    }

    public static List<SettleProjectsFieldOption> convertProjectFieldOptionFromConfig(List<SettleProjectsConfigOption> settleProjectsFieldOptions2, SettleProjectsField projectFiledId) {
        List<SettleProjectsFieldOption> settleProjectsFieldOptionlist=Lists.newArrayList();
        for (SettleProjectsConfigOption settleProjectsConfigOption :settleProjectsFieldOptions2) {
        	settleProjectsFieldOptionlist.add(convertFromSettleProjectsConfigOption(settleProjectsConfigOption,projectFiledId));
        }
        return settleProjectsFieldOptionlist;
    }

    private static SettleProjectsFieldOption convertFromSettleProjectsConfigOption(SettleProjectsConfigOption settleProjectsConfigOption, SettleProjectsField projectFiledId) {
        SettleProjectsFieldOption settleProjectsFieldOption = new SettleProjectsFieldOption();
        settleProjectsFieldOption.setProjectId(projectFiledId.getProjectId());
        settleProjectsFieldOption.setConfigId(settleProjectsConfigOption.getConfigId());
        settleProjectsFieldOption.setOptionValue(settleProjectsConfigOption.getOptionValue());
        settleProjectsFieldOption.setOrderWith(settleProjectsConfigOption.getOrderWith());
        settleProjectsFieldOption.setStatus(settleProjectsConfigOption.getStatus());
        settleProjectsFieldOption.setCreateId(settleProjectsConfigOption.getCreateId());
        settleProjectsFieldOption.setCreateName(settleProjectsConfigOption.getCreateName());
        settleProjectsFieldOption.setCreateTime(settleProjectsConfigOption.getCreateTime());
        settleProjectsFieldOption.setUpdateId(settleProjectsConfigOption.getUpdateId());
        settleProjectsFieldOption.setUpdateName(settleProjectsConfigOption.getUpdateName());
        settleProjectsFieldOption.setUpdateTime(settleProjectsConfigOption.getUpdateTime());
        settleProjectsFieldOption.setProjectFieldId(projectFiledId.getId());
        return settleProjectsFieldOption;


    }
}
