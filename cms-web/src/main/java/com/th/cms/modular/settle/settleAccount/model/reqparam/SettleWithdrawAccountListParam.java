package com.th.cms.modular.settle.settleAccount.model.reqparam;

import cn.stylefeng.roses.kernel.model.validator.BaseValidatingParam;
import lombok.Data;

import java.io.Serializable;


/**
 * <p>
 * 达人结算订单表 列表查询实体
 * </p>
 */
@Data
public class SettleWithdrawAccountListParam  implements Serializable, BaseValidatingParam  {
     private static final long serialVersionUID = 1L;

    /**
     * 达人id
     */
    private String userId;

    /**
     * 达人昵称
     */
    private String nickName;

    /**
     * 支付宝账号
     */
    private String zhifuName;

    /**
     * 达人手机号
     * @return
     */
    private String userTel;
     @Override
     public String checkParam() {
         return "";
     }
}
