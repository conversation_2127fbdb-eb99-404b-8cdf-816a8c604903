<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.th.cms.modular.bizPlatform.mapper.BizPlatformAuthAppConfigMapper">

    <select id="listPage" resultType="com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformAuthDTO">
        select
        bpac.id,
        bpa.id auth_plat_id,
        bpa.plat_name auth_plat_name,
        bpa.icon_pic,
        bpac.auth_account,
        bpac.auth_content,
        bpac.status
        from biz_platform_auth bpa
        left join biz_platform_auth_app_config bpac on bpac.auth_plat_id =bpa.id
        where 1=1
        <if test="paramDTO.search != null and paramDTO.search != ''">
            and bpa.plat_name like concat('%',#{paramDTO.search},'%')
        </if>
        order by bpa.plat_name
    </select>
</mapper>
