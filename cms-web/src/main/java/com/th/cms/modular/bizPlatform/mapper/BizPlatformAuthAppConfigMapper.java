package com.th.cms.modular.bizPlatform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuthAppConfig;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformAuthDTO;
import com.th.cms.modular.influcer.cooperationPlatform.dto.AppPlatformRequestDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface BizPlatformAuthAppConfigMapper extends BaseMapper<BizPlatformAuthAppConfig> {

    Page<AppPlatformAuthDTO> listPage(@Param("objectPage") Page<Object> objectPage, @Param("paramDTO") AppPlatformRequestDTO paramDTO, @Param("userId") Long userId);
}
