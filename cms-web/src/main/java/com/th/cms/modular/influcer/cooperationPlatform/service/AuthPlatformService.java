package com.th.cms.modular.influcer.cooperationPlatform.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.stylefeng.roses.kernel.model.enums.YesOrNotEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.core.dto.FrontPageResult;
import com.th.cms.core.exceptions.BusiException;
import com.th.cms.core.util.Objects;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuth;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuthAppConfig;
import com.th.cms.modular.bizPlatform.entity.BizPlatformAuthAppConfigFieldRel;
import com.th.cms.modular.bizPlatform.mapper.BizPlatformAuthAppConfigFieldRelMapper;
import com.th.cms.modular.bizPlatform.mapper.BizPlatformAuthAppConfigMapper;
import com.th.cms.modular.bizPlatform.mapper.BizPlatformAuthMapper;
import com.th.cms.modular.influcer.cooperationPlatform.convert.AuthPlatformConvert;
import com.th.cms.modular.influcer.cooperationPlatform.dto.*;
import com.th.cms.util.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AuthPlatformService {
    @Autowired
    private BizPlatformAuthMapper bizPlatformAuthMapper;
    @Autowired
    private BizPlatformAuthAppConfigMapper bizPlatformAppConfigMapper;
    @Autowired
    private AuthPlatformConvert authPlatformConvert;
    @Autowired
    private BizPlatformAuthAppConfigMapper bizPlatformAuthAppConfigMapper;
    @Autowired
    private BizPlatformAuthAppConfigFieldRelMapper bizPlatformAuthAppConfigFieldRelMapper;


    /**
     * 认证平台列表
     */
    public FrontPageResult<BizPlatformAuth> basePlatformList(BasePlatformRequestDTO paramDTO, UserDTO userNotNull) {
        return FrontPageResult.convertPage(bizPlatformAuthMapper.basePlatformList(new Page<>(paramDTO.getPage(), paramDTO.getLimit()), paramDTO, userNotNull.getUserId()), t -> t);
    }

    /**
     * 新增认证平台
     */
    public Boolean saveOrUpdateBasePlatform(BasePlatformAddRequestDTO paramDTO, UserDTO userNotNull) {
        BizPlatformAuth record = AuthPlatformConvert.convertBizPlatformAuth(paramDTO, userNotNull);
        if (record.getId() == null) {
            bizPlatformAuthMapper.insert(record);

        } else {
            bizPlatformAuthMapper.updateById(record);
        }

        return true;
    }

    public Boolean toggleBasePlatform(Long platId, Integer status, UserDTO userNotNull) {
        if (platId == null) {
            throw new BusiException("ID不能为空");
        }
        BizPlatformAuth record = new BizPlatformAuth();
        record.setId(platId);
        record.setStatus(status);
        record.setUpdateTime(new Date());
        bizPlatformAuthMapper.updateById(record);
        return true;
    }

    public FrontPageResult<AppPlatformAuthDTO> list(AppPlatformRequestDTO paramDTO, UserDTO userNotNull) {
        Page<AppPlatformAuthDTO> page = bizPlatformAppConfigMapper.listPage(new Page<>(paramDTO.getPage(), paramDTO.getLimit()), paramDTO, userNotNull.getUserId());
        return FrontPageResult.convertPage(page, t->authPlatformConvert.convertAppAuthPlatformDTO(t));
    }

    public AppPlatformAuthDTO detail(Long id, UserDTO userNotNull) {
        if (id==null) {
            return blankDTO(userNotNull);
        }
        AppPlatformRequestDTO dto = new AppPlatformRequestDTO();
        dto.setId(id);
        FrontPageResult<AppPlatformAuthDTO> list = list(dto, userNotNull);
        if(list ==null|| CollectionUtil.isEmpty(list.getList())){
            return null;
        }
        AppPlatformAuthDTO bizPlatformAppConfig = list.getList().get(0);
        return authPlatformConvert.convertAppAuthPlatformDTO(bizPlatformAppConfig);
    }

    private AppPlatformAuthDTO blankDTO(UserDTO userNotNull) {
        AppPlatformAuthDTO appPlatformDTO = new AppPlatformAuthDTO();
        appPlatformDTO.setId(null);
        appPlatformDTO.setAuthPlatId(null);
        appPlatformDTO.setAuthAccount(null);
        appPlatformDTO.setAuthContent(null);
        appPlatformDTO.setStatus(null);
        appPlatformDTO.setFieldList(authPlatformConvert.fauthFieldList());
        return appPlatformDTO;


    }

    public Boolean saveOrUpdateAppPlatform(AppPlatformAddRequestDTO paramDTO, UserDTO userNotNull) {
        BizPlatformAuthAppConfig record = authPlatformConvert.convertBizPlatformAppConfig(paramDTO, userNotNull);
        if(record.getId()==null){
            bizPlatformAuthAppConfigMapper.insert(record);
        }else{
            bizPlatformAuthAppConfigMapper.updateById(record);
        }
        saveFileds(record,  paramDTO,userNotNull);
        return true;
    }

    private void saveFileds(BizPlatformAuthAppConfig record, AppPlatformAddRequestDTO paramDTO, UserDTO userNotNull) {
        List<AppPlatformAddRequestDTO.FieldDTO> collect = paramDTO.getFieldList().stream().filter(e -> Objects.equals(e.getUseField(), YesOrNotEnum.Y.getCode())).collect(Collectors.toList());
        bizPlatformAuthAppConfigFieldRelMapper.delete(new LambdaQueryWrapper<BizPlatformAuthAppConfigFieldRel>().eq(BizPlatformAuthAppConfigFieldRel::getAuthConfigId, record.getId()));
        for (AppPlatformAddRequestDTO.FieldDTO fieldDTO : collect) {
            bizPlatformAuthAppConfigFieldRelMapper.insert(AuthPlatformConvert.convertFieldRel(fieldDTO, record, userNotNull));

        }
    }
}
