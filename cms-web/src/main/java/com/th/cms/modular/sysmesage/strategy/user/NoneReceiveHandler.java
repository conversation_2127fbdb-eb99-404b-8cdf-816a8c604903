package com.th.cms.modular.sysmesage.strategy.user;

import com.th.cms.modular.sysmesage.entity.SysMessage;
import com.th.cms.modular.system.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
@Component
@Slf4j
public class NoneReceiveHandler implements IReceiverTypeHandler{
    /**
     * 获取需要发送的用户列表
     *
     * @param message     消息
     * @param currentUser 当前登录的用户
     * @return 用户列表
     */
    @Override
    public List<User> getReceiverUsers(SysMessage message, User currentUser) {
        return Collections.emptyList();
    }
}
