package com.th.cms.modular.influcer.bizPlatform.service;

import com.th.cms.modular.influcer.bizPlatform.entity.BizPlatform;
import com.th.cms.modular.influcer.bizPlatform.model.PlatformTreeVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TreeBuilder {
    /**
     * 将平面列表转换为树形结构
     * @param entities 原始数据列表
     * @param rootParentId 根节点的parentId值（根据业务定义，如0或null）
     */
    public static List<PlatformTreeVo> buildTree(List<BizPlatform> entities, Integer rootParentId, String selectPid) {
        // 先将Platform转成TreeVo
        List<PlatformTreeVo> nodes = entities.stream()
                .map(PlatformTreeVo::new)
                .collect(Collectors.toList());

        Map<Integer, PlatformTreeVo> nodeMap = new HashMap<>();
        
        // 第一遍遍历：建立ID到节点的映射
        nodes.forEach(node -> nodeMap.put(node.getId(), node));
        
        // 第二遍遍历：构建父子关系
        List<PlatformTreeVo> roots = new ArrayList<>();
        nodes.forEach(node -> {
            Integer parentId = node.getParentId();
            if (rootParentId.equals(parentId)) {
                roots.add(node);
            } else {
                PlatformTreeVo parent = nodeMap.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(node);
                }
            }

            if(node.getId().equals(Integer.parseInt(selectPid))){
                node.setChecked(true);
                node.setOpen(true);
            }
        });
        
        return roots;
    }
}
