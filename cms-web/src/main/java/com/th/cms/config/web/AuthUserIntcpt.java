package com.th.cms.config.web;

import com.th.cms.modular.customer.bizCustomerIm.model.BizCustomerIm;
import com.th.cms.modular.customer.bizCustomerIm.service.BizCustomerImService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024年11月11日 10:16
 */
@Component
@Slf4j
public class AuthUserIntcpt implements HandlerInterceptor {

    @Resource
    private BizCustomerImService bizCustomerImService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
        // 放行特定接口
        try {

            if (request.getMethod().equalsIgnoreCase("OPTIONS")) {
                return true;
            }

            if (!request.getRequestURI().contains("/cms/")){
                return true;
            }

            Object imToken = request.getHeader("AuthToken");

            if (Objects.isNull(imToken)){
                imToken = request.getHeader("authToken");
            }

            if (Objects.isNull(imToken)) {

                throw new SecurityException("无效的authToken");
            }
            BizCustomerIm bizCustomerIm = bizCustomerImService.getByByImToken(imToken.toString());
            if (bizCustomerIm == null) {
                return false;
            }
//            ThreadContext.put("userId", String.valueOf(bizCustomerIm.getUserId()));
//            request.setAttribute("userId", String.valueOf(bizCustomerIm.getUserId()));
            UserContextHolder.setUserId(bizCustomerIm.getUserId());
            return true;

        } catch (Exception e) {
            log.error("", e);
        }
        return false;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserContextHolder.clear();
    }
}
