package com.th.cms.config.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.th.cms.core.auth.AuthInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
@MapperScan(basePackages = {"com.th.cms.modular.*"}, sqlSessionFactoryRef = "mingyuehaoSqlSessionFactory")
public class MingyuehaoDataSourceConfig {

    @Value("${spring.datasource.mingyuehao.username}")
    private String userName;

    @Value("${spring.datasource.mingyuehao.password}")
    private String passWord;

    @Value("${spring.datasource.mingyuehao.jdbc-url}")
    private String url;

    @Value("${spring.datasource.driverClass}")
    private String driverClass;

    @Value("${spring.datasource.maxActive}")
    private Integer maxActive;

    @Value("${spring.datasource.maxWait}")
    private Integer maxWait;

    @Value("${spring.datasource.initialSize}")
    private Integer initialSize;

    @Value("${spring.datasource.minIdle}")
    private Integer minIdle;

    @Value("${spring.datasource.time-between-eviction-runs-millis}")
    private Long timeBetween;

    @Value("${spring.datasource.min-evictable-idle-time-millis}")
    private Long minEvictableIdle;

    @Value("${spring.datasource.validation-query}")
    private String validationQuery;

    @Value("${spring.datasource.keep-alive}")
    private Boolean keepAlive;

    @Value("${spring.datasource.remove-abandoned}")
    private Boolean removeAbandoned;

    @Value("${spring.datasource.remove-abandoned-time}")
    private Integer removeAbandonedTime;

    @Value("${spring.datasource.test-on-borrow}")
    private Boolean testOnBorrow;

    @Value("${spring.datasource.test-on-return}")
    private Boolean testOnReturn;

    @Value("${spring.datasource.test-while-idle}")
    private Boolean testWhileIdle;
    @Autowired
    private MybatisPlusProperties mybatisPlusProperties;

    @Bean(name = "datasourcemingyuehao")
    @Primary
    public DataSource datasourcemingyuehao() throws SQLException {
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setUsername(userName);
        druidDataSource.setPassword(passWord);
        druidDataSource.setUrl(url);
        druidDataSource.setDriverClassName(driverClass);
        druidDataSource.setMaxActive(maxActive);
        // 配置从连接池获取连接等待超时的时间
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMinIdle(minIdle);
        // 配置间隔多久启动一次DestroyThread，对连接池内的连接才进行一次检测，单位是毫秒。
        // 检测时:1.如果连接空闲并且超过minIdle以外的连接，如果空闲时间超过minEvictableIdleTimeMillis设置的值则直接物理关闭。2.在minIdle以内的不处理。
        druidDataSource.setTimeBetweenEvictionRunsMillis(timeBetween);
        // 配置一个连接在池中最大空闲时间，单位是毫秒
        druidDataSource.setMinEvictableIdleTimeMillis(minEvictableIdle);
        // 设置从连接池获取连接时是否检查连接有效性，true时，每次都检查;false时，不检查
        druidDataSource.setTestOnBorrow(testOnBorrow);
        // 设置往连接池归还连接时是否检查连接有效性，true时，每次都检查;false时，不检查
        druidDataSource.setTestOnReturn(testOnReturn);
        // 设置从连接池获取连接时是否检查连接有效性，true时，如果连接空闲时间超过minEvictableIdleTimeMillis进行检查，否则不检查;false时，不检查
        druidDataSource.setTestWhileIdle(testWhileIdle);
        // 检验连接是否有效的查询语句。如果数据库Driver支持ping()方法，则优先使用ping()方法进行检查，否则使用validationQuery查询进行检查。(Oracle jdbc Driver目前不支持ping方法)
        druidDataSource.setValidationQuery(validationQuery);
        // 打开后，增强timeBetweenEvictionRunsMillis的周期性连接检查，minIdle内的空闲连接，每次检查强制验证连接有效性. 参考：https://github.com/alibaba/druid/wiki/KeepAlive_cn
        druidDataSource.setKeepAlive(keepAlive);
        // 连接泄露检查，打开removeAbandoned功能 , 连接从连接池借出后，长时间不归还，将触发强制回连接。回收周期随timeBetweenEvictionRunsMillis进行，如果连接为从连接池借出状态，并且未执行任何sql，并且从借出时间起已超过removeAbandonedTimeout时间，则强制归还连接到连接池中。
        druidDataSource.setRemoveAbandoned(removeAbandoned);
        // 超时时间，秒
        druidDataSource.setRemoveAbandonedTimeout(removeAbandonedTime);
        druidDataSource.init();
        return druidDataSource;
    }

    @Bean(name = "mingyuehaoSqlSessionFactory")
    @ConditionalOnBean(name = "datasourcemingyuehao")
    @Primary
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourcemingyuehao") DataSource dataSource,
                                                   ResourcePatternResolver resourcePatternResolver) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        // 自动加载MyBatis-Plus的配置
        mybatisSqlSessionFactoryBean.setGlobalConfig(mybatisPlusProperties.getGlobalConfig());

        mybatisSqlSessionFactoryBean.setMapperLocations(configResources(mybatisPlusProperties,resourcePatternResolver));
        // 设置MyBatis的基础配置
        MybatisConfiguration configuration = mybatisPlusProperties.getConfiguration();
        if (configuration != null) {
            mybatisSqlSessionFactoryBean.setConfiguration(configuration);
        }
        PaginationInterceptor interceptor = new PaginationInterceptor();
        interceptor.setDialectType(DbType.MYSQL.getDb());
        AuthInterceptor authInterceptor = new AuthInterceptor();
        mybatisSqlSessionFactoryBean.setPlugins(new Interceptor[]{authInterceptor, interceptor});
        return mybatisSqlSessionFactoryBean.getObject();
    }

    private Resource[] configResources(MybatisPlusProperties mybatisPlusProperties,
                                       ResourcePatternResolver resourcePatternResolver) throws IOException {
        // 假设你已经从配置中获取了这个 String[]
        String[] mapperLocationPatterns = mybatisPlusProperties.getMapperLocations(); // 如：["classpath*:mapper/**/*.xml"]

        // 将 String[] 转换为 Resource[]
        List<Resource> resources = new ArrayList<>();
        for (String pattern : mapperLocationPatterns) {
            Resource[] matchedResources = resourcePatternResolver.getResources(pattern);
            resources.addAll(Arrays.asList(matchedResources));
        }

       return resources.toArray(new Resource[0]);
    }

    @Bean(name = "mingyuehaoPlatformTransactionManager")
    public PlatformTransactionManager transactionManager(@Qualifier("datasourcemingyuehao") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
