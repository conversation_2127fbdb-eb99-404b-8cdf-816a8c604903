package com.th.cms.config.web;

import cn.hutool.http.HttpStatus;
import cn.stylefeng.roses.core.reqres.response.ResponseData;
import com.alibaba.fastjson.JSON;
import com.th.cms.core.util.JedisClient;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年5月25日
 */
@Component
@Slf4j
public class UserIntercept implements HandlerInterceptor {

    @Resource
    private UserService userService;
    @Resource
    private JedisClient jedisClient;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
        // 放行特定接口
        try {

            if (request.getMethod().equalsIgnoreCase("OPTIONS")) {
                return true;
            }

            //需要拦截的请求
            if (!request.getRequestURI().contains("/user/wfType/") &&
                    !request.getRequestURI().contains("/user/wfStep/") &&
                    !request.getRequestURI().contains("/user/dept/treeview") &&
                    !request.getRequestURI().contains("/user/role/treeview") &&
                    !request.getRequestURI().contains("/user/bizClueReceive") &&
                    !request.getRequestURI().contains("/user/bizClueUpload") &&
                    !request.getRequestURI().contains("/user/bizCategoryXd") &&
                    !request.getRequestURI().contains("/user/userBizPlatform") &&
                    !request.getRequestURI().contains("/user/myInflucer") &&
                    !request.getRequestURI().contains("/user/userList") &&
                    !request.getRequestURI().contains("/user/detail") &&
                    !request.getRequestURI().contains("/user/submitApprove/")&&
                    !request.getRequestURI().contains("/wechat-contact/audit-list")&&
                    !request.getRequestURI().contains("/wechat-contact/detail/*")&&
                    !request.getRequestURI().contains("/wechat-contact/add")&&
                    !request.getRequestURI().contains("/wechat-contact/task-receive")&&
                    !request.getRequestURI().contains("/wechat-contact/task-recovery")&&
                    !request.getRequestURI().contains("/user/wechat-contact/audit")&&
                    !request.getRequestURI().contains("/user/mid-station/getToken")&&
                    !request.getRequestURI().contains("/user/mid-station/getClueId")&&
                    !request.getRequestURI().contains("/user/bizInflucer/auth")
            ) {
                return true;
            }

            Object imToken = request.getHeader("AuthToken");

            if (Objects.isNull(imToken)) {
                imToken = request.getHeader("authToken");
            }

            if (Objects.isNull(imToken)) {
                writeUnAuth(response);
                return false;
            }

            String userId = jedisClient.get(imToken.toString());
            User user = userService.getById(userId);

            if (user == null) {
                writeUnAuth(response);
                return false;
            }
            UserContextHolder.setUserId(user.getUserId());
            UserContextHolder.setUserName(user.getName());
            return true;

        } catch (Exception e) {
            log.error("", e);
        }
        return false;
    }

    public void writeUnAuth(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.HTTP_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JSON.toJSONString(ResponseData.error("无效的authToken")));
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserContextHolder.clear();
    }
}
