package com.th.cms.timer;

import com.th.cms.core.common.redis.RedisDistributedLock;
import com.th.cms.core.util.JedisClient;
import com.th.cms.core.util.ProductConst;
import com.th.cms.modular.enums.InfcTopType;
import com.th.cms.modular.settle.settleAccount.service.BizInflucerTopExtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class InflucerTopTimer {
    @Autowired
    private BizInflucerTopExtService bizInflucerTopService;
    @Autowired
    private RedisDistributedLock redisDistributedLock;
    @Autowired
    private JedisClient client;


    /**
     * 定时更新排行榜
     * 0点第一次，每隔2小时一次
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void initZeroRankList() {
        String key = "init_rank_list_zero";
        redisDistributedLock.execByLock(key, t -> {
            bizInflucerTopService.initRankList();
            return 1;
        });

    }

    /**
     * 定时更新排行榜
     * 0点第一次，每隔2小时一次
     */
    @Scheduled(cron = "0 0 0/2 * * ?")
    public void initRankList() {
        String key = "init_rank_list_zero";
        redisDistributedLock.execByLock(key, t -> {
            bizInflucerTopService.initRankList();
            return 1;
        });
    }

    public void clearRankKey() {
        String key = ProductConst.RANK_LIST_INIT_ING + "@" + InfcTopType.zuix.getCode();

        client.del(key);
        key = ProductConst.RANK_LIST_INIT_ING + "@" + InfcTopType.zongbang.getCode();

        client.del(key);

    }
}
