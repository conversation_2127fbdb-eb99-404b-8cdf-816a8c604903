package com.th.cms.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.th.cms.config.web.UserContextHolder;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.core.util.JedisClient;
import com.th.cms.core.util.OptionalUtil;
import com.th.cms.modular.system.entity.Role;
import com.th.cms.modular.system.entity.User;
import com.th.cms.modular.system.entity.UserRole;
import com.th.cms.modular.system.mapper.RoleMapper;
import com.th.cms.modular.system.mapper.UserMapper;
import com.th.cms.modular.system.mapper.UserRoleMapper;
import com.th.cms.modular.system.service.DeptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class UserUtil {
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private DeptService deptService;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private JedisClient jedisClient;

    /**
     * 当前登陆用户
     *
     * @return 用户
     */
    public UserDTO getUserNotNull(HttpServletRequest request) {
        UserDTO user1 = getUser(getToken(request));
        if (user1 == null) {
            throw new RuntimeException("用户未登录");
        }
        return user1;
    }

    private String getToken(HttpServletRequest request) {
        Object imToken = request.getHeader("AuthToken");

        if (Objects.isNull(imToken)) {
            imToken = request.getHeader("authToken");
        }
        return imToken==null?null:imToken.toString();
    }

    /**
     * 当前登陆用户
     *
     * @return 用户
     */
    public UserDTO getUserNotNull(String token) {
        UserDTO user1 = getUser(token);
        if (user1 == null) {
            throw new RuntimeException("用户未登录");
        }
        return user1;
    }

    /**
     * 当前登陆用户
     *
     * @return 用户
     */
    public UserDTO getUser(String token) {
        ShiroUser user = ShiroKit.getUser();
        Long userId = null;
        if (user == null) {
            userId = UserContextHolder.getUserId();
        } else {
            userId = user.getId();
        }
        if (userId == null) {
            if (StringUtils.isBlank(token)) {
                return null;
            }
            String userIdStr = jedisClient.get(token);
            if(StringUtils.isBlank(userIdStr)){
                return null;
            }
            userId = Long.parseLong(userIdStr);
        }
        User user1 = userMapper.selectById(userId);
        return convertUser(user1);
    }

    private UserDTO convertUser(User user1) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(user1.getUserId());
        userDTO.setAvatar(user1.getAvatar());
        userDTO.setAccount(user1.getAccount());
        userDTO.setPassword(user1.getPassword());
        userDTO.setSalt(user1.getSalt());
        userDTO.setName(user1.getName());
        userDTO.setBirthday(user1.getBirthday());
        userDTO.setSex(user1.getSex());
        userDTO.setEmail(user1.getEmail());
        userDTO.setPhone(user1.getPhone());
        userDTO.setRoleId(user1.getRoleId());
        userDTO.setDeptId(user1.getDeptId());
        userDTO.setDeptName(user1.getDeptName());
        List<Role> roleList = queryRoleList(user1.getUserId());
        userDTO.setRoleList(OptionalUtil.defaultList(roleList).stream().map(Role::getRoleId).collect(Collectors.toList()));
        userDTO.setRoleNames(OptionalUtil.defaultList(roleList).stream().map(Role::getName).collect(Collectors.toList()));
        userDTO.setLeaderId(user1.getLeaderId());
        return userDTO;
    }

    private List<Role> queryRoleList(Long userId) {
        LambdaQueryWrapper<UserRole> qw = Wrappers.lambdaQuery();
        qw.eq(UserRole::getUserId, userId);
        List<UserRole> userRoles = userRoleMapper.selectList(qw);
        return roleMapper.selectList(new LambdaQueryWrapper<Role>().in(Role::getRoleId, userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList())));
    }

}
