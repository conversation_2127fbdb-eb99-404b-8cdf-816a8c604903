package com.th.cms.util;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component("distributedLocker")
public class RedisDistributedLocker implements DistributedLocker {

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public boolean tryLock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        return lock.tryLock();
    }

    @Override
    public boolean tryLock(String lockKey, int leaseTime) throws InterruptedException {
        RLock lock = redissonClient.getLock(lockKey);
        return lock.tryLock(leaseTime, TimeUnit.SECONDS);
    }

    @Override
    public boolean tryLock(String lockKey, TimeUnit unit ,int timeout) throws InterruptedException {
        RLock lock = redissonClient.getLock(lockKey);
        return lock.tryLock(timeout, unit);
    }

    @Override
    public boolean tryLock(String lockKey, TimeUnit unit, int waitTime, int leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            return false;
        }
    }

    @Override
    public void unlock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean heldByThread = lock.isHeldByThread(Thread.currentThread().getId());
        if (heldByThread){
            lock.unlock();
        }
    }

    @Override
    public void unlock(RLock lock) {
        boolean heldByThread = lock.isHeldByThread(Thread.currentThread().getId());
        if (heldByThread) {
            lock.unlock();
        }
    }
}
