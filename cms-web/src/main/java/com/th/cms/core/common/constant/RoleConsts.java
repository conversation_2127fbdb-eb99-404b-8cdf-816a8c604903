package com.th.cms.core.common.constant;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class RoleConsts {
    public static final Long ROLE_BUSINESS = 1929741272342966274L;

    /**
     * 主管、总监、工区负责人、业务负责人、老板和其他人 返回成功
     *
     * 1904882446309621762	商务组长
     * 1904882622021599233	商务总监
     * 1904882835725582337	老板
     * 1925041551753687042	平台负责人
     * 1925094127157489666	客服
     * 1929726585207259138	客服主管
     * 1929726893555712002	财务主管
     * 1929727070031052801	区域负责人
     * 1929737350953938946	管理员
     * 1929737675886669825	出纳
     * 1929737779481784322	会计
     * 1929738451258290178	提审员
     * 1929739694638739458	质检组长
     * 1929739932447387650	审核员
     * 1929740474775089154	业务（项目）负责人
     * 1929741272342966274	个人
     * 1929742445338800129	商务主管
     * 1930095095600783361	技术
     * @return 角色列表
     */
    public List<Long> getUserIndexBarOrLineRoleIds() {
        List<Long> roleIds = Lists.newArrayList();
        roleIds.add(1904882622021599233L);
        roleIds.add(1904882835725582337L);
        roleIds.add(1925041551753687042L);
        roleIds.add(1925094127157489666L);
        roleIds.add(1929726585207259138L);
        roleIds.add(1929726893555712002L);
        roleIds.add(1929727070031052801L);
        roleIds.add(1929737350953938946L);
        roleIds.add(1929737675886669825L);
        roleIds.add(1929737779481784322L);
        roleIds.add(1929739694638739458L);
        roleIds.add(1929740474775089154L);
        roleIds.add(1929741272342966274L);
        roleIds.add(1929742445338800129L);
        roleIds.add(1930095095600783361L);

        return roleIds;
    }
}
