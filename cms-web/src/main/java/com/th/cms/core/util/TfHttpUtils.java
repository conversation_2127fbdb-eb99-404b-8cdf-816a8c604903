package com.th.cms.core.util;

import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.common.HttpHeader;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/4 20:35
 * cn.stylefeng.guns.core.util
 */
@Slf4j
public class TfHttpUtils {

    public static String postReqNoAck(String requrl, Map dataMap) {
        long sttime = System.currentTimeMillis();
        try {
            Header[] headers = HttpHeader.custom().other("Content-Type","application/json").build();
            // 请求参数
            String urlPre =  toReqStrParms(requrl,dataMap);
            HttpConfig config2 = getHtpCfg().timeout(15000).encoding("utf-8").headers(headers).url(urlPre);

            String html = HttpClientUtil.post(config2);
            log.info("toutiao httpcost "+(System.currentTimeMillis()-sttime)+" URL为【"+urlPre+"】 获取数据为【"+html+"】");
            return html;
        } catch (Exception excep) {
            log.warn("请求异常 ",excep);
        }
        return null;
    }

    private static String toReqStrParms(String url, Map<String, Object> paramsMap) {
        if (paramsMap == null || paramsMap.size() == 0) {
            return url;
        }
        StringBuilder strReqStr = new StringBuilder(url);
        strReqStr.append("?");

        int counter = 0;
        for (Map.Entry<String,Object> entry : paramsMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (key == null) {
                continue;
            }
            if (counter == 0) {
                if(! (value instanceof  String)){
                    strReqStr.append(key).append("=").append(URLEncoder.encode(JSONObject.toJSONString(value)));
                }else{
                    strReqStr.append(key).append("=").append(value);
                }
            } else {
                if(! (value instanceof  String)){
                    strReqStr.append("&").append(key).append("=").append(URLEncoder.encode(JSONObject.toJSONString(value)));
                }else{
                    strReqStr.append("&").append(key).append("=").append(value);
                }
            }
            counter++;
        }
        return strReqStr.toString();
    }
    /**
     * 获取httpclient dayday up
     * @return
     */
    public static CloseableHttpClient getHttpClient() {
        SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(SSLContexts.createDefault(),new String[]{"TLSv1.2"},null,SSLConnectionSocketFactory.getDefaultHostnameVerifier());
        CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssl).build();
        return httpclient;
    }

    public static HttpConfig getHtpCfg() {
        return HttpConfig.custom().client(getHttpClient());
    }


    public static JSONObject httpExec(HttpEntityEnclosingRequestBase httpEntity) {
        String json = reqEnt(httpEntity);
        return JSONObject.parseObject(json);
    }

    public static String reqEnt(HttpEntityEnclosingRequestBase httpEntity) {
        CloseableHttpResponse response = null;
        CloseableHttpClient client =  HttpClients.createDefault();;
        long ctime = System.currentTimeMillis();
        try {
            RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(5000)
                    .setSocketTimeout(5000).setConnectTimeout(5000).build();
            httpEntity.setConfig(requestConfig);
            response = client.execute(httpEntity);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
                StringBuffer result = new StringBuffer();
                String line = "";
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }
                bufferedReader.close();
                String json = result.toString();
                log.info("send httprequest "+httpEntity.getURI()+" "+(System.currentTimeMillis()-ctime));
                return json;
            }
        } catch (IOException e) {
            log.error("", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if (client != null) {
                    client.close();
                }
            } catch (IOException e) {
                log.error("", e);
            }
        }
        return null;
    }

    public static JSONObject sendPostRequest(String url, String bodyString, String authKey, String authToken) throws Exception {

        // 1. 创建HttpClient实例
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // 2. 创建HttpPost请求对象
        HttpPost httpPost = new HttpPost(url);

        try {
            // 3. 设置请求头
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader(authKey, authToken); // 认证token

            // 4. 设置JSON请求体
            StringEntity entity = new StringEntity(bodyString, "UTF-8");
            httpPost.setEntity(entity);

            // 5. 执行请求并获取响应
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                // 6. 获取响应实体
                HttpEntity responseEntity = response.getEntity();

                if (responseEntity != null) {
                    // 7. 将响应内容转换为字符串
                    String responseString = EntityUtils.toString(responseEntity, "UTF-8");

                    // 8. 将响应字符串解析为JSONObject
                    return JSONObject.parseObject(responseString);
                } else {
                    throw new RuntimeException("响应体为空");
                }
            }
        } finally {
            // 9. 关闭HttpClient
            httpClient.close();
        }
    }

}
