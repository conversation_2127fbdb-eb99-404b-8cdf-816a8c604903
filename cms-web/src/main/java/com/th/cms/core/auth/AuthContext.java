package com.th.cms.core.auth;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public class AuthContext {
    private static final ThreadLocal<AuthInfo> CONTEXT = new ThreadLocal<>();

    public static void set(AuthInfo authInfo) {
        CONTEXT.set(authInfo);
    }

    public static AuthInfo get() {
        return CONTEXT.get();
    }

    public static void clear() {
        CONTEXT.remove();
    }

    @Data
    public static class AuthInfo {
        /**
         * 表中是否有update user字段
         */
        private boolean hasUpdateUserField = false;
        /**
         * 配置-启用角色条件
         * 查询此角色（不包括此角色，但包含当前登录人）下级角色，及下下级角色直到最底层所有角色对应的所有人
         */
        private boolean useRole = false;
        /**
         * 配置-启用部门条件
         * 查询此部门（不包括此角色，但包含当前登录人）下级角色，及下下级角色直到最底层所有角色对应的所有人
         */
        private boolean useDept = false;
        /**
         * 配置-启用下属条件，默认true
         */
        private boolean useSubordinate = true;
        /**
         * 部门ID字段名
         */

        private String deptField = " dept_id ";
        /**
         * 创建人ID字段名
         */
        private String createUserField = " create_id ";
        /**
         * 更新人ID字段名
         */
        private String updateUserField = " update_id ";
        /**
         * 当前登录人的部门id集合
         */
        private List<Long> deptIds;
        /**
         * 当前登录人的角色id集合
         */

        private List<Long> roleIds;
        /**
         * 当前登录人id
         */
        private Long currentUserId;

        public String andDeptIdInSql() {
            if (CollectionUtils.isEmpty(deptIds) || StringUtils.isBlank(deptField)) {
                return StringUtils.EMPTY;
            }
            return " AND " + deptField + "in (" + getIn(deptIds) + ")";
        }

        public String andCreateUserIdInSql(List<Long> createUserIds) {
            if (CollectionUtils.isEmpty(createUserIds) || StringUtils.isBlank(createUserField)) {
                return StringUtils.EMPTY;
            }
            return " AND (" + createUserField + " in (" + getIn(createUserIds) + ")" + (hasUpdateUserField ? orModifyUserIdInSql(createUserIds) : StringUtils.EMPTY) + ")";
        }

        public String orModifyUserIdInSql(List<Long> createUserIds) {
            if (CollectionUtils.isEmpty(createUserIds) || StringUtils.isBlank(updateUserField)) {
                return StringUtils.EMPTY;
            }
            return " or " + updateUserField + " in (" + getIn(createUserIds) + ")";
        }

        private <T> String getIn(List<T> list) {
            return list.stream()
                    .map(item -> "'" + item + "'")
                    .collect(Collectors.joining(","));
        }
    }
}