
package com.th.cms.core.shiro.service;

import com.th.cms.core.shiro.ShiroUser;
import com.th.cms.modular.system.entity.User;
import org.apache.shiro.authc.SimpleAuthenticationInfo;

import java.util.List;

/**
 * 定义shirorealm所需数据的接口
 *
 * <AUTHOR>
 * @date 2016年12月5日 上午10:23:34
 */
public interface UserAuthService {

    /**
     * 根据账号获取登录用户
     *
     * @param account 账号
     */
    User user(String account);

    /**
     * 根据系统用户获取Shiro的用户
     *
     * @param user 系统用户
     */
    ShiroUser shiroUser(User user);

    /**
     * 获取权限列表通过角色id
     *
     * @param roleId 角色id
     */
    List<String> findPermissionsByRoleId(Long roleId);

    /**
     * 根据角色id获取角色名称
     *
     * @param roleId 角色id
     */
    String findRoleNameByRoleId(Long roleId);

    /**
     * 获取shiro的认证信息
     */
    SimpleAuthenticationInfo info(<PERSON><PERSON>User shiroUser, User user, String realmName);

    /**
     * 获取菜单
     * @param roleIds 角色
     * @return 结果
     */
    List<String> getMenuList(List<Long> roleIds);

    /**
     * 获取按钮
     * @param roleIds 角色
     * @return 结果
     */
    List<String> getButtonList(List<Long> roleIds);
}
