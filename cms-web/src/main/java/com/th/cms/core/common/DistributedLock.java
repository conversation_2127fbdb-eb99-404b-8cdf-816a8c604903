package com.th.cms.core.common;

import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

@Component
@Slf4j
public class DistributedLock {
    @Autowired
    private JedisPool jedisPool;

    public boolean tryLock(String rkey,int mins) {
        Jedis jedis = jedisPool.getResource();
        Long inum = jedis.incr(rkey);
        jedis.expire(rkey,mins * DateTimeConstants.SECONDS_PER_MINUTE);
        jedis.close();
        if(inum>1){
            return false;
        }else{
            return true;
        }
    }
 
    public void unlock(String rkey) {
        Jedis jedis = jedisPool.getResource();
        Long inum = jedis.incrBy(rkey,-1);
        jedis.close();
    }
}