package com.th.cms.core.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025年04月09日 11:32
 */
public class Objects {
    public static boolean equals(Object a,Object b){
        if(a==null && b==null){
            return true;
        }else if(a==null && b!=null){
            return false;
        }else if(a!=null && b==null){
            return false;
        }

        boolean isEq = a==b;
        if(!isEq && a != null){
            isEq = StringUtils.equals(a.toString(),b.toString());
        }
        return isEq;
    }

    public static String append(Double a,String x,Double b){
        if(a==null && b==null){
            return "";
        }else if(a==null && b!=null){
            return x+""+b.intValue();
        }else{
            return a.intValue()+x+b.intValue();
        }
    }
}
