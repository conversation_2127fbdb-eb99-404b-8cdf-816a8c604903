package com.th.cms.core.shiro;

import com.th.cms.core.aop.DataPermisionTypeEnum;
import com.th.cms.core.exceptions.BusiException;
import com.th.cms.core.util.ShiroUtils;

/**
 * 数据权限
 */
public class DataAuthService {

    public static void checkPermision(Object objData, DataPermisionTypeEnum dataPermisionTypeEnum){
        if(!ShiroKit.isAdmin()){
            if(DataPermisionTypeEnum.ByDept.equals(dataPermisionTypeEnum)){
                if(!ShiroUtils.isCanAuthByDept(objData)){
                    throw new BusiException("您无权访问");
                }
            }else{
                if(!ShiroUtils.isCanAuthByComp(objData)){
                    throw new BusiException("您无权访问");
                }
            }
        }

    }
}
