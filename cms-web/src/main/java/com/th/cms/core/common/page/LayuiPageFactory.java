
package com.th.cms.core.common.page;

import cn.stylefeng.roses.core.util.HttpContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.th.cms.core.util.OptionalUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Layui Table默认的分页参数创建
 *
 * <AUTHOR>
 * @date 2017-04-05 22:25
 */
public class LayuiPageFactory {

    /**
     * 获取layui table的分页参数
     *
     * <AUTHOR>
     * @Date 2019/1/25 22:13
     */
    public static Page defaultPage() {
        HttpServletRequest request = HttpContext.getRequest();
        //测试环境中可能获取不到，通过给默认值的方式
        if (request==null) {
            return new Page(1,10);
        }
        //每页多少条数据
        Integer limit = Integer.valueOf(Objects.toString(request.getParameter("limit"), "10"));

        //第几页
        Integer page = Integer.valueOf(Objects.toString(request.getParameter("page"), "1"));

        return new Page(page, limit);
    }

    /**
     * 创建layui能识别的分页响应参数
     *
     * <AUTHOR>
     * @Date 2019/1/25 22:14
     */
    public static <T> LayuiPageInfo<T> createPageInfo(IPage<T> page) {
        LayuiPageInfo<T> result = new LayuiPageInfo<>();
        result.setCount(page.getTotal());
        result.setData(page.getRecords());
        return result;
    }

    public static <T> LayuiPageInfo<T> createPageInfo(List<T> list) {
        List<T> ts = OptionalUtil.defaultList(list);
        LayuiPageInfo<T> result = new LayuiPageInfo<>();
        result.setCount(ts.size());
        result.setData(ts);
        return result;
    }

    public  static <T,R> LayuiPageInfo<R> createPageInfo(IPage<T> page, Function<T,R> function) {
        LayuiPageInfo<R> result = new LayuiPageInfo<>();
        result.setCount(page.getTotal());
        result.setData(OptionalUtil.defaultList(page.getRecords()).stream().map(function).collect(Collectors.toList()));
        return result;
    }
}
