
package com.th.cms.core.aop;

import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 权限检查的aop
 * @date 2017-07-13 21:05
 */
@Aspect
@Component
@Order(201)
public class PermissionAop {

//    @Pointcut(value = "@annotation(cn.stylefeng.guns.core.shiro.anno.DataPermisionAnno)")
//    private void cutPermission() {
//
//    }
//
//    @Around("cutPermission()")
//    public Object doPermission(ProceedingJoinPoint point) throws Throwable {
//        if(LoginUtils.getGuideInfo()!=null){
//
//        }else{
//            MethodSignature ms = (MethodSignature) point.getSignature();
//            Method method = ms.getMethod();
////            DataPermisionAnno dataPermisionAnno = method.getAnnotation(DataPermisionAnno.class);
////            DataPermisionTypeEnum dataPermisionTypeEnum = dataPermisionAnno.permisionType();
////            Object[] args = point.getArgs();
////
////            for(Object arg : args){
////                DataAuthService.checkPermision(arg,dataPermisionTypeEnum);
////            }
//        }
//        return point.proceed();
//    }


}
