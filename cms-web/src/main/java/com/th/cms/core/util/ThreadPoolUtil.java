package com.th.cms.core.util;

import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.List;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 异步处理线程池
 */
@Slf4j
public class ThreadPoolUtil {

    public final static ScheduledExecutorService SCHEDULE_POOL = Executors.newScheduledThreadPool(10);

    private static final ExecutorService EXECUTOR_POOL = createPool(100, 100, 100);

    private static final ExecutorService WEB_FLUSH_POOL = createPool(10, 10, 100, "flush");

    private static final ExecutorService LOG_POOL = createPool(5, 10, 100, "log");
    private static final ExecutorService TASK_POOL = createPool(20, 50,100,"apply");

    public static void exec(Runnable task) {
        EXECUTOR_POOL.execute(MdcThreadPoolUtil.wrap(task));
    }

    public static void flush(Runnable task) {
        WEB_FLUSH_POOL.execute(MdcThreadPoolUtil.wrap(task));
    }

    public static void log(Runnable task) {
        LOG_POOL.execute(MdcThreadPoolUtil.wrap(task));
    }

    public static void shutDown() {
        EXECUTOR_POOL.shutdown();
        SCHEDULE_POOL.shutdown();
        LOG_POOL.shutdown();
        WEB_FLUSH_POOL.shutdown();
    }

    public static ExecutorService createPool(int size) {
        return createPool(size, size);
    }

    public static ExecutorService createPool(int poolSize, int maxPoolSize) {
        return createPool(poolSize, maxPoolSize, 50);
    }

    public static ExecutorService createPool(int poolSize, int maxPoolSize, int queueSize) {
        return createPool(poolSize, maxPoolSize, queueSize, "myh-web");
    }

    public static ExecutorService createPool(int poolSize, int maxPoolSize, int queueSize, String name) {
        return new ThreadPoolExecutor(
                poolSize,
                maxPoolSize,
                1L,
                TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(queueSize),
                new BasicThreadFactory.Builder().namingPattern(name + "-thread-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public static void closePool(ExecutorService service) {
        service.shutdown();
    }

    @SneakyThrows
    public static <T, E> List<T> doTaskList(List<E> request, Function<E, T> handler) {
        return doTaskList(request, 5, handler);
    }

    @SneakyThrows
    public static <T, E> List<T> doTaskList(List<E> request, int maxPoolSize, Function<E, T> handler) {
        return doTaskList(request, maxPoolSize, handler, "mny-web-task-list");
    }

    @SneakyThrows
    public static <T, E> List<T> doTaskList(List<E> request, int maxPoolSize, Function<E, T> handler, String taskName) {
        List<E> es = OptionalUtil.defaultList(request);
        int size = es.size();
        if (size == 0) {
            return Lists.newArrayList();
        }
        if (maxPoolSize < 0) {
            maxPoolSize = 5;
        }
        //防止无限扩张线程，每个最多允许20个
        int poolSize = Math.min(maxPoolSize, 10);
        //计算最小线程数
        int min = Math.min(size, poolSize);
        //计算队列大小，为了防止队列为0，默认给一个1
        int queueSize = Math.max(size - min, 1);
        //最大100个队列
        queueSize = Math.min(queueSize,100);
        ExecutorService pool = createPool(min, poolSize, queueSize, taskName);
        try {
            List<T> result = Lists.newCopyOnWriteArrayList();
            List<CompletableFuture<T>> futures = es.stream().map(e -> doTask(handler, e, pool)).collect(Collectors.toList());
            CompletableFuture allFuture = CompletableFuture.allOf(futures.<CompletableFuture>toArray(new CompletableFuture[0])).whenComplete((v, t) -> {
                futures.forEach(f -> {
                    result.add(f.getNow(null));
                });
            });
            allFuture.join();
            return result;
        } finally {
            closePool(pool);
        }
    }

    @SneakyThrows
    public static <T, R> CompletableFuture<R> doTask(Function<T, R> handler, ExecutorService executors) {
        return doTask(handler, null, executors);
    }

    @SneakyThrows
    public static <T, R> CompletableFuture<R> doTask(Function<T, R> handler) {
        return doTask(handler, null, null);
    }

    @SneakyThrows
    public static <T, R> CompletableFuture<R> doTask(Function<T, R> handler, T t, ExecutorService executors) {
        return CompletableFuture.supplyAsync(MdcThreadPoolUtil.wrapSupplier(() -> handler.apply(t)), executors == null ? TASK_POOL : executors);
    }

    /**
     * CompletableFuture 不允许自己是用get，因为get方法有bug
     * 需要使用带有超时时间的get
     *
     * @param completableFuture
     * @param <T>
     * @return
     * <AUTHOR>
     */
    @SneakyThrows
    public static <T> T futureGet(CompletableFuture<T> completableFuture) {
        if (completableFuture == null) {
            return null;
        }
        return completableFuture.get(5, TimeUnit.MINUTES);
    }
    /**
     * CompletableFuture 不允许自己是用get，因为get方法有bug
     * 需要使用带有超时时间的get
     *
     * @param completableFuture
     * @param <T>
     * @return
     * <AUTHOR>
     */
    @SneakyThrows
    public static <T> T futureGet(CompletableFuture<T> completableFuture,Integer time) {
        if (completableFuture == null) {
            return null;
        }
        return completableFuture.get(time, TimeUnit.MINUTES);
    }
}
