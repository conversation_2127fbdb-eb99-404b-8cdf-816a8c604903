package com.th.cms.core.exceptions;

public enum PcExceptionCode {
    /** 1: 运营中 */
    LOGINGERROR(1001,"登陆异常"),

    DAYLIYCHECK(1002,"该车辆已经有检查信息，请修改"),
    DAYLArriveAfterLeave(1003,"您必须先添加离开信息"),
    COMMON(1004,""),
    ;
    public Integer value;
    public String name;

    PcExceptionCode(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static PcExceptionCode getType(Integer value) {
    PcExceptionCode[] carStatusList = PcExceptionCode.values();
        for (PcExceptionCode carStatus : carStatusList) {
            if (carStatus.value.equals(value)) {
                return carStatus;
            }
        }
       return  null;
    }
}
