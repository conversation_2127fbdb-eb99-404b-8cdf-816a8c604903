<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="${ctxPath}/assets/expand/images/favicon.ico" rel="icon">
    <title>${systemName}</title>
    <link rel="stylesheet" href="${ctxPath}/assets/common/layui/css/layui.css" />
    <link rel="stylesheet" href="${ctxPath}/assets/common/module/admin.css" />
    <link rel="stylesheet" href="${ctxPath}/assets/common/css/notice.css" />

</head>

<body class="layui-layout-body">

    <div class="layui-layout layui-layout-admin">

        <!-- 头部 -->
        @include("/common/_header.html"){}

        <!-- 侧边栏 -->
        @include("/common/_sidebar.html"){}

        <!-- 主体部分 -->
        @include("/common/_body.html"){}

        <!-- 底部11 -->


    </div>

    <!-- 加载动画，移除位置在common.js中 -->
    @include("/common/loading.html"){}

    @/* 加入contextPath属性和session超时的配置 */
    <script type="text/javascript">
        var Feng = {
            ctxPath: "",
            addCtx: function (ctx) {
                if (this.ctxPath === "") {
                    this.ctxPath = ctx;
                }
            }
        };
        Feng.addCtx("${ctxPath}");
    </script>
    <script type="text/javascript" src="${ctxPath}/assets/common/layui/layui.js"></script>
    <script type="text/javascript" src="${ctxPath}/assets/common/js/common.js"></script>

    <script>
        layui.use(['layer', 'element', 'admin', 'index'], function () {
            var $ = layui.jquery;
            var layer = layui.layer;
            var admin = layui.admin;
            var index = layui.index;

            admin.changeTheme('theme-white');
            // 默认加载主页
            index.loadHome({
                menuPath: '${ctxPath}/system/console',
                menuName: '<i class="layui-icon layui-icon-home"></i>'
            });

            // 修改密码点击事件
            $('#setPsw').click(function () {
                admin.open({
                    id: 'pswForm',
                    type: 2,
                    title: '修改密码',
                    shade: 0,
                    content: '${ctxPath}/system/user_chpwd'
                });
            });

            // 修改手机号点击事件
            $('#setPhone').click(function () {
                admin.open({
                    id: 'setPhoneForm',
                    type: 2,
                    title: '修改手机号',
                    shade: 0,
                    content: '${ctxPath}/system/user_set_phone'
                });
            });

            // 退出登录点击事件
            $('#btnLogout').click(function () {
                layer.confirm('确定退出登录？', {
                    skin: 'layui-layer-admin'
                }, function () {
                    window.location.href = "${ctxPath}/logout";
                });
            });
            var viewModel = {}
            viewModel.getUserInfo = function () {
                var xhr = new XMLHttpRequest();

                xhr.open('GET', Feng.ctxPath + '/system/currentUserInfo', true);

                xhr.onreadystatechange = function () {
                    // 请求完成且成功
                    if (xhr.readyState === 4) {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            try {
                                var data = JSON.parse(xhr.responseText);
                                data = data.data;
                                if (data) {
                                    $('#avatar').attr('src', 'https://mingyuehao.oss-cn-beijing.aliyuncs.com/' + data.avatar);
                                    $('#userName').text(data.name);
                                }
                                console.log('用户详情:', data);
                            } catch (e) {
                                console.error('JSON 解析失败');
                            }
                        } else {
                            console.error('请求失败，状态码:', xhr.status);
                        }
                    }
                };

                xhr.send();
            }
            var viewModel = {
                msgIds: []
            };
            viewModel.getUserInfo();
            document.getElementById('messageListPopId').style.display = 'none';
            var reloadMsg = function () {
                var xhr = new XMLHttpRequest();

                xhr.open('GET', Feng.ctxPath + '/system/myNotice', true);

                xhr.onreadystatechange = function () {
                    // 请求完成且成功
                    if (xhr.readyState === 4) {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            try {
                                var data = JSON.parse(xhr.responseText);

                                if (data) {
                                    var noticeList = document.getElementById('messageListId');
                                    noticeList.innerHTML = '';
                                    // 假设返回的数据是JSON格式的数组
                                    let output = '';
                                    viewModel.msgIds = [];
                                    data.forEach(function (item) {
                                        viewModel.msgIds.push(item.noticeId);
                                        output += ' <a class="message-list-item" href="javascript:;">';
                                        output += '  <img class="message-item-icon" src="${ctxPath}/assets/common/images/message.png">';
                                        output += ' <span class="message-item-right">';
                                        output += '<span class="message-item-title">' + item.title + '</span>';
                                        output += '<span class="message-item-text">' + item.createTime + '</span>';
                                        output += '  </span>';
                                        output += ' </a>';

                                    });
                                    output += '';
                                    document.getElementById('messageListId').innerHTML = output;
                                    document.getElementById('messageListPopId').style.display = 'block';
                                }
                            } catch (e) {
                                console.error('JSON 解析失败');
                            }
                        } else {
                            console.error('请求失败，状态码:', xhr.status);
                        }
                    }
                };

                xhr.send();
            };
            $("#allReadId").click(function () {
                $.ajax({
                    url: Feng.ctxPath + '/system/allRead',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(viewModel.msgIds),
                    success: function (data) {
                        if (data.code == 0) {
                            reloadMsg();
                        } else {
                            layer.msg(data.msg);
                        }
                    }
                });
            })
            $("#messageId").click(function () {
                reloadMsg();
            })

        });
        document.addEventListener("DOMContentLoaded", function () {
            window.addEventListener("click", function (event) {
                console.log("🚀 ~ event:", event)
                var popup = document.getElementById('messageListPopId');

                if (!popup) {
                    console.error("未找到 ID 为 messageListPopId 的元素");
                    return;
                }

                // 如果点击的是弹出框或其子元素，则不隐藏
                if (!popup.contains(event.target)) {
                    popup.style.display = "none";
                }
            });
        });

    </script>
</body>

</html>