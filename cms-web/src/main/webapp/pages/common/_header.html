@/* 框架顶部 */
<div class="layui-header">
    <div class="layui-logo">
        <!--<img src="${ctxPath}/assets/common/images/logo.png"/>-->
        <i class="layui-icon layui-icon-component"></i>
        <cite>&nbsp;${systemName}&emsp;</cite>
    </div>
    <ul class="layui-nav layui-layout-left">
        <li class="layui-nav-item" lay-unselect>
            <a ew-event="flexible" title="侧边伸缩"><i class="layui-icon layui-icon-shrink-right"></i></a>
        </li>
        <li class="layui-nav-item" lay-unselect>
            <a ew-event="refresh" title="刷新"><i class="layui-icon layui-icon-refresh-3"></i></a>
        </li>
    </ul>
    <ul class="layui-nav layui-layout-right">
        <li class="layui-nav-item" lay-unselect>
            <a href="${ctxPath}/im/customer/toImWorkspace" target="_blank">客服工作区</a>
        </li>
        <li class="layui-nav-item" lay-unselect>
            <a id="messageId" title="消息"><i class="layui-icon layui-icon-notice"></i></a>
        </li>
        <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a ew-event="fullScreen" title="全屏"><i class="layui-icon layui-icon-screen-full"></i></a>
        </li>
        <li class="layui-nav-item" lay-unselect>
            <a>
                <img id="avatar" class="layui-nav-img">
                <cite id="userName"></cite>
            </a>
            <dl class="layui-nav-child">
                <dd lay-unselect>
                    <a ew-href="${ctxPath}/system/user_info">个人中心</a>
                </dd>
                <dd lay-unselect>
                    <a id="setPsw">修改密码</a>
                </dd>
                <dd lay-unselect>
                    <a id="setPhone">修改手机号</a>
                </dd>
                <hr>
                <dd lay-unselect>
                    <a id="btnLogout">退出</a>
                </dd>
            </dl>
        </li>
        <li class="layui-nav-item" lay-unselect>
            <a ew-event="theme" title="主题"><i class="layui-icon layui-icon-more-vertical"></i></a>
        </li>
    </ul>
 <div class="layui-tab layui-tab-brief" style="padding: 5px 0;margin: 0;" id="messageListPopId">
 
    <div class="layui-tab-content" style="padding: 5px 0;">

        <!-- tab1 -->
        <div class="layui-tab-item layui-show">
            <div class="message-list" id="messageListId" >
                <!-- 实际项目请使用后台数据循环出来 -->
                <a class="message-list-item" href="javascript:;">
                    <img class="message-item-icon" src="${ctxPath}/assets/common/images/message.png">
                    <div class="message-item-right">
                        <h2 class="message-item-title">你收到了14份新周报</h2>
                        <p class="message-item-text">10个月前</p>
                    </div>
                </a>
                <a class="message-list-item" href="javascript:;">
                    <img class="message-item-icon" src="${ctxPath}/assets/common/images/message.png">
                    <div class="message-item-right">
                        <h2 class="message-item-title">你收到了14份新周报</h2>
                        <p class="message-item-text">10个月前</p>
                    </div>
                </a>
                <a class="message-list-item" href="javascript:;">
                    <img class="message-item-icon" src="${ctxPath}/assets/common/images/message.png">
                    <div class="message-item-right">
                        <h2 class="message-item-title">你收到了14份新周报</h2>
                        <p class="message-item-text">10个月前</p>
                    </div>
                </a>

                <!-- 列表为空 -->
                <div class="message-list-empty" style="display: none;">
                    <img src="${ctxPath}/assets/common/images/img_msg_notice.svg">
                    <div>没有通知</div>
                </div>

            </div>

            <a class="message-btn-clear" href="javascript:;" id="allReadId">全部标记已读</a>

        </div>

    </div>
</div>
</div>
