/**
 * 详情对话框
 */
var BizImConfInfoDlg = {
    data: {
       id:"",
       fenpRule:"",
       jiedaiFenp:"",
       yanshiAltTime:"",
       yanshiAltCs:"",
       shenjiUserId:"",
       shegjiUserName:"",
       isUseTel:"",
       userTels:"",
       createTime:"",
       updateTime:"",
    }
};
layui.use(['form','ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizImConf/detail?bizImConfId=" + Feng.getUrlParam("bizImConfId"));
    var result = ajax.start();
     form.val('bizImConfForm', result.data);
    //图片赋值
    $("#id").attr("disabled","disabled");
    $("#fenpRule").attr("disabled","disabled");
    $("#jiedaiFenp").attr("disabled","disabled");
    $("#yanshiAltTime").attr("disabled","disabled");
    $("#yanshiAltCs").attr("disabled","disabled");
    $("#shenjiUserId").attr("disabled","disabled");
    $("#shegjiUserName").attr("disabled","disabled");
    $("#isUseTel").attr("disabled","disabled");
    $("#userTels").attr("disabled","disabled");
    $("#createTime").attr("disabled","disabled");
    $("#updateTime").attr("disabled","disabled");
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizImConf";
    });
});
