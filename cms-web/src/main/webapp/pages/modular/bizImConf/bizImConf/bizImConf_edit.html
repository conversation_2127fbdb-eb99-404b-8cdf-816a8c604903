@layout("/common/_container.html",{js:["/pages/modular/bizImConf/bizImConf/js/bizImConf_edit.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">设置客服接待规则</span>
</div>

<div class="layui-fluid " style="">
    <form id="bizImConfForm" lay-filter="bizImConfForm" class="layui-form model-form">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-card-header">基础规则</div>

                <div class="layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">分配模式</label>
                        <div class="layui-input-block">
                            <input type="radio" name="fenpRule" value="1" title="平均分配：按参与自动分配客服轮流分配">
                        </div>
                        <div class="layui-input-block">
                            <input type="radio" name="fenpRule" value="2" title="随机分配：按参与自动分配客服随机分配">
                        </div>
                        <div class="layui-input-block">
                            <input type="radio" name="fenpRule" value="3" title="空闲分配：优先分配给用户最少的客服">
                        </div>
                    </div>
                </div>

                <div class="layui-card-header">线索规则</div>
                <div class="layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">线索规则</label>
                        <div class="layui-input-block">
                            <input type="radio" name="jiedaiFenp" value="1" title="按照之前接待过的客服，优先分配">
                        </div>
                    </div>
                </div>

                <div class="layui-card-header">客诉预警</div>
                <div class="layui-form-item"></div>

                <div class="layui-form-item">
                    <div class="layui-row">
                        <label class="layui-form-label" style="width: 180px">延时消息响应时间</label>
                        <div class="layui-input-block layui-col-md6" style="display: flex; align-items: center; padding-right: 15px;">
                            <input id="yanshiAltTime" name="yanshiAltTime" type="text" class="layui-input" lay-verify="required" required style="flex: 1; margin-right: 8px;">
                            <span style="white-space: nowrap;">分钟</span>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-row">
                        <label class="layui-form-label" style="width: 180px">延时消息响应次数</label>
                        <div class="layui-input-block layui-col-md6" style="display: flex; align-items: center; padding-right: 15px;">
                            <input id="yanshiAltCs" name="yanshiAltCs" type="text" class="layui-input" lay-verify="required" required style="flex: 1; margin-right: 8px;">
                            <span style="white-space: nowrap;">次</span>
                        </div>
                    </div>
                </div>



                <div class="layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">升级响应人</label>
                        <div class="layui-input-block">
                            <input type="radio" name="shegjiUserName" id="shegjiUserName" value="1" title="升级响应人" checked>
                        </div>
                    </div>
                </div>


                <div class="layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">是否电话</label>
                        <div class="layui-input-block">
                            <input type="radio" name="isUseTel" id="isUseTel" value="1" title="是否电话" checked>
                        </div>
                    </div>
                </div>

                <div class="layui-card-header">预警电话</div>
                <div class="layui-form">
                    <!-- 一级响应人 -->
                    <div class="layui-form-item">
                        <label class="layui-form-label">一级响应人</label>
                        <div class="layui-input-block">
                            <div class="layui-row layui-col-space10">
                                <!-- 名称输入框 -->
                                <div class="layui-col-md6">
                                    <input type="text" name="uname" placeholder="请输入名称" class="layui-input"/>
                                </div>
                                <!-- 手机号输入框 -->
                                <div class="layui-col-md6">
                                    <input type="text" name="utel" placeholder="请输入手机号" class="layui-input" >
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 二级响应人 -->
                    <div class="layui-form-item">
                        <label class="layui-form-label">二级响应人</label>
                        <div class="layui-input-block">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md6">
                                    <input type="text" name="uname2" placeholder="请输入名称"    class="layui-input" >
                                </div>
                                <div class="layui-col-md6">
                                    <input type="text" name="utel2" placeholder="请输入手机号"    class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 三级响应人 -->
                    <div class="layui-form-item">
                        <label class="layui-form-label">三级响应人</label>
                        <div class="layui-input-block">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md6">
                                    <input type="text" name="uname3" placeholder="请输入名称"  class="layui-input" >
                                </div>
                                <div class="layui-col-md6">
                                    <input type="text" name="utel3" placeholder="请输入手机号"  class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!-- 操作按钮 -->
            <div class="layui-form-item  text-center">
                <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
            </div>
        </div>
    </form>
</div>
@}
