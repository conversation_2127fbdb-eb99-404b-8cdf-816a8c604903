@layout("/common/_container.html",{js:["/pages/modular/bizAppPolicyContent/bizAppPolicyContent/js/bizAppPolicyContent.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">政策内容管理管理</span>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <input type="hidden" id="bizAppPolicyContentId" value="bizAppPolicyContentId"/>
                                <input id="condition" class="layui-input" type="text" placeholder="名称/编码" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <button id="btnSearch" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                 @if(shiro.hasPermission("/bizAppPolicyContent/bizAppPolicyContent_add")){
                                <button id="btnAdd" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                                 @}
                                <button id="btnBack" class="layui-btn icon-btn layui-btn-warm"><i class="layui-icon">&#x1006;</i>关闭</button>
                            </div>
                        </div>
                    </div>
                    <table class="layui-table" id="bizAppPolicyContentTable" lay-filter="bizAppPolicyContentTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
     @if(shiro.hasPermission("/bizAppPolicyContent/bizAppPolicyContent_update")){
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    @}
     @if(shiro.hasPermission("/bizAppPolicyContent/delete")){
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    @}
</script>
@}

