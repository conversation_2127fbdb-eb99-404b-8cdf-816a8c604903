@layout("/common/_container.html",{js:["/pages/modular/wfApprovalRequest/wfApprovalRequest/js/wfApprovalRequest_add.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">添加审批请求</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="wfApprovalRequestForm" lay-filter="wfApprovalRequestForm" class="layui-form model-form"   style="max-width: 700px;margin: 40px auto;">
 <div class="layui-form-item">            <div class="layui-inline">
                <label class="layui-form-label"></label>
                <div class="layui-input-inline">
                    <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">审批标题</label>
                <div class="layui-input-inline">
                    <input id="title" name="title" placeholder="请输入title" type="text" class="layui-input" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">申请人ID</label>
                <div class="layui-input-inline">
                    <input id="applicantId" name="applicantId" placeholder="请输入applicantId" type="text" class="layui-input" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">申请人</label>
                <div class="layui-input-inline">
                    <input id="applicantName" name="applicantName" placeholder="请输入applicantName" type="text" class="layui-input" lay-verify="required" required/>
                </div>
            </div>
 </div>  <div class="layui-form-item">            <div class="layui-inline">
                <label class="layui-form-label">审批类型</label>
                <div class="layui-input-inline">
                    <input id="requestType" name="requestType" placeholder="请输入requestType" type="text" class="layui-input" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">审批内容</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入content" class="layui-textarea" id="content" name="content"></textarea>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">审批状态</label>
                <div class="layui-input-inline">
                    <input id="requestStatus" name="requestStatus" placeholder="请输入requestStatus" type="text" class="layui-input" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">创建时间</label>
                <div class="layui-input-inline">
                    <input id="createTime" name="createTime" placeholder="请输入createTime" type="text" class="layui-input" lay-verify="required" required/>
                </div>
            </div>
 </div>  <div class="layui-form-item">            <div class="layui-inline">
                <label class="layui-form-label">更新时间</label>
                <div class="layui-input-inline">
                    <input id="updateTime" name="updateTime" placeholder="请输入updateTime" type="text" class="layui-input" lay-verify="required" required/>
                </div>
            </div>
            <div class="layui-form-item  text-center">
                <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
            </div>
            </div>
            </form>
        </div>
    </div>
</div>

@}
