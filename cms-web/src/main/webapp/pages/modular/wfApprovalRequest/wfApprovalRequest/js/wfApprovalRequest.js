layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var WfApprovalRequest = {
        tableId: "wfApprovalRequestTable"
    };

    /**
     * 初始化表格的列
     */
    WfApprovalRequest.initColumn = function () {
        return [[
            {type: 'checkbox'},
{field: 'id', minWidth: 100,hide: false,title: ''},
{field: 'title', minWidth: 100,hide: false,title: '审批标题'},
{field: 'applicantId', minWidth: 100,hide: false,title: '申请人ID'},
{field: 'applicantName', minWidth: 100,hide: false,title: '申请人'},
{field: 'requestType', minWidth: 100,hide: false,title: '审批类型'},
{field: 'content', minWidth: 100,hide: false,title: '审批内容'},
{field: 'requestStatus', minWidth: 100,hide: false,title: '审批状态'},
{field: 'createTime', minWidth: 100,hide: false,title: '创建时间'},
{field: 'updateTime', minWidth: 100,hide: false,title: '更新时间'},
            {align: 'center', toolbar: '#tableBar',fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + WfApprovalRequest.tableId,
        url: Feng.ctxPath + '/wfApprovalRequest/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: WfApprovalRequest.initColumn()
    });
    /**
     * 点击查询按钮
     */
    WfApprovalRequest.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(WfApprovalRequest.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    WfApprovalRequest.openAddDlg = function () {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加审批请求',
            content:  Feng.ctxPath + '/wfApprovalRequest/wfApprovalRequest_add',
            end: function () {
                layer.closeAll('loading');
                table.reload(WfApprovalRequest.tableId);
            }
        });
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    WfApprovalRequest.openEditDlg = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加审批请求',
            content:  Feng.ctxPath + '/wfApprovalRequest/wfApprovalRequest_update?wfApprovalRequestId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(WfApprovalRequest.tableId);
            }
        });
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    WfApprovalRequest.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/wfApprovalRequest/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(WfApprovalRequest.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("wfApprovalRequestId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 审批请求详情
     */
    WfApprovalRequest.detail = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '审批请求详情',
            content:  Feng.ctxPath + '/wfApprovalRequest/wfApprovalRequest_detail?wfApprovalRequestId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(WfApprovalRequest.tableId);
            }
        });
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        WfApprovalRequest.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        WfApprovalRequest.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + WfApprovalRequest.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            WfApprovalRequest.openEditDlg(data);
        } else if (layEvent === 'delete') {
            WfApprovalRequest.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            WfApprovalRequest.detail(data);
        }
    });
});


