@layout("/common/_container.html",{js:["/pages/modular/imGroup/imGroup/js/imGroup_detail.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">群组管理详情</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="imGroupForm" lay-filter="imGroupForm" class="layui-form model-form"  style="max-width: 700px;margin: 40px auto;">
            <input name="" type="hidden"/>
 <div class="layui-form-item">        <div class="layui-inline">
            <label class="layui-form-label">ID</label>
            <div class="layui-input-inline">
                <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">群名称</label>
            <div class="layui-input-inline">
                <input id="tname" name="tname" placeholder="请输入tname" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">管理员</label>
            <div class="layui-input-inline">
                <input id="owner" name="owner" placeholder="请输入owner" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">成员</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入members" class="layui-textarea" id="members" name="members"></textarea>
            </div>
        </div>
 </div>  <div class="layui-form-item">        <div class="layui-inline">
            <label class="layui-form-label">类型</label>
            <div class="layui-input-inline">
                <input id="groupType" name="groupType" placeholder="请输入groupType" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">未读数</label>
            <div class="layui-input-inline">
                <input id="unreadNum" name="unreadNum" placeholder="请输入unreadNum" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">图标</label>
            <div class="layui-input-inline">
                <div class="layui-upload">
                    <div class="layui-upload-list">
                        <img class="layui-upload-img" style="width: 200px;height: 200px" id="iconPicImg">
                        <input type="hidden" name="iconPic" id="iconPicHidden">
                        <p id="demoText"></p>
                    </div>
                    <button type="button" class="layui-btn" id="uploadiconPic">上传图片</button>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">消息</label>
            <div class="layui-input-inline">
                <input id="msg" name="msg" placeholder="请输入msg" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
 </div>  <div class="layui-form-item">        <div class="layui-inline">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-inline">
                <input id="remark" name="remark" placeholder="请输入remark" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">成员数</label>
            <div class="layui-input-inline">
                <input id="memberNum" name="memberNum" placeholder="请输入memberNum" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline">
                <input id="createTime" name="createTime" placeholder="请输入createTime" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">更新时间</label>
            <div class="layui-input-inline">
                <input id="updateTime" name="updateTime" placeholder="请输入updateTime" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item  text-center">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
        </div>
        </form>
    </div>
    </div>
</div>
@}
