layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var ImGroup = {
        tableId: "imGroupTable"
    };

    /**
     * 初始化表格的列
     */
    ImGroup.initColumn = function () {
        return [[
            {type: 'checkbox'},
{field: 'id', minWidth: 100,hide: false,title: 'ID'},
{field: 'tname', minWidth: 100,hide: false,title: '群名称'},
{field: 'owner', minWidth: 100,hide: false,title: '管理员'},
{field: 'members', minWidth: 100,hide: false,title: '成员'},
{field: 'groupType', minWidth: 100,hide: false,title: '类型'},
{field: 'unreadNum', minWidth: 100,hide: false,title: '未读数'},
{field: 'iconPic', minWidth: 100, templet: function(d){ return '<img style="width:200px;height: 200px" src="'+ Feng.getAliImgUrl(d.iconPic) +'" class="layui-upload-img">'  },hide: false,title: '图标'},
{field: 'msg', minWidth: 100,hide: false,title: '消息'},
{field: 'remark', minWidth: 100,hide: false,title: '备注'},
{field: 'memberNum', minWidth: 100,hide: false,title: '成员数'},
{field: 'createTime', minWidth: 100,hide: false,title: '创建时间'},
{field: 'updateTime', minWidth: 100,hide: false,title: '更新时间'},
            {align: 'center', toolbar: '#tableBar',fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + ImGroup.tableId,
        url: Feng.ctxPath + '/imGroup/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: ImGroup.initColumn()
    });
    /**
     * 点击查询按钮
     */
    ImGroup.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(ImGroup.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    ImGroup.openAddDlg = function () {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加群组管理',
            content:  Feng.ctxPath + '/imGroup/imGroup_add',
            end: function () {
                layer.closeAll('loading');
                table.reload(ImGroup.tableId);
            }
        });
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    ImGroup.openEditDlg = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加群组管理',
            content:  Feng.ctxPath + '/imGroup/imGroup_update?imGroupId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(ImGroup.tableId);
            }
        });
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    ImGroup.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/imGroup/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(ImGroup.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("imGroupId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 群组管理详情
     */
    ImGroup.detail = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '群组管理详情',
            content:  Feng.ctxPath + '/imGroup/imGroup_detail?imGroupId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(ImGroup.tableId);
            }
        });
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        ImGroup.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        ImGroup.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + ImGroup.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            ImGroup.openEditDlg(data);
        } else if (layEvent === 'delete') {
            ImGroup.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            ImGroup.detail(data);
        }
    });
});


