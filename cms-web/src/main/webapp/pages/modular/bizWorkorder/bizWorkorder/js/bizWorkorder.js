layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var BizWorkorder = {
        tableId: "bizWorkorderTable"
    };

    /**
     * 初始化表格的列
     */
    BizWorkorder.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {field: 'id', minWidth: 100, hide: false, title: '编号'},
            {field: 'questType', minWidth: 100, hide: true, title: '分类编号'},
            {field: 'questTypeName', minWidth: 100, hide: false, title: '问题分类'},
            {field: 'title', minWidth: 100, hide: false, title: '标题'},
            {field: 'content', minWidth: 100, hide: true, title: '内容'},
            {field: 'questStatus', minWidth: 100, hide: true, title: '状态编号'},
            {field: 'questStatusname', minWidth: 100, hide: false, title: '状态'},
            {field: 'urgency', minWidth: 100, hide: false, title: '紧急程度'},
            {field: 'importance', minWidth: 100, hide: false, title: '重要程度'},
            {field: 'customerId', minWidth: 100, hide: true, title: '客服ID'},
            {field: 'customerName', minWidth: 100, hide: false, title: '客服名称'},
            {field: 'infucerId', minWidth: 100, hide: true, title: '达人ID'},
            {field: 'infucerName', minWidth: 100, hide: false, title: '达人名称'},
            {field: 'createTime', minWidth: 100, hide: false, title: '创建时间'},
            {field: 'updateTime', minWidth: 100, hide: false, title: '更新时间'},
            {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BizWorkorder.tableId,
        url: Feng.ctxPath + '/bizWorkorder/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: BizWorkorder.initColumn()
    });
    /**
     * 点击查询按钮
     */
    BizWorkorder.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(BizWorkorder.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    BizWorkorder.openAddDlg = function () {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加客服工单',
            content: Feng.ctxPath + '/bizWorkorder/bizWorkorder_add',
            end: function () {
                layer.closeAll('loading');
                table.reload(BizWorkorder.tableId);
            }
        });
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    BizWorkorder.openEditDlg = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加客服工单',
            content: Feng.ctxPath + '/bizWorkorder/bizWorkorder_update?bizWorkorderId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BizWorkorder.tableId);
            }
        });
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    BizWorkorder.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizWorkorder/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(BizWorkorder.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("bizWorkorderId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 客服工单详情
     */
    BizWorkorder.detail = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '客服工单详情',
            content: Feng.ctxPath + '/bizWorkorder/bizWorkorder_detail?bizWorkorderId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BizWorkorder.tableId);
            }
        });
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BizWorkorder.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        BizWorkorder.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + BizWorkorder.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            BizWorkorder.openEditDlg(data);
        } else if (layEvent === 'delete') {
            BizWorkorder.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            BizWorkorder.detail(data);
        }
    });
});


