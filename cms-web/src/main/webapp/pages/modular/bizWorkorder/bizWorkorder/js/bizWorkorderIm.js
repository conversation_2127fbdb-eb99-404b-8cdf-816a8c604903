layui.use(['table', 'ax','element'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    // debugger
    /**
     * 字典类型表管理
     */
    var BizWorkorder = {
        tableId: "orderTable"
    };


    var BizReceiveWorkorder = {
        tableId: "receivedTable"
    };
    /**
     * 初始化表格的列
     */
    BizWorkorder.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {field: 'id', minWidth: 100, hide: false, title: '编号'},
            {field: 'questType', minWidth: 100, hide: true, title: '分类编号'},
            {field: 'questTypeName', minWidth: 100, hide: false, title: '问题分类'},
            {field: 'title', minWidth: 100, hide: false, title: '标题'},
            {field: 'content', minWidth: 100, hide: false, title: '内容'},
            {field: 'questStatus', minWidth: 100, hide: true, title: '状态编号'},
            {field: 'questStatusname', minWidth: 100, hide: false, title: '状态'},
            {field: 'urgency', minWidth: 100, hide: false, title: '紧急程度'},
            {field: 'importance', minWidth: 100, hide: false, title: '重要程度'},
            {field: 'customerId', minWidth: 100, hide: true, title: '客服ID'},
            {field: 'customerName', minWidth: 100, hide: false, title: '客服名称'},
            {field: 'createTime', minWidth: 100, hide: false, title: '创建时间'},
            {field: 'updateTime', minWidth: 100, hide: true, title: ''},
            {align: 'center', toolbar: '#toolBar', fixed: 'right', minWidth: 250, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BizWorkorder.tableId,
        url: Feng.ctxPath + '/bizWorkorder/list?tp=create',
        page: true,
        limit: 20,
        height: window.innerHeight - 170, // 动态计算高度
        cols: BizWorkorder.initColumn()
    });

    var destReqUrl = Feng.ctxPath +"/bizWorktype/list?limit=200&page=0";
    selector.init(destReqUrl,'fenlei','id','typeName','fenleiName');

    // 监听 Tab 切换事件
    $('#jieshoutab').on('click', 'li', function(e){
        setTimeout(function(e) { // 等待 Layui 完成样式切换
            const activeTab = $('.layui-tab-title .layui-this');
            console.log('当前激活的 Tab:', activeTab.text());

            if (activeTab.text().indexOf("我创建") !== -1) {
                // 设置元素的 class 属性（覆盖原有 class）
                $('#receiveOrderT1').removeClass('layui-show');
                $('#createOrderT1').addClass('layui-show');

                tableResult = table.render({
                    elem: '#' + BizWorkorder.tableId,
                    url: Feng.ctxPath + '/bizWorkorder/list?tp=create',
                    page: true,
                    limit: 20,
                    height: window.innerHeight - 170, // 动态计算高度
                    cols: BizWorkorder.initColumn()
                });
            }else{
                $('#createOrderT1').removeClass('layui-show');
                $('#receiveOrderT1').addClass('layui-show');

                tableResult = table.render({
                    elem: '#' + BizReceiveWorkorder.tableId,
                    url: Feng.ctxPath + '/bizWorkorder/list?tp=receive',
                    page: true,
                    limit: 20,
                    height: window.innerHeight - 170, // 动态计算高度
                    cols: BizWorkorder.initColumn()
                });
            }
        }, 50);
    });

    /**
     * 点击查询按钮
     */
    BizWorkorder.search = function () {
        var queryData = {};
        queryData['shoulikefu'] = $("#shoulikefu").val();
        queryData['biaoti'] = $("#biaoti").val();
        queryData['questStatusname'] = $("#workstatus").val();
        queryData['urgency'] = $("#jingji").val();
        queryData['importance'] = $("#zhongyao").val();
        queryData['questTypeName'] = $("#fenleiName").val();
        queryData['daren'] = $("#daren").val();
        const activeTab = $('.layui-tab-title .layui-this');
        console.log('当前激活的 Tab:', activeTab.text());
        if (activeTab.text().indexOf("我创建") !== -1) {
            // 延迟到切换动画完成后执行
            table.reload(BizWorkorder.tableId, {where: queryData});
        }else{
            table.reload(BizReceiveWorkorder.tableId, {where: queryData});
        }
    };

    /**
     * 弹出添加对话框
     */
    BizWorkorder.openAddDlg = function () {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加客服工单',
            content: Feng.ctxPath + '/bizWorkorder/bizWorkorder_add',
            end: function () {
                layer.closeAll('loading');
                table.reload(BizWorkorder.tableId);
            }
        });
    };


    BizWorkorder.online = function (data) {

        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizWorkorder/customerOnline", function (data) {
                Feng.success("切换成功!");

                window.location.href = Feng.ctxPath + "/im/customer/toImWorkspace";

            }, function (data) {
                Feng.error("切换失败!" + data.responseJSON.message + "!");
            });

            ajax.start();
        };
        Feng.confirm("是否切换登录状态?", operation);
    };


    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    BizWorkorder.openEditDlg = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '修改客服工单',
            content: Feng.ctxPath + '/bizWorkorder/bizWorkorder_update?bizWorkorderId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BizWorkorder.tableId);
            }
        });
    };


    BizWorkorder.zhuanfa = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '转发客服工单',
            content: Feng.ctxPath + '/bizWorkorder/bizWorkorder_zhuanfa?bizWorkorderId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BizWorkorder.tableId);
            }
        });
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    BizWorkorder.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizWorkorder/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(BizWorkorder.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("bizWorkorderId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 客服工单详情
     */
    BizWorkorder.detail = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '客服工单详情',
            content: Feng.ctxPath + '/bizWorkorder/bizWorkorder_detail?bizWorkorderId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BizWorkorder.tableId);
            }
        });
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BizWorkorder.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        BizWorkorder.openAddDlg();
    });

    // 搜索按钮点击事件
    $('#btnOnline').click(function () {
        BizWorkorder.online();
    });


    BizWorkorder.revote = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizWorkorder/revote", function (data) {
                Feng.success("撤回成功!");
                table.reload(BizWorkorder.tableId);
            }, function (data) {
                Feng.error("撤回失败!" + data.responseJSON.message + "!");
            });
            ajax.set("bizWorkorderId", data.id);
            ajax.start();
        };
        Feng.confirm("是否撤回?", operation);
    };
    // 操作按钮事件
    table.on('tool('+BizWorkorder.tableId+')', function (obj) {
        var data = obj.data;
        if (obj.event === 'edit') {
            BizWorkorder.openEditDlg(data)
        } else if (obj.event === 'revoke') {
            layer.confirm('确定要撤回该工单吗？', function (index) {
                BizWorkorder.revote(data);
                layer.close(index);
            });
        } else if (obj.event === 'forward') {
            BizWorkorder.zhuanfa(data)
        } else if (obj.event === 'view') {
            // 弹出详情层
            BizWorkorder.detail(data);
        }
    });


    // 操作按钮事件
    table.on('tool('+BizReceiveWorkorder.tableId+')', function (obj) {
        var data = obj.data;
        if (obj.event === 'forward') {
            BizWorkorder.zhuanfa(data)
        } else if (obj.event === 'view') {
            // 弹出详情层
            BizWorkorder.detail(data);
        }
    });
});


