/**
 * 详情对话框
 */
var BizWorkorderInfoDlg = {
    data: {
       id:"",
       questType:"",
       questTypeName:"",
       title:"",
       content:"",
       questStatus:"",
       questStatusname:"",
       urgency:"",
       importance:"",
       customerId:"",
       customerName:"",
       infucerId:"",
       infucerName:"",
       createTime:"",
       updateTime:"",
    }
};
/**
 * 详情对话框
 */
layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#createTime'
    });
    laydate.render({
        elem: '#updateTime'
    });
});


layui.use(['form', 'ax', 'upload', 'table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var BizWorkorder = {
        tableId: "bizWorkorderTable"
    };
    var destReqUrl = Feng.ctxPath +"/bizWorktype/list?limit=200&page=0";
    selector.init(destReqUrl,'questType','id','typeName','questTypeName');


    var destReqUrl = Feng.ctxPath +"/bizCustomer/list?limit=800&page=0";
    selector.init(destReqUrl,'customerId','id','nickName','customerName');
    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizWorkorder/detail?bizWorkorderId=" + Feng.getUrlParam("bizWorkorderId"));
    var result = ajax.start();
    form.val('bizWorkorderForm', result.data);

    //图片赋值
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/bizWorkorder/zhuanfa", function (data) {
            Feng.success("转发成功！");
            setTimeout(function () {
                top.layer.closeAll();
            }, 1000);
        }, function (data) {
            Feng.error("转发失败！" + data.responseJSON.message);
            setTimeout(function () {
                top.layer.closeAll();
            }, 1000);
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizWorkorder";
    });
});
