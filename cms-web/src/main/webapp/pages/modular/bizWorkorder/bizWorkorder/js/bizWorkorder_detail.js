/**
 * 详情对话框
 */
var BizWorkorderInfoDlg = {
    data: {
        id: "",
        questType: "",
        questTypeName: "",
        title: "",
        content: "",
        questStatus: "",
        questStatusname: "",
        urgency: "",
        importance: "",
        customerId: "",
        customerName: "",
        infucerId: "",
        infucerName: "",
        createTime: "",
        updateTime: "",
    }
};
layui.use(['form', 'ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizWorkorder/detail?bizWorkorderId=" + Feng.getUrlParam("bizWorkorderId"));
    var result = ajax.start();
    form.val('bizWorkorderForm', result.data);
    renderImages(result.data.imgList);

    //图片赋值
    $("#id").attr("disabled", "disabled");
    $("#questType").attr("disabled", "disabled");
    $("#questTypeName").attr("disabled", "disabled");
    $("#title").attr("disabled", "disabled");
    $("#content").attr("disabled", "disabled");
    $("#questStatus").attr("disabled", "disabled");
    $("#questStatusname").attr("disabled", "disabled");
    $("#urgency").attr("disabled", "disabled");
    $("#importance").attr("disabled", "disabled");
    $("#customerId").attr("disabled", "disabled");
    $("#customerName").attr("disabled", "disabled");
    $("#infucerId").attr("disabled", "disabled");
    $("#infucerName").attr("disabled", "disabled");
    $("#createTime").attr("disabled", "disabled");
    $("#updateTime").attr("disabled", "disabled");
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizWorkorder";
    });


    // 渲染图片的方法
    function renderImages(imgList) {
        var container = document.getElementById('imgContainer');
        container.innerHTML = ''; // 清空容器

        if (!imgList || imgList.length === 0) {
            container.innerHTML = '<div class="layui-text">暂无图片</div>';
            return;
        }

        imgList.forEach(function (imgUrl) {
            var imgBox = document.createElement('div');
            imgBox.className = 'layui-upload-list';
            imgBox.style = 'display: inline-block; margin-right: 10px;';

            var imgElement = document.createElement('img');
            imgElement.src = imgUrl;
            imgElement.style = 'max-width: 100px; max-height: 100px; cursor: pointer;';
            imgElement.onclick = function () {
                // 点击图片放大查看
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 1,
                    shadeClose: true,
                    area: ['80%', '80%'],
                    content: '<img src="' + imgUrl + '" style="width: 100%; height: 100%;">'
                });
            };

            imgBox.appendChild(imgElement);
            container.appendChild(imgBox);
        });
    }
});

