@layout("/common/_container.html",{js:["/pages/modular/bizAppPolicy/bizAppPolicy/js/bizAppPolicy_detail.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">政策管理详情</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizAppPolicyForm" lay-filter="bizAppPolicyForm" class="layui-form model-form" style="max-width: 700px;margin: 40px auto;">
                <input name="" type="hidden"/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">编号</label>
                        <div class="layui-input-inline">
                            <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">政策名称</label>
                        <div class="layui-input-inline">
                            <input id="policy" name="policy" placeholder="请输入policy" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">政策状态</label>
                        <div class="layui-input-inline">
                            <select id="policyStatus" name="policyStatus" placeholder="请输入policyStatus">
                                <option value="">全部</option>
                                <option value="1">启动</option>
                                <option value="-1">关闭</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">排序</label>
                        <div class="layui-input-inline">
                            <input id="sortNum" name="sortNum" placeholder="请输入sortNum" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">添加人</label>
                        <div class="layui-input-inline">
                            <input id="createName" name="createName" placeholder="请输入createName" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">创建时间</label>
                        <div class="layui-input-inline">
                            <input id="createTime" name="createTime" placeholder="请输入createTime" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">更新时间</label>
                        <div class="layui-input-inline">
                            <input id="updateTime" name="updateTime" placeholder="请输入updateTime" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                    </div>
            </form>
        </div>
    </div>
</div>
@}
