layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var BizAppPolicy = {
        tableId: "bizAppPolicyTable"
    };

    /**
     * 初始化表格的列
     */
    BizAppPolicy.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {field: 'id', minWidth: 100, hide: false, title: '编号'},
            {field: 'policy', minWidth: 100, hide: false, title: '政策名称'},
            {
                field: 'policyStatus', minWidth: 100, title: '政策状态', templet: function (d) {
                    return statusMap[d.policyStatus] || '未知状态';
                }
            },
            {field: 'sortNum', minWidth: 100, hide: false, title: '排序字段'},
            {field: 'createId', minWidth: 100, hide: true, title: '添加人'},
            {field: 'createName', minWidth: 100, hide: false, title: '添加人'},
            {field: 'createTime', minWidth: 100, hide: false, title: '创建时间'},
            {field: 'updateTime', minWidth: 100, hide: false, title: '更新时间'},
            {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BizAppPolicy.tableId,
        url: Feng.ctxPath + '/bizAppPolicy/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: BizAppPolicy.initColumn()
    });
    var statusMap = {
        1: '启动',
        '-1': '关闭'
    };
    /**
     * 点击查询按钮
     */
    BizAppPolicy.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(BizAppPolicy.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    BizAppPolicy.openAddDlg = function () {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加政策管理',
            content: Feng.ctxPath + '/bizAppPolicy/bizAppPolicy_add',
            end: function () {
                layer.closeAll('loading');
                table.reload(BizAppPolicy.tableId);
            }
        });
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    BizAppPolicy.openEditDlg = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加政策管理',
            content: Feng.ctxPath + '/bizAppPolicy/bizAppPolicy_update?bizAppPolicyId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BizAppPolicy.tableId);
            }
        });
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    BizAppPolicy.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizAppPolicy/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(BizAppPolicy.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("bizAppPolicyId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 政策管理详情
     */
    BizAppPolicy.detail = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '政策管理详情',
            content: Feng.ctxPath + '/bizAppPolicy/bizAppPolicy_detail?bizAppPolicyId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BizAppPolicy.tableId);
            }
        });
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BizAppPolicy.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        BizAppPolicy.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + BizAppPolicy.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            BizAppPolicy.openEditDlg(data);
        } else if (layEvent === 'delete') {
            BizAppPolicy.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            BizAppPolicy.detail(data);
        }
    });
});


