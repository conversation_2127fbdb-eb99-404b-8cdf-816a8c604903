/**
 * 详情对话框
 */
var SettleOrderInfoDlg = {
    data: {
       id:"",
       projectId:"",
       projectName:"",
       companyTalentRatio:"",
       settlePnum:"",
       liushuiAmount:"",
       yingfaAmount:"",
       yifaAmount:"",
       withdrawnAmount:"",
       unwithdrawnAmount:"",
       arriveAmount:"",
       noarriveAmount:"",
       feibiaoPnum:"",
       feibiaoRate:"",
       feibiaoAmount:"",
       feibiaoAmountRate:"",
       batchStatus:"",
       batchStatusName:"",
       jisuanFile:"",
       fujianFiles:"",
       remark:"",
       groupName:"",
       businessContact:"",
       statusLog:"",
       createId:"",
       createName:"",
       deptId:"",
       deptName:"",
       companyId:"",
       companyName:"",
       updateTime:"",
       createTime:"",
    }
};
layui.use(['form','ax','table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var table = layui.table;
    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/settleOrder/detail?settleOrderId=" + Feng.getUrlParam("settleOrderId"));
    var result = ajax.start();
     form.val('settleOrderForm', result.data);

    //图片赋值
    $("#id").attr("disabled","disabled");
    $("#projectId").attr("disabled","disabled");
    $("#projectName").attr("disabled","disabled");
    $("#companyTalentRatio").attr("disabled","disabled");
    $("#settlePnum").attr("disabled","disabled");
    $("#liushuiAmount").attr("disabled","disabled");
    $("#yingfaAmount").attr("disabled","disabled");
    $("#yifaAmount").attr("disabled","disabled");
    $("#withdrawnAmount").attr("disabled","disabled");
    $("#unwithdrawnAmount").attr("disabled","disabled");
    $("#arriveAmount").attr("disabled","disabled");
    $("#noarriveAmount").attr("disabled","disabled");
    $("#feibiaoPnum").attr("disabled","disabled");
    $("#feibiaoRate").attr("disabled","disabled");
    $("#feibiaoAmount").attr("disabled","disabled");
    $("#feibiaoAmountRate").attr("disabled","disabled");
    $("#batchStatus").attr("disabled","disabled");
    $("#batchStatusName").attr("disabled","disabled");
    $("#jisuanFile").attr("disabled","disabled");
    $("#fujianFiles").attr("disabled","disabled");
    $("#remark").attr("disabled","disabled");
    $("#groupName").attr("disabled","disabled");
    $("#businessContact").attr("disabled","disabled");
    $("#statusLog").attr("disabled","disabled");
    $("#createId").attr("disabled","disabled");
    $("#createName").attr("disabled","disabled");
    $("#deptId").attr("disabled","disabled");
    $("#deptName").attr("disabled","disabled");
    $("#companyId").attr("disabled","disabled");
    $("#companyName").attr("disabled","disabled");
    $("#updateTime").attr("disabled","disabled");
    $("#createTime").attr("disabled","disabled");
    //返回按钮

    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/settleOrder";
    });


    /**
     * 字典类型表管理
     */
    var SettleBatch = {
        tableId: "settleBatchTable"
    };

    /**
     * 初始化表格的列
     */
    SettleBatch.initColumn = function () {
        return [[

            {type: 'checkbox'},
            {
                field: 'id', minWidth: 100, hide: false, title: '序号', templet: function (d) {
                    return d.settleBatch.id;
                }
            },
            {
                field: 'projectName', minWidth: 100, hide: false, title: '项目简称', templet: function (d) {
                    return d.settleBatch.projectName;
                }
            },


            {
                field: 'batchNo', minWidth: 100, hide: false, title: '结算批次', templet: function (d) {
                    return d.settleBatch.batchNo;
                }
            },


            {
                field: 'settlementCycleName', minWidth: 100, hide: false, title: '结算周期', templet: function (d) {
                    return d.settleProjects.settlementCycleName;
                }
            },


            {
                field: 'commissionRate', minWidth: 100, hide: false, title: '标准分成比例', templet: function (d) {
                    const comp = d.settleProjects.commissionToComp;
                    const daren = d.settleProjects.commissionToDaren;

                    // 判断是否为有效值（包括数字0）
                    const isValid = (val) => val !== undefined && val !== null && val !== '';

                    if (isValid(comp) && isValid(daren)) {
                        return comp + '|' + daren;
                    } else {
                        return isValid(comp) ? comp : (isValid(daren) ? daren : '');
                    }
                }
            },

            {
                field: 'settlePnum', minWidth: 40, hide: false, title: '参与结算人数', templet: function (d) {
                    return d.settleBatch.settlePnum;
                }
            },

            {
                field: 'liushuiAmount', minWidth: 100, hide: false, title: '总营收金额', templet: function (d) {
                    return d.settleBatch.liushuiAmount;
                }
            },


            {
                field: 'yingfaAmount', minWidth: 100, hide: false, title: '应发放收益', templet: function (d) {
                    return d.settleBatch.yingfaAmount;
                }
            },
            {
                field: 'yifaAmount', minWidth: 100, hide: false, title: '已发放权益', templet: function (d) {
                    return d.settleBatch.yifaAmount;
                }
            },
            {
                field: 'withdrawnAmount', minWidth: 100, hide: false, title: '已提现金额', templet: function (d) {
                    return d.settleBatch.withdrawnAmount;
                }
            },
            {
                field: 'settleOrder.unwithdrawnAmount', minWidth: 100, hide: false, title: '未提现金额', templet: function (d) {
                    return d.settleBatch.unwithdrawnAmount;
                }
            },
            {
                field: 'arriveAmount', minWidth: 100, hide: false, title: '已到账金额', templet: function (d) {
                    return d.settleBatch.arriveAmount;
                }
            },
            {
                field: 'noarriveAmount', minWidth: 100, hide: false, title: '未到账金额', templet: function (d) {
                    return d.settleBatch.noarriveAmount;
                }
            },

            {
                field: 'feibiaoPnum', minWidth: 100, hide: false, title: '非标结算人数及比例', templet: function (d) {
                    const so = d.settleBatch || {};  // 防止 settleOrder 未定义
                    const amount = so.feibiaoPnum;
                    const rate = so.feibiaoRate;

                    // 判断字段是否有有效值（支持数字0、字符串非空等场景）
                    const hasAmount = amount != null && String(amount).trim() !== '';
                    const hasRate = rate != null && String(rate).trim() !== '';

                    // 动态拼接逻辑
                    return [hasAmount ? amount : null, hasRate ? rate : null]
                        .filter(x => x !== null)
                        .join('|');
                }
            },

            {
                field: 'feibiaoAmount', minWidth: 100, hide: false, title: '非标结算金额及比例', templet: function (d) {
                    const so = d.settleBatch || {};  // 防止 settleOrder 未定义
                    const amount = so.feibiaoAmount;
                    const rate = so.feibiaoAmountRate;

                    // 判断字段是否有有效值（支持数字0、字符串非空等场景）
                    const hasAmount = amount != null && String(amount).trim() !== '';
                    const hasRate = rate != null && String(rate).trim() !== '';

                    // 动态拼接逻辑
                    return [hasAmount ? amount : null, hasRate ? rate : null]
                        .filter(x => x !== null)
                        .join('|');
                }
            },

            {
                field: 'feibiaoPnum', minWidth: 100, hide: false, title: '结算人数异常比例', templet: function (d) {
                    const so = d.settleBatch || {};  // 防止 settleOrder 未定义
                    const count = so.feibiaoPnum;
                    const rate = so.feibiaoRate;

                    // 判断字段是否有有效值（支持数字0、字符串非空等场景）
                    const hasCount = count != null && String(count).trim() !== '';
                    const hasRate = rate != null && String(rate).trim() !== '';

                    // 动态拼接逻辑
                    return [hasCount ? count : null, hasRate ? rate : null]
                        .filter(x => x !== null)
                        .join('|');
                }
            },
            {
                field: 'sendSms', minWidth: 100, hide: false, title: '发送短信', templet: function (d) {
                    if(d.settleBatch.sendSms == 1){
                        return "已发送"
                    }else{
                        return "未发送"
                    }

                }
            },
            {field: 'd.showLb', minWidth: 100, hide: true, title: 'd.showLb', templet: function (d) {
                    return d.showLb
                }},
            {field: 'd.showCw', minWidth: 100, hide: true, title: 'd.showCw', templet: function (d) {
                    return d.showCw
                }},
            {field: 'batchStatusName', minWidth: 100, hide: false, title: '批次状态', templet: function (d) {
                    const so = d.settleBatch || {};
                    return so.batchStatusName;

                }
            },
            {field: 'confirmStatusName', minWidth: 100, hide: false, title: '结算状态', templet: function (d) {
                    const so = d.settleBatch || {};
                    return so.confirmStatusName;
                }
            },
            {field: 'id', minWidth: 20, hide: true, title: '主键ID（自增）'},
            {field: 'batchNo', minWidth: 100, hide: true, title: '批次号'},
            {field: 'projectId', minWidth: 100, hide: true, title: '项目ID'},
            {field: 'projectName', minWidth: 100, hide: true, title: '项目简称'},
            {field: 'zhouqiStart', minWidth: 100, hide: true, title: '周期开始时间'},
            {field: 'zhouqiEnd', minWidth: 100, hide: true, title: '周期结束时间'},
            {field: 'companyTalentRatio', minWidth: 100, hide: true, title: '公司与达人分成比例（示例：70%/30%）'},
            {field: 'settlePnum', minWidth: 100, hide: true, title: '参与结算人数'},
            {field: 'liushuiAmount', minWidth: 100, hide: true, title: '流水金额'},
            {field: 'yingfaAmount', minWidth: 100, hide: true, title: '应发放权益'},
            {field: 'yifaAmount', minWidth: 100, hide: true, title: '已发放权益'},
            {field: 'withdrawnAmount', minWidth: 100, hide: true, title: '已提现金额'},
            {field: 'unwithdrawnAmount', minWidth: 100, hide: true, title: '待提现金额（计算字段）'},
            {field: 'arriveAmount', minWidth: 100, hide: true, title: '已到账金额'},
            {field: 'noarriveAmount', minWidth: 100, hide: true, title: '未到账金额'},
            {field: 'feibiaoPnum', minWidth: 100, hide: true, title: '非标结算人数'},
            {field: 'feibiaoRate', minWidth: 100, hide: true, title: '非标人数比例'},
            {field: 'feibiaoAmount', minWidth: 100, hide: true, title: '非标金额'},
            {field: 'feibiaoAmountRate', minWidth: 100, hide: true, title: '非标金额比例'},
            {field: 'batchStatus', minWidth: 100, hide: true, title: '批次状态'},
            {field: 'confirmStatus', minWidth: 100, hide: true, title: '结算状态'},
            {field: 'jisuanFile', minWidth: 100, hide: true, title: '结算单'},
            {field: 'fujianFiles', minWidth: 100, hide: true, title: '附件'},
            {field: 'remark', minWidth: 100, hide: true, title: '备注'},
            {field: 'groupName', minWidth: 100, hide: true, title: '运营小组名称'},
            {field: 'businessContact', minWidth: 100, hide: true, title: '对接商务姓名'},
            {field: 'statusLog', minWidth: 100, hide: true, title: '状态变更流水（JSON格式记录操作日志）'},
            {field: 'createId', minWidth: 100, hide: true, title: '创建人ID'},
            {field: 'createName', minWidth: 100, hide: true, title: '创建人'},
            {field: 'deptId', minWidth: 100, hide: true, title: '部门ID'},
            {field: 'deptName', minWidth: 100, hide: true, title: '部门'},
            {field: 'companyId', minWidth: 100, hide: true, title: '公司ID'},
            {field: 'companyName', minWidth: 100, hide: true, title: '公司名称'},
            {field: 'updateTime', minWidth: 100, hide: true, title: '更新时间'},
            {field: 'createTime', minWidth: 100, hide: true, title: '创建时间'},
            {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 350, title: '操作'}
        ]];
    };

    setFilesVal(result.data.settleProjects)
    // 渲染表格 无机构收益
    var tableResult = table.render({
        elem: '#' + SettleBatch.tableId,
        url: Feng.ctxPath + '/settleBatch/btlist?projectId='+result.data.settleProjects.id+"&jigouShouyi=2",
        page: true,
        height: 260,
        limit: 5,
        cols: SettleBatch.initColumn()
    });
// 工具条点击事件
    table.on('tool(' + SettleBatch.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'darenDetail') {
            SettleBatch.darenDetail(data);
        } else if (layEvent === 'delete') {
            SettleBatch.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            SettleBatch.detail(data);
        }else if(layEvent === 'cwAudit'){
            //cwAudit
            SettleBatch.cwAudit(data);
        }else if(layEvent === 'lbAudit'){
            //cwAudit
            SettleBatch.lbAudit(data);
        } else if(layEvent === 'businessConfirmApproval') {
            SettleBatch.businessConfirmApproval(data);
        } else if(layEvent === 'financeConfirmApproval') {
            SettleBatch.financeConfirmApproval(data);
        } else if(layEvent === 'dowloadJiesuan') {
            SettleBatch.downloadJiesuan(data);
        }
    });
    //有机构收益
    var tableResult2 = table.render({
        elem: '#settleBatchTableJigou',
        url: Feng.ctxPath + '/settleBatch/btlist?projectId='+result.data.settleProjects.id+"&jigouShouyi=1",
        page: true,
        height: 260,
        limit: 5,
        cols: SettleBatch.initColumn()
    });

    table.on('tool(settleBatchTableJigou)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'darenDetail') {
            SettleBatch.darenDetail(data);
        } else if (layEvent === 'detail') {
            SettleBatch.detail(data);
        }else if(layEvent === 'cwAudit'){
            //cwAudit
            SettleBatch.cwAudit(data);
        }else if(layEvent === 'lbAudit'){
            //cwAudit
            SettleBatch.lbAudit(data);
        } else if(layEvent === 'dowloadJiesuan') {
            SettleBatch.downloadJiesuan(data);
        }
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function (e) {
        e.preventDefault();  // 阻止默认跳转
        SettleBatch.openAddDlg();
    });

    SettleBatch.openAddDlg = function () {
        window.location.href = Feng.ctxPath + '/settleOrder/settleOrder_add?t=' + Date.now()+'&settleOrderId='+Feng.getUrlParam("settleOrderId");
    };

    SettleBatch.cwAudit = function (data) {
        window.location.href = Feng.ctxPath + '/settleBatch/settBatchCw?cw=1&settleBatchId=' + data.settleBatch.id;
    };

    SettleBatch.lbAudit = function (data) {
        window.location.href = Feng.ctxPath + '/settleBatch/settBatchCw?cw=2&settleBatchId=' + data.settleBatch.id;
    };

    SettleBatch.darenDetail = function (data){
        window.location.href = Feng.ctxPath + '/settleInfluencerOrder/idx?settleBatchId=' + data.settleBatch.id;
    }

    SettleBatch.businessConfirmApproval = function (data) {
        window.location.href = Feng.ctxPath + '/settleBatchConfirm/confirmView?type=1&settleBatchId=' + data.settleBatch.id;
    };

    SettleBatch.financeConfirmApproval = function (data) {
        window.location.href = Feng.ctxPath + '/settleBatchConfirm/confirmView?type=2&settleBatchId=' + data.settleBatch.id;
    };

    /**
     * 下载结算单
     */
    SettleBatch.downloadJiesuan = function (data) {
        try {
            // 检查是否有结算文件
            var jisuanFile = data.settleBatch.jisuanFile;
            if (!jisuanFile || jisuanFile.trim() === '') {
                Feng.error("该批次暂无结算文件可下载");
                return;
            }

            // 构建下载URL
            var downloadUrl = Feng.getAliImgUrl(jisuanFile, null, null, 1);

            // 创建隐藏的下载链接并触发下载
            var $downloadLink = $('<a>', {
                href: downloadUrl,
                download: jisuanFile,
                style: 'display: none;'
            });

            $('body').append($downloadLink);
            $downloadLink[0].click(); // 使用原生DOM点击
            $downloadLink.remove();

            console.log("开始下载结算文件: " + jisuanFile);

        } catch (error) {
            console.error("下载结算文件失败:", error);
            Feng.error("下载结算文件失败，请稍后重试");
        }
    };

});
