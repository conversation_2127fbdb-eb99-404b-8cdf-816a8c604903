layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var SettleOrder = {
        tableId: "settleOrderTable"
    };

    /**
     * 初始化表格的列
     */
    SettleOrder.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {
                field: 'id', minWidth: 100, hide: false, title: '序号', templet: function (d) {
                    return d.settleOrder.id;
                }
            },
            {
                field: 'projectName', minWidth: 100, hide: false, title: '项目简称', templet: function (d) {
                    return d.settleOrder.projectName;
                }
            },

            {
                field: 'projectNo', minWidth: 100, hide: false, title: '项目编号', templet: function (d) {
                    return d.settleProjects.projectCode;
                }
            },
            {
                field: 'projectTypeName', minWidth: 100, hide: false, title: '项目类型', templet: function (d) {
                    return d.settleProjects.projectTypeName;
                }
            },
            {
                field: 'platform', minWidth: 100, hide: false, title: '服务平台', templet: function (d) {
                    const platforms = [
                        d.settleProjects.yijiPlatform,  // 一级平台
                        d.settleProjects.erjiPlatform,  // 二级平台
                        d.settleProjects.sanjiPlatform   // 三级平台
                    ].filter(Boolean); // 过滤空值

                    return platforms.join('-'); // 用 "-" 连接非空字段
                }
            },

            {
                field: 'settleBatchStatusName', minWidth: 100, hide: false, title: '审批状态', templet: function (d) {
                    return d.settleBatchStatusName;
                }
            },

            {
                field: 'settlementCycleName', minWidth: 100, hide: false, title: '结算周期', templet: function (d) {
                    return d.settleProjects.settlementCycleName;
                }
            },


            {
                field: 'businessOwnerName', minWidth: 100, hide: false, title: '业务负责人', templet: function (d) {
                    return d.settleProjects.businessOwnerName;
                }
            },

            {
                field: 'commissionRate', minWidth: 100, hide: false, title: '标准分成比例', templet: function (d) {
                    const comp = d.settleProjects.commissionToComp;
                    const daren = d.settleProjects.commissionToDaren;

                    // 判断是否为有效值（包括数字0）
                    const isValid = (val) => val !== undefined && val !== null && val !== '';

                    if (isValid(comp) && isValid(daren)) {
                        return comp + '|' + daren;
                    } else {
                        return isValid(comp) ? comp : (isValid(daren) ? daren : '');
                    }
                }
            },

            {
                field: 'settlePnum', minWidth: 100, hide: false, title: '参与结算人数', templet: function (d) {
                    return d.settleOrder.settlePnum;
                }
            },

            {
                field: 'liushuiAmount', minWidth: 100, hide: false, title: '总营收金额', templet: function (d) {
                    return d.settleOrder.liushuiAmount;
                }
            },


            {
                field: 'yingfaAmount', minWidth: 100, hide: false, title: '应发放收益', templet: function (d) {
                    return d.settleOrder.yingfaAmount;
                }
            },
            {
                field: 'withdrawnAmount', minWidth: 100, hide: false, title: '已提现金额', templet: function (d) {
                    return d.settleOrder.withdrawnAmount;
                }
            },
            {
                field: 'settleOrder.unwithdrawnAmount', minWidth: 100, hide: false, title: '未提现金额', templet: function (d) {
                    return d.settleOrder.unwithdrawnAmount;
                }
            },
            {
                field: 'arriveAmount', minWidth: 100, hide: false, title: '已到账金额', templet: function (d) {
                    return d.settleOrder.arriveAmount;
                }
            },
            {
                field: 'noarriveAmount', minWidth: 100, hide: false, title: '未到账金额', templet: function (d) {
                    return d.settleOrder.noarriveAmount;
                }
            },

            {
                field: 'feibiaoPnum', minWidth: 100, hide: false, title: '非标结算人数及比例',templet: function (d) {
                    const so = d.settleOrder || {};  // 防止 settleOrder 未定义
                    const amount = so.feibiaoPnum;
                    const rate = so.feibiaoRate;

                    // 判断字段是否有有效值（支持数字0、字符串非空等场景）
                    const hasAmount = amount != null && String(amount).trim() !== '';
                    const hasRate = rate != null && String(rate).trim() !== '';

                    // 动态拼接逻辑
                    return [hasAmount ? amount : null, hasRate ? rate : null]
                        .filter(x => x !== null)
                        .join('|');
                }
            },

            {
                field: 'feibiaoAmount', minWidth: 100, hide: false, title: '非标结算金额及比例',  templet: function (d) {
                    const so = d.settleOrder || {};  // 防止 settleOrder 未定义
                    const amount = so.feibiaoAmount;
                    const rate = so.feibiaoAmountRate;

                    // 判断字段是否有有效值（支持数字0、字符串非空等场景）
                    const hasAmount = amount != null && String(amount).trim() !== '';
                    const hasRate = rate != null && String(rate).trim() !== '';

                    // 动态拼接逻辑
                    return [hasAmount ? amount : null, hasRate ? rate : null]
                        .filter(x => x !== null)
                        .join('|');
                }
            },

            {
                field: 'feibiaoAmount', minWidth: 100, hide: false, title: '结算人数异常比例',  templet: function (d) {
                    const so = d.settleOrder || {};  // 防止 settleOrder 未定义
                    const amount = so.feibiaoAmount;
                    const rate = so.feibiaoAmountRate;

                    // 判断字段是否有有效值（支持数字0、字符串非空等场景）
                    const hasAmount = amount != null && String(amount).trim() !== '';
                    const hasRate = rate != null && String(rate).trim() !== '';

                    // 动态拼接逻辑
                    return [hasAmount ? amount : null, hasRate ? rate : null]
                        .filter(x => x !== null)
                        .join('|');
                }
            },

            {field: 'd.showLb', minWidth: 100, hide: true, title: 'd.showLb', templet: function (d) {
                    return d.showLb
                }},
            {field: 'd.showCw', minWidth: 100, hide: true, title: 'd.showCw', templet: function (d) {
                    return d.showCw
                }},

            {field: 'projectId', minWidth: 100, hide: true, title: '项目ID'},
            {field: 'projectId', minWidth: 100, hide: true, title: '项目ID'},
            {field: 'companyTalentRatio', minWidth: 100, hide: true, title: '公司与达人分成比例（示例：70%/30%）'},
            {field: 'liushuiAmount', minWidth: 100, hide: true, title: '流水金额'},
            {field: 'feibiaoPnum', minWidth: 100, hide: true, title: '非标结算人数'},
            {field: 'feibiaoRate', minWidth: 100, hide: true, title: '非标人数比例'},
            {field: 'feibiaoAmount', minWidth: 100, hide: true, title: '非标金额'},
            {field: 'feibiaoAmountRate', minWidth: 100, hide: true, title: '非标金额比例'},
            {field: 'batchStatus', minWidth: 100, hide: true, title: '批次状态'},
            {field: 'batchStatusName', minWidth: 100, hide: true, title: '批次状态'},
            {field: 'jisuanFile', minWidth: 100, hide: true, title: '结算单'},
            {field: 'fujianFiles', minWidth: 100, hide: true, title: '附件'},
            {field: 'remark', minWidth: 100, hide: true, title: '备注'},
            {field: 'groupName', minWidth: 100, hide: true, title: '运营小组名称'},
            {field: 'businessContact', minWidth: 100, hide: true, title: '对接商务姓名'},
            {field: 'statusLog', minWidth: 100, hide: true, title: '状态变更流水（JSON格式记录操作日志）'},
            {field: 'createId', minWidth: 100, hide: true, title: '创建人ID'},
            {field: 'createName', minWidth: 100, hide: true, title: '创建人'},
            {field: 'deptId', minWidth: 100, hide: true, title: '部门ID'},
            {field: 'deptName', minWidth: 100, hide: true, title: '部门'},
            {field: 'companyId', minWidth: 100, hide: true, title: '公司ID'},
            {field: 'companyName', minWidth: 100, hide: true, title: '公司名称'},
            {field: 'updateTime', minWidth: 100, hide: true, title: '更新时间'},
            {field: 'createTime', minWidth: 100, hide: true, title: '创建时间'},
            {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + SettleOrder.tableId,
        url: Feng.ctxPath + '/settleOrder/list',
        page: true,
        height: "full-200",
        limit: 20,
        cols: SettleOrder.initColumn()
    });
    /**
     * 点击查询按钮
     */
    SettleOrder.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(SettleOrder.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    SettleOrder.openAddDlg = function () {
        window.location.href = Feng.ctxPath + '/settleOrder/settleOrder_add?t=' + Date.now();
    };

    var destReqUrl = Feng.ctxPath + "/bizNomalEnums/list?projName=项目类型&limit=800&page=0";
    selector.init(destReqUrl, 'projectTypeName', 'keyValue', 'keyType', 'projectTypeId');
    var destReqUrl3 = Feng.ctxPath + "/bizPlatform/listPage?limit=800&page=0&cengji=1";
    selector.init(destReqUrl3, 'platform1', 'id', 'platName', 'platformName1');


    var destReqUrl3 = Feng.ctxPath + "/bizPlatform/listPage?limit=800&page=0&cengji=2";
    selector.init(destReqUrl3, 'platform2', 'id', 'platName', 'platformName2');

    var destReqUrl3 = Feng.ctxPath + "/bizPlatform/listPage?limit=800&page=0&cengji=3";
    selector.init(destReqUrl3, 'platform3', 'id', 'platName', 'platformName3');


    /**
     * 结算单详情
     */
    SettleOrder.detail = function (data) {
        window.location.href = Feng.ctxPath + '/settleOrder/settleOrder_detail?settleOrderId=' + data.settleOrder.id;
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function (e) {
        e.preventDefault();  // 阻止默认跳转
        SettleOrder.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function (e) {
        e.preventDefault();  // 阻止默认跳转
        SettleOrder.openAddDlg();
    });
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/settleOrder";
    });
    // 工具条点击事件
    table.on('tool(' + SettleOrder.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            SettleOrder.openEditDlg(data);
        } else if (layEvent === 'delete') {
            SettleOrder.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            SettleOrder.detail(data);
        }
    });
});


