/**
 * 详情对话框
 */
var SettleInfluencerOrderInfoDlg = {
    data: {
        id: "",
        influencerName: "",
        influencerId: "",
        projectId: "",
        projectName: "",
        phoneNumber: "",
        companyTalentRatio: "",
        settlementBatch: "",
        platformNickname: "",
        platformId: "",
        earningsAmount: "",
        payableAmount: "",
        withdrawnAmount: "",
        unwithdrawnAmount: "",
        isArrived: "",
        arrivalIssue: "",
        workArea: "",
        groupName: "",
        businessContact: "",
        statusLog: "",
        createId: "",
        createName: "",
        deptId: "",
        deptName: "",
        companyId: "",
        companyName: "",
        updateTime: "",
        createTime: "",
    }
};
layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#updateTime'
    });
    laydate.render({
        elem: '#createTime'
    });
    // 绑定日期选择器到指定输入框
    laydate.render({
        elem: '#zhouqiStart',
        trigger: 'click',
        done: function (value, date) {
            // 结束日期不能早于开始日期
            laydate.render({
                elem: '#zhouqiEnd',
                min: value
            });
        }
    });

});


layui.use(['form', 'ax', 'upload', 'table', 'element'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var SettleInfluencerOrder = {
        tableId: "settleInfluencerOrderTable"
    };

    var element = layui.element;
    element.render('step');

    function formatDate(date, fmt) {
        var shortDate = date.substring(0, 10);
        return shortDate;
    }

    function setStVal(settleProjectId) {
        var reqPurl = Feng.ctxPath + "/settleProjects/detail?settleProjectsId=" + settleProjectId;
        var ajax = new $ax(reqPurl, function (rpsData) {
            console.log('rpsData ' + rpsData)

            $("#projectCode").text(rpsData.data.projectCode);
            $("#createTime").text(formatDate(rpsData.data.createTime, "yyyy-MM-dd"));
            $("#projectStatusName").text(rpsData.data.projectStatusName);

            $("#zhouqi").text(formatDate(rpsData.data.projectStarttime, "yyyy-MM-dd") + "-" + formatDate(rpsData.data.projectEndtime, "yyyy-MM-dd"));
            $("#compName").text(rpsData.data.compName);
            var pltstr = rpsData.data.yijiPlatform;
            if (rpsData.data.erjiPlatform) {
                pltstr = pltstr + "-" + rpsData.data.erjiPlatform
                if (rpsData.data.sanjiPlatform) {
                    pltstr = pltstr + "-" + rpsData.data.sanjiPlatform
                }
            }
            $("#servicePlat").text((pltstr));
            $("#projectTypeName").text((rpsData.data.projectTypeName));
            var fenchegnStr = rpsData.data.commissionToComp;
            var darenFench = rpsData.data.commissionToDaren;

            if (darenFench) {
                fenchegnStr = fenchegnStr + "|" + darenFench
            }
            $("#businessOwnerName").text((rpsData.data.businessOwnerName));
            $("#createName").text((rpsData.data.createName));
            $("#quantTarget").text(rpsData.data.quantTarget);
            $("#settlementCycleName").text(rpsData.data.settlementCycleName);
            $("#revenueTypeName").text(rpsData.data.revenueTypeName);
            $("#commissionRate").text(fenchegnStr);

            // $("#agencyRevenue").text(rpsData.data.agencyRevenue);
            // //${item.settleProjects.agencyRevenue == 1 ? "有收益" : "无收益"}
            if(rpsData.data.agencyRevenue==1){
                $("#agencyRevenue").text("有收益");
            }else{
                $("#agencyRevenue").text("无收益");
            }
            $("#jigouName").text(rpsData.data.jigouName);
            $("#projectDesc").text(rpsData.data.projectDesc);
            $("#projectZhixingFa").text(rpsData.data.projectZhixingFa);
            $('#beijingFileDiv').empty();
            $('#zhixingFileDiv').empty();
            setFilesVal(rpsData.data);
        });
        ajax.start();
    }

    var destReqUrl3 = Feng.ctxPath + "/settleProjects/listYiLixiang?page=0&limit=10000";
    selector.init(destReqUrl3, 'project', 'id', 'projectName', 'projectName', function (data) {
        setStVal(data.value);
    });

    var projectIdVd = $("#projectIdVd").val();
    if (projectIdVd) {
        $("#project").val(projectIdVd);  // 设置值为北京的选项
        layui.form.render('select');        // 关键：重新渲染

        setStVal(projectIdVd);
    }

    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var loadIndex = layer.load(2, {shade: 0.5}); // 参数2表示加载图标样式
        $.ajax({
            url: Feng.ctxPath + "/settlecore/add",
            async: true, // 必须为异步
            data: data.field,
            success: function (data) {
                layer.close(loadIndex); // 关闭加载层
                Feng.success("添加成功！");
                window.location.href = Feng.ctxPath + "/settleOrder";
            },
            error: function (xhr, status, err) { // 新增错误处理
                layer.close(loadIndex); // 关闭加载层
                const errorData = xhr.responseJSON || {};
                const errorMsg = errorData.message || xhr.responseText || "未知错误";

                Feng.error("添加失败！" + errorMsg)
            }

        });

        // var ajax = new $ax(Feng.ctxPath + "/settlecore/add", function (data) {
        //     layer.close(loadIndex); // 关闭加载层
        //     Feng.success("添加成功！");
        //     window.location.href = Feng.ctxPath + "/settleOrder";
        // }, function (data) {
        //     layer.close(loadIndex); // 关闭加载层
        //     Feng.error("添加失败！" + data.responseJSON.message)
        //     window.location.href = Feng.ctxPath + "/settleOrder";
        // });

        return false;
    });

    //返回按钮
    $("#backupPage").click(function (e) {
        e.preventDefault();  // 阻止默认跳转
        window.location.href = Feng.ctxPath + "/settleOrder";
    });

    function resetHdVals() {
        var settleSrc = $('#fuzhuFile .file-item a').map(function () {
            return $(this).attr('srcSource');
        }).get().join(',');
        $("#fuzhuFiles").val(settleSrc);


        var settleSrcd = $('#fileList .file-item a').map(function () {
            return $(this).attr('srcSource');
        }).get().join(',');

        $("#jiesuanFile").val(settleSrcd);
    }

    //普通图片上传
    upload.render({
        elem: '#settleIdBut',
        url: Feng.ctxPath + '/oss/ff/uploadPri',
        accept: 'file',
        multiple: false,
        exts: 'xlsx|csv|xls|pdf',
        done: function (res) {
            $('#fileList').append(`
                <div class="file-item layui-inline">
                    <span class="file-name"><a class="layui-btn layui-btn-primary layui-btn-sm" href="` + res.data.src + `" srcSource="` + res.data.srcSource + `" download="` + res.data.title + `"> <i class="layui-icon layui-icon-download"></i>` + res.data.title + `</a></span>
                    <i class="layui-icon layui-icon-close delete-btn" data-id="` + res.data.title + `"></i>
                </div>
            `);

            resetHdVals();
            Feng.success(res.message);
        },

        error: function () {
            Feng.error("上传结算文件失败！");
        }
    });

    // 初始化上传组件‌:ml-citation{ref="8" data="citationList"}
    upload.render({
        elem: '#orderFuzhuUpdBut',
        multiple: true,
        accept: 'file',
        url: Feng.ctxPath + '/oss/ff/uploadPri',
        done: function (res) {
            // 插入带删除按钮的文件项‌:ml-citation{ref="5" data="citationList"}
            $('#fuzhuFile').append(`
                <div class="file-item layui-inline">
                    <span class="file-name"><a class="layui-btn layui-btn-primary layui-btn-sm" href="` + res.data.src + `" srcSource="` + res.data.srcSource + `" download="` + res.data.title + `"> <i class="layui-icon layui-icon-download"></i>` + res.data.title + `</a></span>
                    <i class="layui-icon layui-icon-close delete-btn" data-id="` + res.data.title + `"></i>
                </div>
            `);

            resetHdVals();
            Feng.success(res.message);
        }
    });

});
