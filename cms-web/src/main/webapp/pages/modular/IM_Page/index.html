<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服界面</title>
    @ include("/modular/IM_Page/js/cmn.js"){}
    <link rel="stylesheet" href="${ctxPath}/assets/common/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/common/module/admin.css" media="all"/>

    <link rel="stylesheet" href="${ctxPath}/pages/modular/IM_Page/css/index.css">
    <link rel="stylesheet" href="${ctxPath}/pages/modular/IM_Page/css/nav.css">
    <link rel="stylesheet" href="${ctxPath}/pages/modular/IM_Page/libs/zTree/css/zTreeStyle/zTreeStyle.css">

    <script type="text/javascript" src="${ctxPath}/assets/common/layui/layui.js"></script>
    <script type="text/javascript" src="${ctxPath}/assets/common/js/common.js"></script>
    <script type="text/javascript" src="${ctxPath}/assets/common/js/selector.js"></script>
    <script type="text/javascript" src="${ctxPath}/assets/common/js/init.js"></script>
    <script type="text/javascript" src="${ctxPath}/assets/expand/plugins/jquery/jquery-3.2.1.min.js"></script>
    <script type="text/javascript" src="${ctxPath}/assets/expand/plugins/ztree/jquery.ztree.all.min.js"></script>


    <script type="text/javascript" src="${ctxPath}/pages/modular/IM_Page/js/contactsTree.js"></script>
    <script type="text/javascript" src="${ctxPath}/pages/modular/IM_Page/js/nav.js"></script>

</head>

<body id="body">
<div class="main-container">
    <!-- 侧边导航 -->
    <div class="side-nav">
        <!-- 头部 -->
        <input type="hidden" id="imAccid">
        <input type="hidden" id="imToken">

        <div class="nav-header">
            <img src="${bizCustomerIm.imIconPic}" id="userAvata" class="nav-user-avatar">
            <div     id="customerName" class="user-name">${bizCustomerIm.customerName}</div>
        </div>

        <!-- 导航菜单 -->
        <div class="nav-menu">
            <div class="nav-item" name="jdz">
                <div class="icon-wrapper">
                    <i class="layui-icon nav-icon">&#xe60a;</i>
                    <span class="badge" id="jdsp">12</span>
                </div>
                <span class="nav-text">接待中</span>
            </div>
            <div class="nav-item active" name="pdz">
                <div class="icon-wrapper">
                    <i class="layui-icon nav-icon">&#xe629;</i>
                    <span class="badge" id="ddsp">3</span>
                </div>
                <span class="nav-text">排队中</span>
            </div>
            <div class="nav-item" name="yjd">
                <div class="icon-wrapper">
                    <i class="layui-icon nav-icon">&#xe63c;</i>
                    <span class="badge" id="yijidai">3</span>
                </div>
                <span class="nav-text">已接待</span>
            </div>
            <div class="nav-item" name="gd">
                <div class="icon-wrapper">
                    <i class="layui-icon nav-icon">&#xe60e;</i>
                    <span class="badge" id="gongdansp">5</span>
                </div>
                <span class="nav-text">工单</span>
            </div>
            <div class="nav-item" name="tj">
                <div class="icon-wrapper">
                    <i class="layui-icon nav-icon">&#xe62a;</i>
                </div>
                <span class="nav-text">统计</span>
            </div>
        </div>
    </div>
    <!-- 接待中 排队中 已接待 -->
    @ include("/modular/IM_Page/chat.html"){}
    <!-- 工单 -->
    @ include("/modular/IM_Page/workorderlist.html"){}
    <!-- 统计 -->
    @ include("/modular/IM_Page/tongji.html"){}
</div>
<!-- 编辑表单模板 -->
@ include("/modular/IM_Page/addworkorder.html"){}

<!-- 自定义图标样式 -->
<style>
    .ztree li span.button.user_ico_docu {
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyRpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoTWFjaW50b3NoKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo4MjRGRTg0QzA3NjgxMUUzQkZGNDhENEJFQjM2OTcyRiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo4MjRGRTg0RDA3NjgxMUUzQkZGNDhENEJFQjM2OTcyRiI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjgyNEZFODRBMDc2ODExRTNCRkY0OEQ0QkVCMzY5NzJGIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjgyNEZFODRCMDc2ODExRTNCRkY0OEQ0QkVCMzY5NzJGIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Y7ZvUAAAAXhJREFUeNpi/P//PwMlgImBQkC1AUxEDN7PQEQA/GcgbQALqQ5gZGRkZGNjY2RmZgYx/wMxAwMDIxMTE8jR/0FskFp2dnYwH6QOJA7Sx8jICPIM2ABGRkZGRUVFBhUVFQYJCQkGISEhBi4uLrABbGxsDOLi4gxSUlIMcnJyDAoKCmB1IL0gPSADwAaA1CkrKzPo6OgwaGtrM4iKioINAHkZ5HiQAXJycgzq6upgNSD1IL0gA8AGgFwK8qKUlBTY60JCQmADQC4FqQP5GuR7kDpZWVmwWpAekAFgA0BeBrkS5GJQeIACDORykAEgl4PkQeGhoKAADheQHpABYANA4QzyMigMQF4HhQUorEABBgoXkDgoHEDqQOED0gMyAGwAKHxALgZ5HeRyUJiAwgYUJiBxkDxIHUgtSA/IAGZGqmsnI3P+/ydNP8UJCBQvIAPABoDSSGNjI0NHRwc4mYLCB5RkQeIgcZA8SB1ILcgARlDyBqXZv3//gvM4KO2CwgQkDpIHqQOpBekBGQBOBqDkCUrHoHQMSsegtAxKy6C0DEq7oDABiYPkQepAakF6QAYAEGAAW+Qlvqamp0MAAAAASUVORK5CYII=) no-repeat center;
        width: 16px;
        height: 16px;
        margin-top: -3px;
    }
</style>

<script src="${ctxPath}/pages/modular/IM_Page/js/statistics.js"></script>
<!-- 序号列模板 -->
<script type="text/html" id="indexTpl">
    {{d.LAY_INDEX}}
</script>
</body>

</html>