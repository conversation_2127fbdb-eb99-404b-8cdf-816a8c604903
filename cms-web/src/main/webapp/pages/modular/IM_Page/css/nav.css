/* 紧凑导航容器 */
.side-nav {
    width: 80px;
    height: 100vh;
    background: #2f4050;
    color: #a7b1c2;
    text-align: center;
}

/* 头部区域 */
.nav-header {
    padding: 20px 0;
    border-bottom: 1px solid #293846;
}
.nav-user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid #fff;
    margin-bottom: 8px;
}
.user-name {
    color: #fff;
    font-size: 12px;
    line-height: 1.3;
    padding: 0 5px;
    word-break: break-all;
}

/* 导航菜单 */
.nav-menu {
    padding: 15px 0;
}
.nav-item {
    position: relative;
    padding: 12px 0;
    cursor: pointer;
    transition: all 0.3s;
}
.nav-item:hover {
    background: #293846;
}
.nav-item.active {
    background: #293846;
}

/* 图标容器 */
.icon-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 5px;
}
.nav-icon {
    font-size: 24px;
    display: block;
}

/* 消息提示 */
.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff5722;
    color: white;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 9px;
    font-size: 12px;
    padding: 0 4px;
}

/* 菜单文字 */
.nav-text {
    font-size: 12px;
    display: block;
    line-height: 1.2;
    padding: 0 3px;
}
.main-container {
    display: flex;
    height: 100vh; /* 关键点1：全屏高度 */
}
.side-nav {
    flex-shrink: 0; /* 关键点2：固定宽度 */
}
.main-container .container {
    flex: 1; /* 关键点3：占据剩余空间 */
}

/* 去除边框和滚动条 */
.layui-iframe {
    border: 0;
    width: 100%;
    height: 100%;
    display: block; /* 模仿 div 的块级特性 */
    background-color: #ffffff !important;
    overflow: hidden; /* 隐藏内部滚动条 */
}

/* 父容器模拟 div 容器样式 */
.iframe-wrapper {
    border-radius: 4px;
    with:100%;
    background-color: #ffffff !important;
    height:100%;
}
