* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    height: 100vh;
    /* 容器高度充满视口 */
}

.left {
    width: 300px;
    /* 不放大、不缩小、固定200px宽度 */
    background: #ffffff;
    box-sizing: border-box;
    position: relative;
}
.left .contacts {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 5px 0;
    text-align: center;
    font-size: 13px;
    border-top: 1px solid #eeeeee;
    cursor: pointer;
}
.left .contacts i {
    font-size: 13px;
}

.center {
    flex: 1;
    /* 占据剩余空间 */
    background: #99ff99;
    overflow: auto;
    /* 内容溢出时显示滚动条 */
}


/* 可选：为子元素添加内容演示高度撑满 */
.left,
.center,
.right {
    padding: 15px;
}
.layui-tab {
    margin: 0;
}
.right {
    flex: 0 0 300px;
    /* 固定300px宽度 */
    background: #ffffff;
    padding: 0;
}
.search-container {
    position: relative;
    width: 100%; /* 可根据需要调整宽度 */
}
.search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}
/* 调整输入框右侧留出图标空间 */
.layui-input {
    padding-right: 35px;
}

.layui-tab-title > li {
    width: 50% !important;
    box-sizing: border-box;
}

/* 聊天列表 */
.chat-list {
    display: flex;
    flex-direction: column;
}

.chat-item {
    display: flex;
    padding: 10px;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;
    position: relative;
}

.chat-item img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
    position: relative;
    top: 1px;
}

/* 消息提示圆圈 */
.chat-item .msg-tip {
    position: absolute;
    top: 10px;
    left: 37px;
    width: 14px;
    height: 14px;
    background-color: red;
    color: white;
    font-size: 12px;
    font-weight: bold;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.chat-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    width: 205px;
}

.tallBox {
    width: 15px;
    position: absolute;
    top: 3px;
    right: 9px;
}

.chat-info .name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.chat-info .message {
    font-size: 14px;
    color: #777;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.layui-tab-content {
    padding: 0;
}

.chat-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 600px;
    height: 100%;
    margin: 0 auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* -------------------------------------------------------------------------------------------------- */
.container .center {
    padding: 0;
}
#chatContainer {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f0f0f0;
}

/* ========== 消息列表区域 ========== */
#messageList {
    flex: 1;
    overflow-y: auto;
    padding: 20px 15px;
}

/* ========== 消息容器 ========== */
.message-container {
    position: relative;
    min-height: 46px;
    margin-bottom: 15px;
    clear: both;
}

/* ========== 头像样式 ========== */
.user-avatar {
    width: 38px;
    height: 38px;
    border-radius: 3px;
    position: absolute;
    bottom: 2px;
    z-index: 2;
}

/* ========== 消息方向布局 ========== */
/* 接收消息容器 */
.receive-container {
    float: left;
    width: calc(100% - 60px);
}
.receive-container .user-avatar {
    left: 0;
}

/* 发送消息容器 */
.send-container {
    float: right;
    width: calc(100% - 60px);
}
.send-container .user-avatar {
    right: 0;
}

/* ========== 气泡容器 ========== */
.message-bubble-wrapper {
    position: relative;
    min-height: 40px;
    display: flex;
    align-items: center;
}
.receive-container .message-bubble-wrapper {
    margin-left: 48px;
}
.send-container .message-bubble-wrapper {
    margin-right: 48px;
    justify-content: flex-end;
}

/* ========== 消息气泡 ========== */
.message-bubble {
    padding: 8px 12px;
    border-radius: 4px;
    position: relative;
    max-width: 480px;
    min-width: 40px;
    word-break: break-word;
    line-height: 1.5;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}

.receive-bubble {
    background: white;
    margin-left: 8px;
}

.send-bubble {
    background: #95ec69;
    margin-right: 8px;
}

/* ========== 气泡箭头 ========== */
.bubble-arrow {
    position: absolute;
    width: 0;
    height: 0;
    top: 50%;
    transform: translateY(-50%);
}

.receive-bubble .bubble-arrow {
    left: -6px;
    border-top: 6px solid transparent;
    border-right: 6px solid white;
    border-bottom: 6px solid transparent;
}

.send-bubble .bubble-arrow {
    right: -6px;
    border-top: 6px solid transparent;
    border-left: 6px solid #95ec69;
    border-bottom: 6px solid transparent;
}

/* ========== 时间戳 ========== */
.message-time {
    text-align: center;
    color: #999;
    font-size: 12px;
    margin: 18px 0;
    clear: both;
    position: relative;
    z-index: 1;
}

/* ========== 输入区域 ========== */
#inputArea {
    border: 1px solid #ddd;
    background: white;
    padding: 15px;
    box-sizing: border-box;
    position: relative;
}
#inputArea .icons {
    position: absolute;
    top: 3px;
    left: 20px;
    font-size: 15px;
    color: #666666;
    z-index: 999;
}
#inputArea .icons .addQuickReply {
    font-size: 12px;
    color: #333;
    background: #eee;
    padding: 2px;
    border: 1px solid #999999;
    border-radius: 3px;
    cursor: pointer;
}
#inputArea .icons .addQuickReply i {
    font-size: 12px;
}
#inputArea .icons i {
    padding: 0 5px;
}

#chatHead {
    border-bottom: 1px solid #ddd;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.chatHeadLeft {
    font-size: 12px;
    padding: 0 18px;
}
.chatHeadRight {
    display: flex;
}
.chatHeadRight > div {
    flex: 1;
    width: 45px;
    font-size: 12px;
    text-align: center;
}

/* 自定义样式 */
.WO-container {
    padding: 15px;
}
.WO-container .layui-form-label {
    font-weight: bold;
    text-align: left;
    padding-left: 0;
}
.WO-container .layui-form-item {
    margin-bottom: 15px;
}
.WO-container .layui-form-radio {
    margin: 5px 20px 5px 0;
}
.WO-container .layui-form-label {
    font-weight: 400;
    width: 100%;
}
.WO-container .layui-input-block {
    margin-left: 0;
}

/* 添加高亮样式 */
.chat-item.active {
    background-color: #e8f4ff !important;
    transition: background-color 0.3s;
    cursor: pointer;
    border-radius: 5px;
}

/* 基础样式建议 */
.chat-item {
    padding: 12px;
    margin: 5px 0;
    position: relative;
    border-bottom: 1px solid #eee;
}

.msg-tip {
    position: absolute;
    top: 10px;
    left: 45px;
    background: #ff5722;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
}

.reply-list li {
    padding: 10px;
    margin: 5px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.reply-content {
    flex: 1;
    margin-right: 15px;
    word-break: break-all;
}
.reply-actions button {
    margin-left: 5px;
}
/* 修复下拉框溢出问题 */
.layui-layer-page .layui-layer-content {
    overflow: visible !important;
}

/* 调整下拉框层级 */
.layui-form-select dl {
    z-index: 999999 !important; /* 确保高于弹窗层级 */
    max-height: 200px; /* 控制最大高度 */
    overflow-y: auto; /* 超长时显示滚动条 */
}
/* 调整表单label样式 */
.layui-form-label {
    white-space: nowrap; /* 禁止换行 */
    width: auto; /* 自动宽度 */
}

.contactsTree {
    width: 100%;
    height: 300px;
    position: absolute;
    bottom: 0;
    left: 0;
    display: none;
    cursor: pointer;
}
.contactsTree .head {
    font-size: 13px;
    text-align: center;
    padding: 5px 0;
    border: 1px solid #eee;
}
.contactsTree .contactsTreeBox {
    padding: 5px;
    overflow-y: auto;
}
.ztree {
    padding: 10px;
    font-size: 14px;
}
.ztree li span.node_name {
    font-size: 14px;
}
.contactsTree .head i {
    font-size: 13px;
}
/* .workOrder {
    display: none;
} */