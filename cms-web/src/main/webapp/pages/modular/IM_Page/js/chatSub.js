let messages = [
    {
        type: 'receive',
        content: '你好呀！',
        time: '10:30',
        avatar: 'https://img.icons8.com/color/48/000000/circled-user-female-skin-type-4.png'
    },
    {
        type: 'send',
        content: '你好！有什么可以帮助你的？',
        time: '10:31',
        avatar: 'https://img.icons8.com/color/48/000000/circled-user-male-skin-type-7.png'
    },
    {
        type: 'receive',
        content: '我需要帮助调试这个完美的聊天界面布局',
        time: '10:32',
        avatar: 'https://img.icons8.com/color/48/000000/circled-user-female-skin-type-4.png'
    }
];

// 模拟快捷回复数据
let quickReplyList = [
    { id: 1, content: "您好，请问有什么可以帮您？" },
    { id: 2, content: "请稍等，正在为您查询..." },
    { id: 3, content: "感谢您的耐心等待" }
];

// 渲染消息列表
function renderMessages() {
    let html = '';
    messages.forEach(msg => {
        html += `
            <div class="message-time">${msg.time}</div>
            <div class="message-container ${msg.type}-container">
                <img src="${msg.avatar}" class="user-avatar">
                <div class="message-bubble-wrapper">
                    <div class="message-bubble ${msg.type}-bubble">
                        <div class="bubble-arrow"></div>
                        ${msg.content}
                    </div>
                </div>
            </div>
        `;
    });
    document.getElementById('messageList').innerHTML = html;
    // 自动滚动到底部
    let container = document.getElementById('messageList');
    container.scrollTop = container.scrollHeight;
}

layui.use(['form', 'element', 'layer'], function () {
    const layer = layui.layer;
    const form = layui.form;
    let $ = layui.$;

    // 初始化渲染
    renderMessages();

    // 发送消息逻辑
    $('#sendBtn').click(function () {
        debugger
        const content = $('#messageInput').val().trim();
        if (content) {
            // 添加新消息
            messages.push({
                type: 'send',
                content: content,
                time: new Date().toLocaleTimeString('zh', { hour: '2-digit', minute: '2-digit' }),
                avatar: 'https://img.icons8.com/color/48/000000/circled-user-male-skin-type-7.png'
            });

            // 模拟接收回复
            setTimeout(() => {
                messages.push({
                    type: 'receive',
                    content: '已收到：' + content,
                    time: new Date().toLocaleTimeString('zh', { hour: '2-digit', minute: '2-digit' }),
                    avatar: 'https://img.icons8.com/color/48/000000/circled-user-female-skin-type-4.png'
                });
                renderMessages();
            }, 800);

            renderMessages();
            $('#messageInput').val('');
        }
    });

    // 回车发送支持
    $('#messageInput').keydown(function (e) {
        if (e.keyCode === 13 && !e.shiftKey) {
            e.preventDefault();
            $('#sendBtn').click();
        }
    });

    // 打开快捷回复弹窗
    $('#showQuickReply').click(function () {
        showQuickReplyModal();
    });

    // 显示快捷回复弹窗
    function showQuickReplyModal() {
        layer.open({
            type: 1,
            title: '添加快捷回复',
            area: ['500px', '400px'],
            content: createReplyListHTML(),
            success: function (layero, index) {
                // 绑定发送事件
                $(layero).find('.send-btn').click(function () {
                    const content = $(this).siblings('.reply-content').text();
                    sendReply(content);
                });

                // 绑定编辑事件
                $(layero).find('.edit-btn').click(function () {
                    const item = $(this).closest('.reply-item');
                    editReply(item);
                });

                // 绑定删除事件
                $(layero).find('.delete-btn').click(function () {
                    const item = $(this).closest('.reply-item');
                    deleteReply(item);
                });
            }
        });
    }

    // 创建回复列表HTML
    function createReplyListHTML() {
        let html = `<div class="reply-box" style="padding: 15px;">
            <ul class="reply-list">`;

        quickReplyList.forEach(item => {
            html += `<li class="reply-item" data-id="${item.id}">
                <div class="reply-content">${item.content}</div>
                <div class="reply-actions">
                    <button class="layui-btn layui-btn-xs send-btn">发送</button>
                    <button class="layui-btn layui-btn-xs layui-btn-normal edit-btn">编辑</button>
                    <button class="layui-btn layui-btn-xs layui-btn-danger delete-btn">删除</button>
                </div>
            </li>`;
        });

        html += `</ul>
            <button class="layui-btn layui-btn-fluid" id="addReply" style="margin-top: 15px;">
                <i class="layui-icon">&#xe654;</i> 添加
            </button>
        </div>`;

        return html;
    }

    // 添加回复
    $('#body').on('click', '#addReply', function () {
        layer.prompt({
            title: '输入快捷回复内容',
            formType: 2
        }, function (text, index) {
            quickReplyList.push({
                id: Date.now(),
                content: text
            });
            layer.close(index);
            showQuickReplyModal();
        });
    });

    // 编辑回复
    function editReply(item) {
        const id = item.data('id');
        const content = item.find('.reply-content').text();

        layer.prompt({
            title: '修改回复内容',
            formType: 2,
            value: content
        }, function (text, index) {
            quickReplyList = quickReplyList.map(reply =>
                reply.id === id ? { ...reply, content: text } : reply
            );
            layer.close(index);
            showQuickReplyModal();
        });
    }

    // 删除回复
    function deleteReply(item) {
        const id = item.data('id');
        layer.confirm('确认删除这条回复吗？', function (index) {
            quickReplyList = quickReplyList.filter(reply => reply.id !== id);
            layer.close(index);
            showQuickReplyModal();
        });
    }

    // 发送回复（示例）
    function sendReply(content) {
        layer.msg('发送内容：' + content);
        // 实际使用中可替换为真实发送逻辑
        layer.closeAll();
    }

    // 点击转接按钮
    $('#switch').click(function () {
        layer.open({
            type: 1,
            title: '选择转接客服',
            area: ['400px', '250px'],
            content: `
                <div class="layui-form" style="padding: 20px;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">客服列表</label>
                        <div class="layui-input-block">
                            <select name="kefu" lay-verify="required" id="kefuSelect">
                                <option value="">请选择客服</option>
                                <option value="1">客服A</option>
                                <option value="2">客服B</option>
                                <option value="3">客服C</option>
                            </select>
                        </div>
                    </div>
                </div>
            `,
            // 如果需要动态加载客服列表
            // function loadKefuList() {
            //     $.ajax({
            //         url: '/api/getKefuList',
            //         success: function(res) {
            //             let options = '<option value="">请选择客服</option>';
            //             res.data.forEach(item => {
            //                 options += `<option value="${item.id}">${item.name}</option>`;
            //             });
            //             $('#kefuSelect').html(options);
            //             form.render('select');
            //         }
            //     });
            // }

            // // 在弹窗打开时调用
            // success: function(layero, index){
            //     loadKefuList();
            // }
            btn: ['确定转接', '取消'],
            success: function (layero, index) {
                // 修复下拉框的关键代码
                const $selectBox = $(layero).find('.layui-form-select');
                $selectBox.css({
                    'z-index': layer.zIndex, // 继承layer的z-index
                    'position': 'relative'   // 解除父级overflow限制
                });
                form.render('select'); // 渲染下拉框
            },
            yes: function (index, layero) {
                // 获取选中的值
                const selectedVal = $('#kefuSelect').val();
                if (!selectedVal) {
                    layer.msg('请先选择要转接的客服');
                    return;
                }

                // 这里写转接逻辑
                layer.confirm(`确认转接到 ${$('#kefuSelect option:selected').text()} 吗？`, {
                    title: '确认转接',
                    btn: ['确认', '取消']
                }, function () {
                    layer.msg('转接成功', { icon: 1 });
                    layer.closeAll();
                }, function () {
                    layer.msg('已取消转接');
                });
            },
            btn2: function (index, layero) {
                layer.msg('已取消操作');
            }
        });
    });
});

// 好友列表添加点击事件
$('.chat-item').on('click', function () {
    // 移除所有高亮样式
    $('.chat-item').removeClass('active');
    // 隐藏所有未读提示
    $('.msg-tip').fadeOut();

    // 为当前点击项添加高亮
    $(this)
        .addClass('active')
        .find('.msg-tip')  // 找到当前项的未读提示
        .fadeOut();        // 隐藏未读提示

    // 如果需要保留未读提示数字，可以改为清空内容：
    // .text('')
});