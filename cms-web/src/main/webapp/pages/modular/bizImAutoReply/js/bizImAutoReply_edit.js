/**
 * 详情对话框
 */
var BizImAutoReplyInfoDlg = {
    data: {
        id: "",
        replyContent: "",
        replyImage: "",
        replyType: "",
        status: "",
        createTime: "",
        updateTime: "",
    }
};
layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#createTime'
    });
    laydate.render({
        elem: '#updateTime'
    });
});
layui.use(['form', 'ax', 'upload', 'table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var BizImAutoReply = {
        tableId: "bizImAutoReplyTable"
    };

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizImAutoReply/detail?bizImAutoReplyId=" + Feng.getUrlParam("bizImAutoReplyId"));
    var result = ajax.start();


    var replyOptionsAjax = new $ax(Feng.ctxPath + "/bizImAutoReply/bizImAutoReplyOptions");
    var replyOptionsResult = replyOptionsAjax.start();
    selector.initStaticSelected('replyType', 'value', 'name', replyOptionsResult.data, result.data.replyType, null);

    const statusOptions = [
        {"value": "1", "name": "启用"},
        {"value": "0", "name": "禁用"}
    ]
    selector.initStaticSelected('status', 'value', 'name', statusOptions, result.data.status, null);


    $('#imIconPicImg').attr("src", Feng.getAliImgUrl(result.data.replyImage, 800, 800))

    form.val('bizImAutoReplyForm', result.data);

    //图片赋值
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/bizImAutoReply/update", function (data) {
            Feng.success("更新成功！");
            setTimeout(function () {
                window.location.href = Feng.ctxPath + "/bizImAutoReply";
            }, 1000);
        }, function (data) {
            Feng.error("更新失败！" + data.responseJSON.message);
            setTimeout(function () {
                top.layer.closeAll();
            }, 1000);
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizImAutoReply";
    });

    //普通图片上传
    upload.render({
        elem: '#uploadimIconPic'
        , url: Feng.ctxPath + '/oss/upload'
        , before: function (obj) {
            obj.preview(function (index, file, result) {
                $('#imIconPicImg').attr('src', result);
            });
        }
        , done: function (res) {
            $('#imIconPicImg').attr('src', res.data.acurl);
            $("#replyImageHidden").val(res.data.ourl);
            Feng.success(res.message);
        }
        , error: function () {
            Feng.error("上传图片失败！");
        }
    });
});
