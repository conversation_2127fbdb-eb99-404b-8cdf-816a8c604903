layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var BizImAutoReply = {
        tableId: "bizImAutoReplyTable"
    };

    /**
     * 初始化表格的列
     */
    BizImAutoReply.initColumn = function () {

        var replyOptionsAjax = new $ax(Feng.ctxPath + "/bizImAutoReply/bizImAutoReplyOptions");
        var replyOptionsResult = replyOptionsAjax.start();
        selector.initStaticSelected('replyType', 'value', 'name',
            replyOptionsResult.data, null, "类型");

        const statusOptions = [
            {"value": "1", "name": "启用"},
            {"value": "0", "name": "禁用"}
        ]
        //状态
        selector.initStaticSelected('status', 'value', 'name',
            statusOptions, null, "启用状态");


        return [[
            {type: 'checkbox'},
            {field: 'id', minWidth: 100, hide: true, title: 'id'},
            {field: 'replyContent', minWidth: 100, hide: false, title: '内容'},
            {field: 'replyImage', minWidth: 100, hide: true, title: '图片'},
            {field: 'replyType', minWidth: 100, hide: true, title: '类型'},
            {field: 'replyTypeName', minWidth: 100, hide: false, title: '类型'},
            {
                title: '启用', templet: function (d) {
                    return `
                          <div class="sort-container">
                            <div class="sort-btn'}"  data-id="${d.id}" >
                              <i class="layui-icon">
                                ${d.status === 1 ? '启用' : '禁用'}
                              </i>
                            </div>
                          </div>
                        `;
                }
            },
            {field: 'createTime', minWidth: 100, hide: false, title: '创建时间'},
            {field: 'updateTime', minWidth: 100, hide: false, title: '更新时间'},
            {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BizImAutoReply.tableId,
        url: Feng.ctxPath + '/bizImAutoReply/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: BizImAutoReply.initColumn()
    });
    /**
     * 点击查询按钮
     */
    BizImAutoReply.search = function () {
        var queryData = {};
        queryData['replyContent'] = $("#replyContent").val();
        queryData['replyType'] = $("#replyType").val();
        queryData['status'] = $("#status").val();
        table.reload(BizImAutoReply.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    BizImAutoReply.openAddDlg = function () {
        window.location.href = Feng.ctxPath + '/bizImAutoReply/bizImAutoReply_add';
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    BizImAutoReply.openEditDlg = function (data) {
        window.location.href = Feng.ctxPath + '/bizImAutoReply/bizImAutoReply_update?bizImAutoReplyId=' + data.id;
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    BizImAutoReply.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizImAutoReply/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(BizImAutoReply.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("bizImAutoReplyId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };

    BizImAutoReply.toggleStatus = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizImAutoReply/enable?bizImAutoReplyId=" + data.id + "&status=" + data.status, function (data) {
                Feng.success("切换成功!");
                table.reload(BizImAutoReply.tableId);
            }, function (data) {
                Feng.error("切换失败!" + data.responseJSON.message + "!");
            });
            ajax.start();
        };
        Feng.confirm("是否切换状态?", operation);
    };

    /**
     * bizImAutoReply详情
     */
    BizImAutoReply.detail = function (data) {
        window.location.href = Feng.ctxPath + '/bizImAutoReply/bizImAutoReply_detail?bizImAutoReplyId=' + data.id;
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BizImAutoReply.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        BizImAutoReply.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + BizImAutoReply.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            BizImAutoReply.openEditDlg(data);
        } else if (layEvent === 'delete') {
            BizImAutoReply.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            BizImAutoReply.detail(data);
        } else if (layEvent === 'toggleStatus') {
            BizImAutoReply.toggleStatus(data);
        }
    });
});


