@layout("/common/_container.html",{js:["/pages/modular/bizImAutoReply/js/bizImAutoReply_add.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">添加bizImAutoReply</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizImAutoReplyForm" lay-filter="bizImAutoReplyForm" class="layui-form model-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">内容</label>
                        <div class="layui-input-inline">
                            <input id="replyContent" name="replyContent" placeholder="请输入replyContent" type="text"
                                   class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">图片</label>
                        <div class="layui-input-inline">
                            <div class="layui-upload">
                                <div class="layui-upload-list">
                                    <img class="layui-upload-img" style="width: 200px;height: 200px" id="imIconPicImg">
                                    <input type="hidden" name="replyImage" id="replyImageHidden">
                                    <p id="demoText"></p>
                                </div>
                                <button type="button" class="layui-btn" id="uploadimIconPic">上传图片</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">类型</label>
                        <div class="layui-input-inline">
                            <select name="replyType" id="replyType" lay-filter="questType">
                            </select>
                        </div>

                        <label class="layui-form-label">启用</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status" lay-filter="questType">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog"
                                id="backupPage">返回
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@}
