@layout("/common/_container.html",{js:["/pages/modular/bizImAutoReply/js/bizImAutoReply.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">bizImAutoReply管理</span>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <input type="hidden" id="bizImAutoReplyId" value="bizImAutoReplyId"/>
                                <input id="replyContent" class="layui-input" type="text" placeholder="内容"
                                       autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <select id="replyType"></select>
                            </div>
                            <div class="layui-inline">
                                <select id="status"></select>
                            </div>

                            <div class="layui-inline">
                                <button id="btnSearch" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索
                                </button>
                                @if(shiro.hasPermission("/bizImAutoReply/bizImAutoReply_add")){
                                <button id="btnAdd" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加
                                </button>
                                @}

                            </div>
                        </div>
                    </div>
                    <table class="layui-table" id="bizImAutoReplyTable" lay-filter="bizImAutoReplyTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tableBar">
    @if(shiro.hasPermission("/bizImAutoReply/bizImAutoReply_update")){
    <a class="layui-btn layui-btn-primary layui-btn-xs  {{d.status == 1 ? 'layui-btn-danger' : 'layui-btn-normal'}}"
       lay-event="toggleStatus">
        {{d.status == 1 ? '禁用' : '启用'}}
    </a>
    @}

    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
    @if(shiro.hasPermission("/bizImAutoReply/bizImAutoReply_update")){
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    @}
    @if(shiro.hasPermission("/bizImAutoReply/delete")){
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    @}
</script>
@}

