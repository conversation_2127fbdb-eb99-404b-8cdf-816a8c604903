/**
 * 详情对话框
 */
var ImGroupUserInfoDlg = {
    data: {
       id:"",
       userId:"",
       nickName:"",
       accid:"",
       userType:"",
       userTypename:"",
       gid:"",
       gname:"",
       mute:"",
       isRemove:"",
       createTime:"",
       updateTime:"",
    }
};
layui.use('laydate', function(){
    var laydate = layui.laydate;
        laydate.render({
                elem: '#createTime'
         });
        laydate.render({
                elem: '#updateTime'
         });
});
layui.use(['form', 'ax','upload','table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var ImGroupUser = {
            tableId: "imGroupUserTable"
     };

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/imGroupUser/detail?imGroupUserId=" + Feng.getUrlParam("imGroupUserId"));
    var result = ajax.start();
    form.val('imGroupUserForm', result.data);

    //图片赋值
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/imGroupUser/update", function (data) {
            Feng.success("更新成功！");
           setTimeout(function () {
                           top.layer.closeAll();
           }, 1000);
        }, function (data) {
            Feng.error("更新失败！" + data.responseJSON.message);
           setTimeout(function () {
                           top.layer.closeAll();
                       }, 1000);
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/imGroupUser";
    });
});
