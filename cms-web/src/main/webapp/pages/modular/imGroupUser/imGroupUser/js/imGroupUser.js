layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var ImGroupUser = {
        tableId: "imGroupUserTable"
    };

    /**
     * 初始化表格的列
     */
    ImGroupUser.initColumn = function () {
        return [[
            {type: 'checkbox'},
{field: 'id', minWidth: 100,hide: false,title: 'ID'},
{field: 'userId', minWidth: 100,hide: false,title: '用户ID'},
{field: 'nickName', minWidth: 100,hide: false,title: '群昵称'},
{field: 'accid', minWidth: 100,hide: false,title: '用户accid'},
{field: 'userType', minWidth: 100,hide: false,title: '用户类型'},
{field: 'userTypename', minWidth: 100,hide: false,title: '类型名称'},
{field: 'gid', minWidth: 100,hide: false,title: '群ID'},
{field: 'gname', minWidth: 100,hide: false,title: '群名称'},
{field: 'mute', minWidth: 100,hide: false,title: ''},
{field: 'isRemove', minWidth: 100,hide: false,title: '移除标识'},
{field: 'createTime', minWidth: 100,hide: false,title: '创建时间'},
{field: 'updateTime', minWidth: 100,hide: false,title: '更新时间'},
            {align: 'center', toolbar: '#tableBar',fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + ImGroupUser.tableId,
        url: Feng.ctxPath + '/imGroupUser/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: ImGroupUser.initColumn()
    });
    /**
     * 点击查询按钮
     */
    ImGroupUser.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(ImGroupUser.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    ImGroupUser.openAddDlg = function () {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加群组用户',
            content:  Feng.ctxPath + '/imGroupUser/imGroupUser_add',
            end: function () {
                layer.closeAll('loading');
                table.reload(ImGroupUser.tableId);
            }
        });
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    ImGroupUser.openEditDlg = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加群组用户',
            content:  Feng.ctxPath + '/imGroupUser/imGroupUser_update?imGroupUserId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(ImGroupUser.tableId);
            }
        });
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    ImGroupUser.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/imGroupUser/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(ImGroupUser.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("imGroupUserId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 群组用户详情
     */
    ImGroupUser.detail = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '群组用户详情',
            content:  Feng.ctxPath + '/imGroupUser/imGroupUser_detail?imGroupUserId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(ImGroupUser.tableId);
            }
        });
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        ImGroupUser.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        ImGroupUser.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + ImGroupUser.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            ImGroupUser.openEditDlg(data);
        } else if (layEvent === 'delete') {
            ImGroupUser.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            ImGroupUser.detail(data);
        }
    });
});


