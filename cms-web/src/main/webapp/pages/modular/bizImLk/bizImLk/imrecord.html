@layout("/common/_container.html",{js:["/pages/modular/bizImLk/bizImLk/js/imrecode.js"]}){
<style>
    .chat-container {
        width: 100%;
        margin: 20px auto;
        border: 1px solid #e6e6e6;
        border-radius: 5px;
    }
    .msg-image {
        max-width: 300px;
        height: auto;
        border-radius: 4px;
        margin-top: 8px;
    }
    .msg-list {
        height: 100%;
        overflow-y: auto;
        padding: 15px;
    }

    .msg-item {
        margin: 10px 0;
    }

    .msg-item.left {
        text-align: left;
    }

    .msg-item.right {
        text-align: right;
    }

    .msg-content {
        display: inline-block;
        max-width: 70%;
        padding: 10px 15px;
        border-radius: 5px;
        position: relative;
    }

    .msg-item.left .msg-content {
        background: #fff;
        border: 1px solid #e6e6e6;
    }

    .msg-item.right .msg-content {
        background: #a0d9f8;
        color: #fff;
    }

    .msg-info {
        font-size: 12px;
        color: #999;
        margin-top: 5px;
    }

    .input-area {
        border-top: 1px solid #e6e6e6;
        padding: 15px;
    }
</style>
<div class="chat-container">
    <!-- 消息列表 -->
    <div class="msg-list" id="msgList"></div>
</div>

@}