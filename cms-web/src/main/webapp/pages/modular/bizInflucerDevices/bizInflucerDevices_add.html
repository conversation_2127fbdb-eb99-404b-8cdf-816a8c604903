@layout("/common/_container.html",{js:["/pages/modular/bizInflucerDevices/js/bizInflucerDevices_add.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">添加InflucerDevices</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizInflucerDevicesForm" lay-filter="bizInflucerDevicesForm" class="layui-form model-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-inline">
                            <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input"
                                   lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">达人用户id</label>
                        <div class="layui-input-inline">
                            <input id="influcerId" name="influcerId" placeholder="请输入influcerId" type="text"
                                   class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-inline">
                            <input id="registrationId" name="registrationId" placeholder="请输入registrationId"
                                   type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-inline">
                            <input id="platform" name="platform" placeholder="请输入platform" type="text"
                                   class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-inline">
                            <input id="alias" name="alias" placeholder="请输入alias" type="text" class="layui-input"
                                   lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-inline">
                            <input id="tags" name="tags" placeholder="请输入tags" type="text" class="layui-input"
                                   lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-inline">
                            <input id="updateTime" name="updateTime" placeholder="请输入updateTime" type="text"
                                   class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog"
                                id="backupPage">返回
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@}
