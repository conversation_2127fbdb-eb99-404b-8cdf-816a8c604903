@layout("/common/_container.html",{js:["/pages/modular/bizAppSlideshow/bizAppSlideshow/js/bizAppSlideshow_detail.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">轮播图管理详情</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizAppSlideshowForm" lay-filter="bizAppSlideshowForm" class="layui-form model-form"
                  style="max-width: 700px;margin: 40px auto;">
                <input name="" type="hidden"/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">标题</label>
                        <div class="layui-input-inline">
                            <input id="title" name="title" placeholder="请输入标题" type="text" class="layui-input"
                                   lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">跳转链接</label>
                        <div class="layui-input-inline">
                            <input id="likeUrl" name="likeUrl" placeholder="请输入跳转链接" type="text"
                                   class="layui-input"/>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status" lay-filter="status">
                                <option value=""></option>
                                <option value="1">启用</option>
                                <option value="-1">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">排序字段</label>
                        <div class="layui-input-inline">
                            <input id="sortNum" name="sortNum" placeholder="请输入排序字段" type="number"
                                   class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">图片</label>
                    <div class="layui-input-inline">
                        <div class="layui-upload">
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" style="width: 200px;height: 200px" id="imgPicImg">
                                <input type="hidden" name="imgPic" id="imgPicHidden">
                                <p id="demoText"></p>
                            </div>
                            <button type="button" class="layui-btn" id="uploadimgPic">上传图片</button>
                        </div>
                    </div>
                </div>


                <div class="layui-form-item">
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog"
                                id="backupPage">返回
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@}
