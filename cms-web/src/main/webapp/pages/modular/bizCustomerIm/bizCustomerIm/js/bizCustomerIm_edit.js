/**
 * 详情对话框
 */
var BizCustomerImInfoDlg = {
    data: {
       id:"",
       customerId:"",
       customerName:"",
       customerAccid:"",
       imToken:"",
       imIconPic:"",
       createTime:"",
       updateTime:"",
    }
};
layui.use('laydate', function(){
    var laydate = layui.laydate;
        laydate.render({
                elem: '#createTime'
         });
        laydate.render({
                elem: '#updateTime'
         });
});
layui.use(['form', 'ax','upload','table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var BizCustomerIm = {
            tableId: "bizCustomerImTable"
     };

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizCustomerIm/detail?bizCustomerImId=" + Feng.getUrlParam("bizCustomerImId"));
    var result = ajax.start();
    form.val('bizCustomerImForm', result.data);

    //图片赋值
    $('#imIconPicImg').attr("src",Feng.getAliImgUrl(result.data.imIconPic,800,800))
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/bizCustomerIm/update", function (data) {
            Feng.success("更新成功！");
           setTimeout(function () {
                           top.layer.closeAll();
           }, 1000);
        }, function (data) {
            Feng.error("更新失败！" + data.responseJSON.message);
           setTimeout(function () {
                           top.layer.closeAll();
                       }, 1000);
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });

    //普通图片上传
    upload.render({
      elem: '#uploadimIconPic'
      , url: Feng.ctxPath + '/oss/upload'
      , before: function (obj) {
          obj.preview(function (index, file, result) {
              $('#imIconPicImg').attr('src', result);
          });
      }
      , done: function (res) {
          $('#imIconPicImg').attr('src', res.data.acurl);
          $("#imIconPicHidden").val(res.data.ourl);
          Feng.success(res.message);
      }
      , error: function () {
          Feng.error("上传图片失败！");
      }
    });

    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizCustomerIm";
    });
});
