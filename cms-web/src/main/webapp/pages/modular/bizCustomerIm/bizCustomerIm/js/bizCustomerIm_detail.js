/**
 * 详情对话框
 */
var BizCustomerImInfoDlg = {
    data: {
       id:"",
       customerId:"",
       customerName:"",
       customerAccid:"",
       imToken:"",
       imIconPic:"",
       createTime:"",
       updateTime:"",
    }
};
layui.use(['form','ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizCustomerIm/detail?bizCustomerImId=" + Feng.getUrlParam("bizCustomerImId"));
    var result = ajax.start();
     form.val('bizCustomerImForm', result.data);
    //图片赋值
    $("#id").attr("disabled","disabled");
    $("#customerId").attr("disabled","disabled");
    $("#customerName").attr("disabled","disabled");
    $("#customerAccid").attr("disabled","disabled");
    $("#imToken").attr("disabled","disabled");
    $('#imIconPicImg').attr("src",Feng.getAliImgUrl(result.data.imIconPic,800,800))
    $("#createTime").attr("disabled","disabled");
    $("#updateTime").attr("disabled","disabled");
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizCustomerIm";
    });
});
