@layout("/common/_container.html",{js:["/pages/modular/bizNomalEnums/bizNomalEnums/js/bizNomalEnums_edit.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">修改类型设置</span>
</div>

<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizNomalEnumsForm" lay-filter="bizNomalEnumsForm" class="layui-form model-form" style="max-width: 700px;margin: 40px auto;">
                <input name="" type="hidden"/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">编号</label>
                        <div class="layui-input-inline">
                            <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">项目名称</label>
                        <div class="layui-input-inline">
                            <input id="projName" name="projName" placeholder="请输入projName" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">类型名称</label>
                        <div class="layui-input-inline">
                            <input id="keyType" name="keyType" placeholder="请输入keyType" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">描述</label>
                        <div class="layui-input-block">
                            <input id="keyDesc" name="keyDesc" placeholder="请输入describe" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@}
