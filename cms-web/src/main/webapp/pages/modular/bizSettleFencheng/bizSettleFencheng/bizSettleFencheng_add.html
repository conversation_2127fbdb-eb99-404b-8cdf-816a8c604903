@layout("/common/_container.html",{js:["/pages/modular/bizSettleFencheng/bizSettleFencheng/js/bizSettleFencheng_add.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">添加结算比例</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizSettleFenchengForm" lay-filter="bizSettleFenchengForm" class="layui-form model-form" style="max-width: 700px;margin: 40px auto;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">名称</label>
                        <div class="layui-input-inline">
                            <input id="projName" name="projName" placeholder="请输入projName" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">公司分成</label>
                        <div class="layui-input-inline">
                            <input id="toComp" name="toComp" placeholder="请输入toComp" type="text" class="layui-input" lay-verify="required" required/><span style="position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #666;">%</span>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">个人分成</label>
                        <div class="layui-input-inline">
                            <input id="toDaren" name="toDaren" placeholder="请输入toDaren" type="text" class="layui-input" lay-verify="required" required/> <span style="position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #666;">%</span>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">分成描述</label>
                        <div class="layui-input-block">
                            <input id="fenchDesc" name="fenchDesc" placeholder="请输入fenchDesc" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@}
