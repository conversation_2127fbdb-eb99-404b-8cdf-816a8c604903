/**
 * 详情对话框
 */
var BizImRecordInfoDlg = {
    data: {
       id:"",
       customerId:"",
       customerName:"",
       customerAccid:"",
       iflncerId:"",
       iflncerName:"",
       iflnceAccid:"",
       msg:"",
       msgFangxiang:"",
       createTime:"",
       updateTime:"",
    }
};
layui.use(['form','ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizImRecord/detail?bizImRecordId=" + Feng.getUrlParam("bizImRecordId"));
    var result = ajax.start();
     form.val('bizImRecordForm', result.data);
    //图片赋值
    $("#id").attr("disabled","disabled");
    $("#customerId").attr("disabled","disabled");
    $("#customerName").attr("disabled","disabled");
    $("#customerAccid").attr("disabled","disabled");
    $("#iflncerId").attr("disabled","disabled");
    $("#iflncerName").attr("disabled","disabled");
    $("#iflnceAccid").attr("disabled","disabled");
    $("#msg").attr("disabled","disabled");
    $("#msgFangxiang").attr("disabled","disabled");
    $("#createTime").attr("disabled","disabled");
    $("#updateTime").attr("disabled","disabled");
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizImRecord";
    });
});
