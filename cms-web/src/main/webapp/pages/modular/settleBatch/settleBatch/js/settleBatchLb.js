layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#updateTime'
    });
    laydate.render({
        elem: '#createTime'
    });
    // 绑定日期选择器到指定输入框
    laydate.render({
        elem: '#caiwuZhouqiStart',
        trigger: 'click',
        done: function(value, date){
            // 结束日期不能早于开始日期
            laydate.render({
                elem: '#caiwuZhouqiEnd',
                min: value
            });
        }
    });

});

layui.use(['form', 'ax', 'upload', 'table', 'element'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var SettleInfluencerOrder = {
        tableId: "settleInfluencerOrderTable"
    };

    var element = layui.element;
    element.render('step');
    function formatDate(date, fmt) {
        var shortDate = date.substring(0, 10);
        return shortDate;
    }
    var ajax = new $ax(Feng.ctxPath + "/settleBatch/detail?settleBatchId=" + Feng.getUrlParam("settleBatchId"));
    var batchRst = ajax.start();

    function resetJsHdVals(){
        var settleSrc = $('#caiwuFuzhuFile .file-item a').map(function() {
            return $(this).attr('srcSource');
        }).get().join(',');
        $("#caiwuFujianFiles").val(settleSrc);


        var settleSrcd = $('#cwJiesuanFiles .file-item a').map(function() {
            return $(this).attr('srcSource');
        }).get().join(',');

        $("#caiwuJisuanFile").val(settleSrcd);
    }

    var reqPurl = Feng.ctxPath + "/settleProjects/detail?settleProjectsId=" + batchRst.data.projectId;
    var ajax = new $ax(reqPurl, function (rpsData) {
        console.log('rpsData ' + rpsData)

        $("#projectCode").text(rpsData.data.projectCode);
        $("#createTime").text(formatDate(rpsData.data.createTime, "yyyy-MM-dd") );
        $("#projectStatusName").text(rpsData.data.projectStatusName);

        $("#zhouqi").text(formatDate(rpsData.data.projectStarttime, "yyyy-MM-dd") + "-" + formatDate(rpsData.data.projectEndtime, "yyyy-MM-dd"));
        $("#compName").text(rpsData.data.compName);
        var pltstr = rpsData.data.yijiPlatform;
        if (rpsData.data.erjiPlatform) {
            pltstr = pltstr + "-" + rpsData.data.erjiPlatform
            if (rpsData.data.sanjiPlatform) {
                pltstr = pltstr + "-" + rpsData.data.sanjiPlatform
            }
        }
        $("#servicePlat").text((pltstr));
        $("#projectTypeName").text((rpsData.data.projectTypeName));
        var fenchegnStr =  rpsData.data.commissionToComp;
        var darenFench = rpsData.data.commissionToDaren;

        if(darenFench){
            fenchegnStr =  fenchegnStr+"|"+darenFench
        }
        $("#businessOwnerName").text((rpsData.data.businessOwnerName));
        $("#createName").text((rpsData.data.createName));
        $("#quantTarget").text(rpsData.data.quantTarget);
        $("#settlementCycleName").text(rpsData.data.settlementCycleName);
        $("#revenueTypeName").text(rpsData.data.revenueTypeName);
        $("#commissionRate").text(fenchegnStr);

        $("#agencyRevenue").text(rpsData.data.agencyRevenue);
        $("#jigouName").text(rpsData.data.jigouName);
        $("#projectDesc").text(rpsData.data.projectDesc);
        $("#projectZhixingFa").text(rpsData.data.projectZhixingFa);
        $('#beijingFileDiv').empty()  ;
        $('#zhixingFileDiv').empty()  ;
        setFilesVal(rpsData.data);

        $('#zhouqiStart').val(formatDate(batchRst.data.zhouqiStart, "yyyy-MM-dd") )  ;
        $('#zhouqiEnd').val(formatDate(batchRst.data.zhouqiEnd, "yyyy-MM-dd") )  ;
        $('#liushuiAmount').val(batchRst.data.liushuiAmount)  ;
        $('#yingfaAmount').val(batchRst.data.yingfaAmount)  ;

        setFilesToEle(batchRst.data.jisuanFile,$('#jiesuanFileList'));
        setFilesToEle(batchRst.data.fujianFiles,$('#fuzhuFile'));
        $('#remark').val(batchRst.data.remark)  ;

        setFilesToEle(batchRst.data.caiwuJisuanFile, $('#cwJiesuanFiles'));
        setFilesToEle(batchRst.data.caiwuFujianFiles, $('#caiwuFuzhuFile'));

        $('#caiwuZhouqiStart').val(batchRst.data.caiwuZhouqiStart);
        $('#caiwuZhouqiEnd').val(batchRst.data.caiwuZhouqiEnd);
        $('#caiwuRemark').val(batchRst.data.caiwuRemark);

    });
    ajax.start();

    form.on('submit(jujueBut)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/settleBatch/cwshhe", function (data) {
            Feng.success("拒绝成功！");
            window.location.href = Feng.ctxPath + "/settleOrder";
        }, function (data) {
            Feng.error("拒绝失败！" + data.responseJSON.message)
            window.location.href = Feng.ctxPath + "/settleOrder";
        });

        ajax.set("isTag",2);
        ajax.set("batchId",$("#batchId").val());
        ajax.set("recordId",$("#cwRecordId").val());

        ajax.start();
    });
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/settleBatch/lbshhe", function (data) {
            Feng.success("审核成功！");
            window.location.href = Feng.ctxPath + "/settleOrder";
        }, function (data) {
            Feng.error("审核失败！" + data.responseJSON.message)
            window.location.href = Feng.ctxPath + "/settleOrder";
        });
        var settleSrc = $('#fileList .file-item a').map(function() {
            return $(this).attr('srcSource');
        }).get().join(',');

        var fujianSrcs = $('#fuzhuUpdBut .file-item a').map(function() {
            return $(this).attr('srcSource').join(',');
        }).get();

        /**
         *     private Date caiwuZhouqiStart;
         *     private Date caiwuZhouqiEnd;
         *     private String caiwuJisuanFile;
         *     private String caiwuFujianFiles;
         *     private String caiwuRemark;
         *     private Long recordId;
         *     private Integer isTag;
         *     private Long batchId;
         */

        ajax.set("recordId",$("#cwRecordId").val());
        ajax.set("isTag",1);
        ajax.set("batchId",$("#batchId").val());
        ajax.start();

        return false;
    });
    $("#backupPage").click(function (e) {
        e.preventDefault();  // 阻止默认跳转
        window.location.href = Feng.ctxPath + "/settleOrder";
    });
});