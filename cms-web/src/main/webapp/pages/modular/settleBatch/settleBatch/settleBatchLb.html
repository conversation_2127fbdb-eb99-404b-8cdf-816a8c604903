@layout("/common/_container.html",{js:["/pages/modular/settleProjects/settleProjects/js/fileupd.js","/pages/modular/settleBatch/settleBatch/js/settleBatchLb.js"]}){

<div class="layui-container" style="width: 100%">
    <!-- 基础信息模块 -->
    <form id="settleInfluencerOrderForm" lay-filter="settleInfluencerOrderForm" class="layui-form model-form">
        <div class="layui-row layui-col-space15">
            @ include("/modular/settleOrder/settleOrder/settleProject.html"){}
            <div class="layui-col-24">
                <div class="layui-card" style="width: 100%">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-card-header">业务上传</div>
                        <div class="layui-card-body">

                            <div class="layui-form-item">
                                <label class="layui-form-label">结算周期</label>
                                <div class="layui-inline">
                                    <input id="zhouqiStart" name="zhouqiStart"
                                           placeholder="开始时间"
                                           class="layui-input date-picker" style="width: 160px;">
                                </div>
                                <div class="layui-inline" style="margin: 0 5px;">至</div>
                                <div class="layui-inline">
                                    <input id="zhouqiEnd" name="zhouqiEnd"
                                           placeholder="结束时间"
                                           class="layui-input date-picker" style="width: 160px;">
                                </div>
                            </div>
                            <div class="layui-inline" style="width: 400px">
                                <label class="layui-form-label">流水金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input" id="liushuiAmount" name="liushuiAmount" disabled>
                                </div>
                            </div>

                            <div class="layui-inline" style="width: 400px">
                                <label class="layui-form-label">应发放收益</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input" id="yingfaAmount" name="yingfaAmount" disabled>
                                </div>
                            </div>
                            <div class="layui-inline" style="width: 400px">
                                <label class="layui-form-label">结算单</label>
                                <div class="layui-input-inline">
                                    <div class="file-list" id="jiesuanFileList">
                                        <!-- 动态插入的文件项 -->
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="layui-form-item">
                            <div class="layui-form-item">
                                <div class="layui-block">
                                    <label class="layui-form-label" style="width: 150px">其他辅助材料</label>
                                    <div class="layui-input-block">
                                        <div id="fuzhuFile" class="layui-clear">
                                            <!-- 动态插入文件项 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-col-12">
                            <div class="layui-form-item">
                                <label class="layui-form-label">备注说明</label>
                                <div class="layui-input-block">
                                    <textarea class="layui-textarea" id="remark"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 财务单据上传 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-24">
                <div class="layui-card">
                    <div class="layui-card-header">财务上传</div>
                    <div class="layui-card-body">
                        <div class="layui-row">
                            <div class="layui-col-12">
                                <!-- 删除重复的结算周期模块 -->
                                <div class="layui-form-item">
                                    <label class="layui-form-label">结算周期</label>
                                    <div class="layui-inline">
                                        <input id="caiwuZhouqiStart" name="caiwuZhouqiStart"
                                               placeholder="开始时间"
                                               class="layui-input date-picker" style="width: 160px;">
                                    </div>
                                    <div class="layui-inline" style="margin: 0 5px;">至</div>
                                    <div class="layui-inline">
                                        <input id="caiwuZhouqiEnd" name="caiwuZhouqiEnd"
                                               placeholder="结束时间"
                                               class="layui-input date-picker" style="width: 160px;">
                                    </div>
                                </div>

                                <!-- 文件上传区域 -->
                                <div class="layui-form-item">
                                    <label class="layui-form-label">结算文件</label>
                                    <div class="layui-input-block">
                                        <div class="file-list" id="cwJiesuanFiles">
                                            <!-- 动态插入的文件项 -->
                                        </div>
                                        <div class="layui-word-aux">支持格式：.xlsx, .pdf（单个文件不超过10MB）</div>
                                        <input type="hidden" id="caiwuJisuanFile" name="caiwuJisuanFile"/>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">其他辅助文件</label>
                                    <div class="layui-input-block">
                                        <div id="caiwuFuzhuFile" class="layui-clear">
                                            <!-- 动态插入文件项 -->
                                        </div>
                                        <input type="hidden" id="caiwuFujianFiles" name="caiwuFujianFiles"/>
                                    </div>
                                </div>

                            </div>
                            <div class="layui-col-12">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">备注说明</label>
                                    <div class="layui-input-block">
                                        <textarea class="layui-textarea" name="caiwuRemark" id="caiwuRemark"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <input type="hidden" id="cwRecordId" name="cwRecordId" value="${wfApprovalRecord.id}"/>
            <input type="hidden" id="batchId" name="batchId" value="${item.id}"/>
        </div>
        <div class="layui-col-24">
            <div class="layui-card">
                @ include("/modular/settleOrder/settleOrder/settlepjs.html"){}
                <div class="layui-row">
                    <div class="layui-col-24" style="text-align: center; margin-bottom: 80px;padding: 10px 0;">
                        @if(isshow==1){
                        <button class="layui-btn layui-btn-lg" id="submitBut" lay-filter="btnSubmit" lay-submit style="width: 200px;">审核通过</button>
                        <button class="layui-btn layui-btn-lg layui-bg-orange" id="jujueBut" lay-filter="jujueBut" style="width: 200px;">拒绝</button>
                        @}
                        <button class="layui-btn layui-btn-primary layui-btn-lg" id="backupPage" ew-event="closeDialog" style="width: 200px; margin-left: 30px;">返回</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>


@}