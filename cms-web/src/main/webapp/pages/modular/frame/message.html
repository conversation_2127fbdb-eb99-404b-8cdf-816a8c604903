@layout("/common/_container.html",{title:"消息列表",css:["/assets/modular/frame/message.css"],js:["/assets/modular/frame/message.js"]}){
<div class="layui-tab layui-tab-brief" style="padding: 5px 0;margin: 0;">
    <ul class="layui-tab-title" style="text-align: center;">
        <!-- <li class="layui-this">通知(5)</li> -->
        <!-- <li>私信(12)</li>
        <li>待办(3)</li> -->
    </ul>
    <div class="layui-tab-content" style="padding: 5px 0;">

        <!-- tab1 -->
        <div class="layui-tab-item layui-show">
            <div class="message-list"  >
                <!-- 实际项目请使用后台数据循环出来 -->
                <a class="message-list-item" href="javascript:;">
                    <img class="message-item-icon" src="${ctxPath}/assets/common/images/message.png">
                    <div class="message-item-right">
                        <h2 class="message-item-title">你收到了14份新周报</h2>
                        <p class="message-item-text">10个月前</p>
                    </div>
                </a>
                <a class="message-list-item" href="javascript:;">
                    <img class="message-item-icon" src="${ctxPath}/assets/common/images/message.png">
                    <div class="message-item-right">
                        <h2 class="message-item-title">你收到了14份新周报</h2>
                        <p class="message-item-text">10个月前</p>
                    </div>
                </a>
                <a class="message-list-item" href="javascript:;">
                    <img class="message-item-icon" src="${ctxPath}/assets/common/images/message.png">
                    <div class="message-item-right">
                        <h2 class="message-item-title">你收到了14份新周报</h2>
                        <p class="message-item-text">10个月前</p>
                    </div>
                </a>

                <!-- 列表为空 -->
                <div class="message-list-empty" style="display: none;">
                    <img src="${ctxPath}/assets/common/images/img_msg_notice.svg">
                    <div>没有通知</div>
                </div>

            </div>

            <a class="message-btn-clear" href="javascript:;">全部标记已读</a>

        </div>

    </div>
</div>
@}