<!DOCTYPE html>
<html>

<head>
    <title>${systemName}</title>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="${ctxPath}/assets/common/layui/css/layui.css"/>
    <link rel="stylesheet" href="${ctxPath}/assets/common/module/admin.css"/>
</head>

<body>

<!-- 加载动画，移除位置在common.js中 -->
<div class="page-loading">
    <div class="rubik-loader"></div>
</div>

<div class="layui-card-body" style="text-align: center;">
    <h2 style="margin-top: 170px;margin-bottom: 20px;font-size: 28px;color: #91ADDC;">${welcomeTip}</h2>
    <img src="${ctxPath}/assets/common/images/welcome.png" style="max-width: 100%;">
</div>

<!-- js部分 -->
@/* 加入contextPath属性和session超时的配置 */
<script type="text/javascript">
    var Feng = {
        ctxPath: "",
        addCtx: function (ctx) {
            if (this.ctxPath === "") {
                this.ctxPath = ctx;
            }
        }
    };
    Feng.addCtx("${ctxPath}");
</script>
<script type="text/javascript" src="${ctxPath}/assets/common/layui/layui.js"></script>
<script type="text/javascript" src="${ctxPath}/assets/common/js/common.js"></script>

<script>
    layui.use(['layer'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
    });
</script>
</body>

</html>