@layout("/common/_container.html",{title:"个人中心",css:["/assets/modular/frame/user_info.css"],js:["/assets/modular/frame/user_info.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">个人中心</span>
    <span class="layui-breadcrumb pull-right">
        <a href="${ctxPath}/system/console">首页</a>
        <a><cite>个人中心</cite></a>
    </span>
</div>

<div class="layui-fluid">
    <div class="layui-col-md4">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md4">
                        <div class="text-center layui-text">
                            <div class="user-info-head" id="imgHead">
                                <img src="${avatar}" id="headPicImg"/>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="info-item">
                            <span>${name}</span><span style="margin-left: 20px;">${phone}</span>
                        </div>
                        <div class="info-item">
                            <span>部门：${deptName}</span>
                        </div>
                        <div class="info-item">
                            <span>岗位：</span>内容运营
                        </div>
                        <div class="info-item">
                            <span>邀请码：</span>th666888
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 旧的首页内容，先注释了 -->
<!-- 正文开始 -->
<!--<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        &lt;!&ndash; 左 &ndash;&gt;
        <div class="layui-col-sm12 layui-col-md3">
            <div class="layui-card">
                <div class="layui-card-body" style="padding: 25px;">
                    <div class="text-center layui-text">
                        <div class="user-info-head" id="imgHead">
                            <img src="${avatar}"/>
                        </div>
                        <h2 style="padding-top: 20px;">${name}</h2>
                        <p style="padding-top: 8px;">${roleName!} ${deptName!}</p>
                    </div>
                    <div class="layui-text" style="padding-top: 30px;">
                        <div class="info-list-item">
                            <i class="layui-icon layui-icon-notice"></i>
                            <p>${email!}</p>
                        </div>

                        <div class="info-list-item">
                            <i class="layui-icon layui-icon-cellphone"></i>
                            <p>${phone!}</p>
                        </div>
                        <div class="info-list-item">
                            <i class="layui-icon layui-icon-location"></i>
                            <p>中国 北京市 朝阳区 xxx街道 000号</p>
                        </div>
                    </div>
                    <div class="dash"></div>
                    <h3>标签</h3>
                    <div class="layui-badge-list" style="padding-top: 6px;">
                        <span class="layui-badge layui-bg-gray">Guns</span>
                        <span class="layui-badge layui-bg-gray">Roses</span>
                    </div>
                </div>
            </div>
        </div>
        &lt;!&ndash; 右 &ndash;&gt;
        <div class="layui-col-sm12 layui-col-md9">
            <div class="layui-card">
                <div class="layui-card-body layui-text">

                    <div class="layui-tab layui-tab-brief" lay-filter="userInfoTab">
                        <ul class="layui-tab-title">
                            <li class="layui-this">基本信息</li>
                            <li>其他</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-form layui-tab-item layui-show" lay-filter="userInfoForm">
                                <input type="hidden" name="userId"/>
                                <div class="layui-form user-info-form" style="max-width: 400px;padding-top: 25px;">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">账号:</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="account" class="layui-input" lay-verify="required" disabled/>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">性别:</label>
                                        <div class="layui-input-block">
                                            <input type="radio" name="sex" value="M" title="男">
                                            <input type="radio" name="sex" value="F" title="女">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">邮箱:<span style="color: red;">*</span></label>
                                        <div class="layui-input-block">
                                            <input type="email" name="email" class="layui-input" lay-verify="required|email" required/>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">姓名:<span style="color: red;">*</span></label>
                                        <div class="layui-input-block">
                                            <input type="text" name="name" class="layui-input" lay-verify="required" required/>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">出生日期:</label>
                                        <div class="layui-input-block">
                                            <input type="text" id="birthday" name="birthday" class="layui-input"/>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">联系电话:</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="phone" class="layui-input"/>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <button class="layui-btn" lay-filter="userInfoSubmit" lay-submit>
                                                更新基本信息
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            &lt;!&ndash;另一个标签&ndash;&gt;
                            <div class="layui-tab-item" style="padding: 6px 25px 30px 25px;">
                                <div class="bd-list">
                                    <div class="bd-list-item">
                                        <div class="bd-list-item-content">
                                            <div class="bd-list-item-lable">密保手机</div>
                                            <div class="bd-list-item-text">已绑定手机：182****0000</div>
                                        </div>
                                        <a class="bd-list-item-oper">修改</a>
                                    </div>
                                    <div class="bd-list-item">
                                        <div class="bd-list-item-content">
                                            <div class="bd-list-item-lable">密保邮箱</div>
                                            <div class="bd-list-item-text">已绑定邮箱：sn***qq.com</div>
                                        </div>
                                        <a class="bd-list-item-oper">修改</a>
                                    </div>
                                    <div class="bd-list-item">
                                        <div class="bd-list-item-img">
                                            <i class="layui-icon layui-icon-login-qq"
                                               style="color: #3492ED;font-size: 48px;"></i>
                                        </div>
                                        <div class="bd-list-item-content">
                                            <div class="bd-list-item-lable">绑定QQ</div>
                                            <div class="bd-list-item-text">当前未绑定QQ账号</div>
                                        </div>
                                        <a class="bd-list-item-oper">绑定</a>
                                    </div>
                                    <div class="bd-list-item">
                                        <div class="bd-list-item-img">
                                            <i class="layui-icon layui-icon-login-wechat"
                                               style="color: #4DAF29;font-size: 48px;"></i>
                                        </div>
                                        <div class="bd-list-item-content">
                                            <div class="bd-list-item-lable">绑定微信</div>
                                            <div class="bd-list-item-text">当前未绑定绑定微信账号</div>
                                        </div>
                                        <a class="bd-list-item-oper">绑定</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</div>-->
@}