@layout("/common/_container.html",{js:["/pages/modular/sysMessagePage/messageTemplate/js/list.js","/pages/modular/sysMessagePage/js/common.js"]}){


<!-- 页面内容 -->
<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">

    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <!-- 查询条件 -->
            <form class="layui-form" action="">
                <div class="layui-inline">
                    <label class="layui-form-label">消息名称</label>
                    <div class="layui-input-inline">
                        <input type="text" id="messageName" name="messageName" placeholder="请输入消息名称" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
               
                <div class="layui-inline">
                    <label class="layui-form-label">发送类型</label>
                    <div class="layui-input-inline">
                        <select id="receiveMethod" name="receiveMethod">
                        </select>
                    </div>
                </div>
           
                <button class="layui-btn" lay-submit="" lay-filter="search">搜索</button>
                <button class="layui-btn layui-btn-primary" type="reset">重置</button>
                <!-- 添加按钮 -->
                <button id="addMsgBtn" class="layui-btn">新增模板</button>

            </form>

            <!-- 数据表格 -->
            <table id="msgTable" lay-filter="msgTable"></table>

        </div>
    </div>
</div>


<!-- 工具条模板 -->
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
</script>

@}