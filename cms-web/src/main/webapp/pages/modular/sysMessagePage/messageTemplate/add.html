@layout("/common/_container.html",{js:["/pages/modular/sysMessagePage/messageTemplate/js/add.js","/pages/modular/sysMessagePage/js/common.js"],css:["/pages/modular/sysMessagePage/css/add.css"]}){


<div class="layui-container" style="padding: 20px;">

    <form class="layui-form" lay-filter="messageTemplateForm" id="messageTemplateForm">
        <div class="layui-tab-content">
            <input type="hidden" id="id" name="id">
            <!-- Tab 1: 消息基础设置 -->
            <div class="layui-tab-item layui-show">
                <div class="layui-form-item">
                    <label class="layui-form-label">模板名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="tplName" required lay-verify="required" placeholder="请输入消息名称"
                            class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">模板标题</label>
                    <div class="layui-input-block">
                        <input type="text" name="tplTitle" required lay-verify="required" placeholder=""
                            class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">模板内容</label>
                    <div class="layui-input-block">

                        <textarea name="tplContent" placeholder="多行文本框" class="layui-textarea" required
                            lay-verify="required"></textarea>

                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">发送类型</label>
                    <div class="layui-input-block">
                        <select name="msgType" id="receiveMethod" lay-filter="receiveMethod-select-filter">

                        </select>
                    </div>
                </div>
                <div class="layui-form-item" id="tplCodeIdDiv">
                    <label class="layui-form-label">模板CODE</label>
                    <div class="layui-input-block">
                        <input type="text" name="tplCode" id="tplCodeId" placeholder="" class="layui-input">
                    </div>
                </div>
                <!-- 提交按钮 -->
                <div class="layui-form-item">
                    <div class="layui-input-block">

                        <button class="layui-btn" lay-submit lay-filter="submitMessageTemplateForm">保存</button>
                        <!-- <button id="cancelMessageTemplateBtn" class="layui-btn layui-btn-primary">取消</button> -->
                    </div>
                </div>
    </form>
</div>
@}