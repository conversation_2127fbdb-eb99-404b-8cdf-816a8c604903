layui.use(['form', 'jquery', 'layer'], function () {
    var form = layui.form;
    var $ = layui.jquery;
    var layer = layui.layer
    var viewModel = {}
    viewModel.renderTagList = function (data) {
        var ele = document.getElementById("tagId");
        if (!!!ele) {
            return;
        }
        var option = document.createElement("option");
        option.value = '';
        option.text = '请选择';
        document.getElementById("tagId").appendChild(option);
        for (var i = 0; i < data.length; i++) {
            var option = document.createElement("option");
            option.value = data[i].tagId;
            option.text = data[i].tagName;
            document.getElementById("tagId").appendChild(option);
        }
        form.render('select');
    }
    viewModel.renderReceiveMethod = function (data) {
        var ele = document.getElementById("receiveMethod");
        if (!!!ele) {
            return;
        }
        var option = document.createElement("option");
        option.value = '';
        option.text = '请选择';
        document.getElementById("receiveMethod").appendChild(option);
        for (var i = 0; i < data.length; i++) {
            var option = document.createElement("option");
            option.value = data[i].code;
            option.text = data[i].name;
            document.getElementById("receiveMethod").appendChild(option);
        }
        form.render('select');
    }
    viewModel.renderStatus = function (data) {
        var ele = document.getElementById("status");
        if (!!!ele) {
            return;
        }
        var option = document.createElement("option");
        option.value = '';
        option.text = '请选择';
        document.getElementById("status").appendChild(option);
        for (var i = 0; i < data.length; i++) {
            var option = document.createElement("option");
            option.value = data[i].code;
            option.text = data[i].name;
            document.getElementById("status").appendChild(option);
        }
        form.render('select');
    }
    viewModel.renderDayOfWeek = function (data) {
        var ele = document.getElementById("dayOfWeek");
        if (!!!ele) {
            return;
        }
        var option = document.createElement("option");
        option.value = '';
        option.text = '请选择';
        document.getElementById("dayOfWeek").appendChild(option);
        for (var i = 0; i < data.length; i++) {
            var option = document.createElement("option");
            option.value = data[i].code;
            option.text = data[i].name;
            document.getElementById("dayOfWeek").appendChild(option);
        }
        form.render('select');
    }
    viewModel.renderUrgencyLevelList = function (data) {
        var ele = document.getElementById("urgencyLevel");
        if (!!!ele) {
            return;
        }
        var option = document.createElement("option");
        option.value = '';
        option.text = '请选择';
        document.getElementById("urgencyLevel").appendChild(option);
        for (var i = 0; i < data.length; i++) {
            var option = document.createElement("option");
            option.value = data[i].code;
            option.text = data[i].name;
            document.getElementById("urgencyLevel").appendChild(option);
        }
        form.render('select');
    }
    viewModel.queryEmums = function () {
        // 发送 AJAX 请求
        $.ajax({
            url: Feng.ctxPath + '/sysMessage/enums',
            type: 'POST',
            contentType: 'application/json',
            success: function (res) {
                console.log(res.data);
                var data = res.data;
                viewModel.renderTagList(data.messageTagList);
                viewModel.renderReceiveMethod(data.sendTypeList);
                viewModel.renderStatus(data.statusList);
                viewModel.renderDayOfWeek(data.dayOfWeekList);
                viewModel.renderUrgencyLevelList(data.urgencyLevelList);

            },
            error: function (e) {
                console.error(e);
                // 请求错误时的回调函数
                layer.msg('请求异常，请稍后再试[' + e.responseJSON.message + ']');
                //layer.msg('请求异常：' + res.message);
            }
        });
    }


    viewModel.queryEmums();
})