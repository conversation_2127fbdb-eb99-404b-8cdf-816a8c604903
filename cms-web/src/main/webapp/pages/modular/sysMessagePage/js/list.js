layui.use(['table', 'form', 'jquery', 'layer'], function () {
    var table = layui.table;
    var form = layui.form;
    var $ = layui.jquery;
    var layer = layui.layer
    var viewModel = {
        tableId: 'msgTable'
    };
    viewModel.queryData = function () {
        return {
            messageName: $("#messageName").val(),
            receiveMethod: $("#receiveMethod").val(),
            tagId: $("#tagId").val(),
            status: $("#status").val()
        }
    }
    viewModel.deleteData = function (id) {
        //加载
        var loadIndex = layer.load(2);
        $.ajax({
            url: Feng.ctxPath + '/sysMessage/delete?messageId=' + id,
            type: 'GET',
            success: function (res) {
                console.log(res.data);
                table.reload(viewModel.tableId, { where: viewModel.queryData() })
                layer.close(loadIndex);
            },
            error: function (e) {
                console.error(e);
                // 请求错误时的回调函数
                layer.msg('请求异常，请稍后再试[' + e.responseJSON.message + ']');
                //layer.msg('请求异常：' + res.message);
                layer.close(loadIndex);
            }
        });
    }
    // 渲染表格
    table.render({
        elem: "#" + viewModel.tableId,
        url: Feng.ctxPath + '/sysMessage/list', //数据接口
        method: 'post',
        contentType: "application/json",
        where: viewModel.queryData(),
        cols: [[ //表头
            { title: '序号', templet: '<div>{{ d.LAY_INDEX+1 }}</div>', minWidth: 80 }
            , { field: 'title', title: '消息标题', width: 100 },
            , { field: 'messageName', title: '消息名称' }
            , { field: 'interfaceCode', title: '接口标识符' }
            , { field: 'tagName', title: '消息类型', minWidth: 100 }
            , {
                field: 'templateRelDTOS', title: '接收方式', minWidth: 100, templet: function (d) {

                    return d.templateRelDTOS.map(e => {


                        if (e.msgType == 1) {
                            return '  <i class="layui-icon layui-icon-notice" style="font-size: 30px; color: #1E9FFF;"></i> '
                        }
                        if (e.msgType == 2) {
                            return '  <i class="layui-icon layui-icon-email" style="font-size: 30px; color: #1E9FFF;"></i> '
                        }
                        return ''
                    }).join('');
                }
            }
            , {
                field: 'urgencyLevel', title: '紧急程度', minWidth: 100, templet: function (d) {
                    return d.urgentLevel == 1 ? '<span class="urgency-level-normal">' + d.urgencyLevelDesc + '</span>' : (d.urgentLevel == 2 ? '<span class="urgency-level-urgent">' + d.urgencyLevelDesc + '</span>' : '<span class="urgency-level-very_urgent">' + d.urgencyLevelDesc + '</span>');
                }
            }
            , { field: 'pushTime', title: '推送时间' }
            , {
                field: 'status', title: '状态', minWidth: 80, templet: function (d) {
                    return '<input type="checkbox" name="status" value="' + d.messageId + '" lay-skin="switch" lay-text="启用|禁用" ' + (d.status == 1 ? 'checked' : '') + ' lay-filter="statusSwitch">';
                }
            }
            , { fixed: 'right', title: '操作', toolbar: '#barDemo', minWidth: 150 }
        ]],
        height: 'full-98'
        , page: true //开启分页
    });

    // 监听工具条事件
    table.on('tool(msgTable)', function (obj) {
        var data = obj.data;
        if (obj.event === 'edit') {
            layer.open({
                type: 2,
                title: '编辑消息',
                shade: false, // 不显示遮罩
                area: ['100%', '100%'],
                closeBtn: 1,
                offset: '0px',
                content: Feng.ctxPath + '/sysMessagePage/add?messageId=' + data.messageId,
            });
        } else if (obj.event === 'delete') {
            layer.confirm('真的删除么', function (index) {
                layer.close(index);
                viewModel.deleteData(data.messageId);
            });
        }
    });

    // 监听状态开关
    form.on('switch(statusSwitch)', function (data) {
        var messageId = this.value
        console.log(data);
        var val = !!data.value ? 1 : 0;
        layer.confirm('确定要修改状态吗？', function (index) {
            //加载
            var loadIndex = layer.load(2);
            // 发送请求更新状态
            $.ajax({
                url: Feng.ctxPath + '/sysMessage/toggle?messageId=' + messageId + "&status=" + val,
                type: 'GET',
                success: function (res) {
                    layer.msg('状态更新成功');
                    layer.close(index);
                    //关闭加载
                    layer.close(loadIndex);
                }, error: function (res) {
                    layer.msg('状态更新失败');
                    //关闭加载
                    layer.close(loadIndex);
                }
            });
        });



    });

    // 监听搜索提交
    form.on('submit(search)', function (data) {
        console.log(data.field);
        table.reload('msgTable', {
            where: viewModel.queryData()
        });
        return false;
    });
    // 新增消息
    $("#addMsgBtn").click(function () {
        localStorage.setItem('initTemplateSelect', JSON.stringify([]))

        top.layer.open({
            type: 2,
            title: '新增消息',
            shade: false, // 不显示遮罩
            area: ['100%', '100%'],
            closeBtn: 1,
            offset: '0px',
            content: Feng.ctxPath + '/sysMessagePage/add'
        });
    });
    viewModel.toTemplate = function () {
        window.location.href = Feng.ctxPath + '/sysMessagePage/messageTemplate'
    };
    $("#templeMsgBtn").click(function () {
        viewModel.toTemplate();
        return false;

    });
})