@layout("/common/_container.html",{js:["/pages/modular/bizImAutoQuestion/js/bizImAutoQuestion_add.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">添加bizImAutoQuestion</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizImAutoQuestionForm" lay-filter="bizImAutoQuestionForm" class="layui-form model-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">问题</label>
                        <div class="layui-input-inline">
                            <input id="question" name="question" placeholder="请输入question" type="text"
                                   class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">回复</label>
                        <div class="layui-input-inline">
                            <input id="reply" name="reply" placeholder="请输入reply" type="text" class="layui-input"
                                   lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">排序</label>
                        <div class="layui-input-inline">
                            <input id="sort" name="sort" placeholder="请输入sort" type="text" class="layui-input"
                                   lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label">启用</label>
                        <div class="layui-input-inline">
                            <select name="status" id="status" lay-filter="status" lay-verify="required" required>
                                <option value=""></option>
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">问题Key</label>
                            <div class="layui-input-inline">
                                <input id="questionKeys" name="questionKeys" placeholder="请输入questionKeys" type="text"
                                       class="layui-input" lay-verify="required" required/>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item  text-center">
                        <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog"
                                id="backupPage">返回
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@}
