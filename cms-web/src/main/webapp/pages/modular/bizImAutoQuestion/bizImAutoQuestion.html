@layout("/common/_container.html",{js:["/pages/modular/bizImAutoQuestion/js/bizImAutoQuestion.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">bizImAutoQuestion管理</span>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <input type="hidden" id="bizImAutoQuestionId" value="bizImAutoQuestionId"/>
                                <input id="question" class="layui-input" type="text" placeholder="问题"
                                       autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <input id="reply" class="layui-input" type="text" placeholder="回答"
                                       autocomplete="off"/>
                            </div>
                            <div class="layui-inline"><select id="status"></select></div>
                            <div class="layui-inline">
                                <button id="btnSearch" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索
                                </button>
                                @if(shiro.hasPermission("/bizImAutoQuestion/bizImAutoQuestion_add")){
                                <button id="btnAdd" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加
                                </button>
                                @}
                                <button id="btnBack" class="layui-btn icon-btn layui-btn-warm"><i class="layui-icon">&#x1006;</i>关闭
                                </button>
                            </div>
                        </div>
                    </div>
                    <table class="layui-table" id="bizImAutoQuestionTable" lay-filter="bizImAutoQuestionTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tableBar">

    @if(shiro.hasPermission("/bizImAutoQuestion/bizImAutoQuestion_update")){
    <a class="layui-btn layui-btn-primary layui-btn-xs  {{d.status == 1 ? 'layui-btn-danger' : 'layui-btn-normal'}}"
       lay-event="toggleStatus">
        {{d.status == 1 ? '禁用' : '启用'}}
    </a>
    @}

    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>

    @if(shiro.hasPermission("/bizImAutoQuestion/bizImAutoQuestion_update")){
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    @}
    @if(shiro.hasPermission("/bizImAutoQuestion/delete")){
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    @}
</script>
@}

