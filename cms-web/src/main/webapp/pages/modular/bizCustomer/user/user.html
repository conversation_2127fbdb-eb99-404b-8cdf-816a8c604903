@layout("/common/_container.html",{plugins:["ztree"],js:["/pages/modular/bizCustomer/user/js/user.js"]}){

<div class="layui-body-header">
    <span class="layui-body-header-title">客服添加</span>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md3 layui-col-lg2">
            <div class="layui-card">
                <div class="layui-card-body mini-bar">
                    <div class="ztree" id="deptTree"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-sm12 layui-col-md9 layui-col-lg10">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <input id="name" class="layui-input" type="text" placeholder="账号/姓名/手机号"/>
                            </div>
                            <div class="layui-inline">
                                <input id="timeLimit" class="layui-input" type="text" placeholder="注册时间"/>
                            </div>
                            <div class="layui-inline">
                                <button id="btnSearch" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                <button id="btnAdd" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                            </div>
                        </div>
                    </div>
                    <table class="layui-table" id="userTable2" ></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="addCus">添加</a>
</script>

<script type="text/html" id="statusTpl">
    <input type="checkbox" lay-filter="status" value="{{d.userId}}" lay-skin="switch" lay-text="正常|冻结" {{d.status=='ENABLE'?'checked':''}} />
</script>
@}