/**
 * 详情对话框
 */
var BizCustomerInfoDlg = {
    data: {
        id: "",
        seqNum: "",
        loginName: "",
        nickName: "",
        deptName: "",
        deptId: "",
        jiedaiMaxnum: "",
        roleName: "",
        roleId: "",
        createTime: "",
        updateTime: "",
    }
};
layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#createTime'
    });
    laydate.render({
        elem: '#updateTime'
    });
});
var UserInfoDlg = {
    data: {
        deptId: "",
        deptName: ""
    }
};
layui.use(['form', 'ax', 'upload', 'table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var BizCustomer = {
        tableId: "bizCustomerTable"
    };

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizCustomer/detail?bizCustomerId=" + Feng.getUrlParam("bizCustomerId"));
    var result = ajax.start();
    form.val('bizCustomerForm', result.data);

    //图片赋值
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/bizCustomer/update", function (data) {
            Feng.success("更新成功！");
            setTimeout(function () {
                top.layer.closeAll();
            }, 1000);
        }, function (data) {
            Feng.error("更新失败！" + data.responseJSON.message);
            setTimeout(function () {
                top.layer.closeAll();
            }, 1000);
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizCustomer";
    });

    // 点击部门时
    $('#deptName').click(function () {
        var formName = encodeURIComponent("parent.UserInfoDlg.data.deptName");
        var formId = encodeURIComponent("parent.UserInfoDlg.data.deptId");
        var treeUrl = encodeURIComponent("/dept/tree");

        layer.open({
            type: 2,
            title: '部门选择',
            area: ['300px', '400px'],
            content: Feng.ctxPath + '/system/commonTree?formName=' + formName + "&formId=" + formId + "&treeUrl=" + treeUrl,
            end: function () {
                console.log(UserInfoDlg.data);
                $("#deptId").val(UserInfoDlg.data.deptId);
                $("#deptName").val(UserInfoDlg.data.deptName);
            }
        });
    });

    //图片赋值
    $('#iconPicImg').attr("src", Feng.getAliImgUrl(result.data.iconPic, 800, 800))
    //普通图片上传
    upload.render({
        elem: '#uploadiconPic'
        , url: Feng.ctxPath + '/oss/upload'
        , before: function (obj) {
            obj.preview(function (index, file, result) {
                $('#iconPicImg').attr('src', result);
            });
        }
        , done: function (res) {
            $('#iconPicImg').attr('src', res.data.acurl);
            $("#iconPicHidden").val(res.data.ourl);
            Feng.success(res.message);
        }
        , error: function () {
            Feng.error("上传图片失败！");
        }
    });
});
