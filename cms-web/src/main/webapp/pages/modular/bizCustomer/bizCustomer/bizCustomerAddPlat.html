@layout("/common/_container.html",{js:["/pages/modular/bizCustomer/bizCustomer/js/bizCustomer_editAddPlat.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">修改客服设置</span>
</div>

<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizCustomerForm" lay-filter="bizCustomerForm" class="layui-form model-form" style="max-width: 700px;margin: 40px auto;">
                <input id="id" name="id" placeholder="请输入id"  type="hidden" class="layui-input" lay-verify="required"  required/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">序号</label>
                        <div class="layui-input-inline">
                            <input id="seqNum" name="seqNum" placeholder="请输入seqNum" type="text" class="layui-input" disabled required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">登录名称</label>
                        <div class="layui-input-inline">
                            <input id="loginName" name="loginName" placeholder="请输入loginName" type="text" class="layui-input" disabled required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">昵称</label>
                        <div class="layui-input-inline">
                            <input id="nickName" name="nickName" placeholder="请输入nickName" type="text" class="layui-input" disabled required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label">部门</label>
                        <div class="layui-input-block">
                            <input id="deptId" name="deptId" type="hidden">
                            <input id="deptName" name="deptName" placeholder="请输入部门" type="text" class="layui-input" autocomplete="off"/>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label">接待上限</label>
                        <div class="layui-input-inline">
                            <input id="jiedaiMaxnum" name="jiedaiMaxnum" placeholder="请输入jiedaiMaxnum" type="text" class="layui-input"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">客服角色</label>
                        <div class="layui-input-inline">
                            <input id="roleName" name="roleName" placeholder="请输入roleName" type="text" disabled class="layui-input" lay-verify="required"  disabled required/>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">合作平台</label>
                    <div class="layui-input-block" >
                        <!--                            <input id="plat_vals" name="plat_vals" placeholder="请输入plat_vals" type="text" disabled class="layui-input" lay-verify="required"  disabled required/>-->
                        <!-- 复选框容器 -->
                        <div id="platBox" style=" white-space: nowrap; overflow-x: auto;"></div>
                        <!-- 隐藏域存储 plat_vals（若需要回传） -->
                        <input type="hidden" name="platVals" id="platVals" />
                    </div>
                </div>
                <div class="layui-form-item  text-center">
                    <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                    <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                </div>
            </form>
        </div>
    </div>
</div>
@}
