@layout("/common/_container.html",{js:["/pages/modular/bizCustomer/bizCustomer/js/bizCustomer.js"]}){
<style>
    /* 自定义排序按钮样式 */
    .sort-container {
        display: inline-flex;
        flex-direction: row;
        align-items: center;   /* 垂直居中 */
        gap: 6px;
    }
    .sort-btn {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: #f8f8f8;
        border: 1px solid #eee;
        cursor: pointer;
        transition: all 0.2s;
        align-items: center;
        justify-content: center;
    }
    .sort-btn:hover {
        background: #5FB878;
        color: white;
        transform: scale(1.1);
    }
    .sort-btn.disabled {
        opacity: 0.4;
        cursor: not-allowed;
        transform: none;
        background: #f8f8f8;
    }
    /* 编辑单元格高亮 */
    .layui-table-edit {
        border-color: #5FB878 !important;
    }
</style>
<div class="layui-body-header">
    <span class="layui-body-header-title">客服设置管理</span>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <input type="hidden" id="bizCustomerId" value="bizCustomerId"/>
                                <input id="condition" class="layui-input" type="text" placeholder="名称/编码" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <button id="btnSearch" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>

                                <button id="btnAdd" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>

                                <button id="btnBack" class="layui-btn icon-btn layui-btn-warm"><i class="layui-icon">&#x1006;</i>关闭</button>
                            </div>
                        </div>
                    </div>
                    <table class="layui-table" id="bizCustomerTable" lay-filter="bizCustomerTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">详情</a>
    @if(shiro.hasPermission("/bizCustomer/bizCustomer_update")){
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">修改</a>
    @}
    @if(shiro.hasPermission("/bizCustomer/delete")){
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
    @}

    {{# if(d.roleName.indexOf('商务') !== -1){ }}
        <a class="layui-btn layui-btn-danger layui-btn-xs"  lay-event="butPlatEdit">平台编辑</a>
    {{# } }}

</script>

<script type="text/html" id="jiedaiEdit">
    <input type="text" lay-filter="jiedaiMaxnumD" class="layui-input"  value="{{d.jiedaiMaxnum}}" />
</script>
@}

