.multi-line-ellipsis {
  display: -webkit-box;           /* 必须结合的显示模式 */
  -webkit-box-orient: vertical;   /* 内容垂直排列 */
  -webkit-line-clamp: 3;          /* 显示最大行数 */
  overflow: hidden;               /* 超出隐藏 */
  text-overflow: ellipsis;        /* 最后一行显示省略号 */
  line-height: 1.4em;             /* 行高（可选） */
  max-height: calc(1.4em * 3);    /* 最大高度 = 行高 × 行数（可选） */
}

.single-line-ellipsis {
  white-space: nowrap;       /* 不换行 */
  overflow: hidden;          /* 隐藏溢出内容 */
  text-overflow: ellipsis;   /* 添加省略号 */
}