/**
 * 详情对话框
 */
var SettleInfluencerOrderInfoDlg = {
    data: {
       id:"",
       influencerName:"",
       influencerId:"",
       projectId:"",
       projectName:"",
       phoneNumber:"",
       companyTalentRatio:"",
       settlementBatch:"",
       platformNickname:"",
       platformId:"",
       earningsAmount:"",
       payableAmount:"",
       withdrawnAmount:"",
       unwithdrawnAmount:"",
       isArrived:"",
       arrivalIssue:"",
       workArea:"",
       groupName:"",
       businessContact:"",
       statusLog:"",
       createId:"",
       createName:"",
       deptId:"",
       deptName:"",
       companyId:"",
       companyName:"",
       updateTime:"",
       createTime:"",
    }
};
layui.use('laydate', function(){
    var laydate = layui.laydate;
        laydate.render({
                elem: '#updateTime'
         });
        laydate.render({
                elem: '#createTime'
         });
});
layui.use(['form', 'ax','upload','table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var SettleInfluencerOrder = {
            tableId: "settleInfluencerOrderTable"
     };

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/settleInfluencerOrder/detail?settleInfluencerOrderId=" + Feng.getUrlParam("settleInfluencerOrderId"));
    var result = ajax.start();
    form.val('settleInfluencerOrderForm', result.data);

    //图片赋值
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/settleInfluencerOrder/update", function (data) {
            Feng.success("更新成功！");
           setTimeout(function () {
                            window.location.href = Feng.ctxPath + "/settleInfluencerOrder";
           }, 1000);
        }, function (data) {
            Feng.error("更新失败！" + data.responseJSON.message);
           setTimeout(function () {
                           top.layer.closeAll();
                       }, 1000);
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/settleInfluencerOrder";
    });
});
