/**
 * 详情对话框
 */
var SettleInfluencerOrderInfoDlg = {
    data: {
        id: "",
        influencerName: "",
        influencerId: "",
        projectId: "",
        projectName: "",
        phoneNumber: "",
        companyTalentRatio: "",
        settlementBatch: "",
        platformNickname: "",
        platformId: "",
        earningsAmount: "",
        payableAmount: "",
        withdrawnAmount: "",
        unwithdrawnAmount: "",
        isArrived: "",
        arrivalIssue: "",
        workArea: "",
        groupName: "",
        businessContact: "",
        statusLog: "",
        createId: "",
        createName: "",
        deptId: "",
        deptName: "",
        companyId: "",
        companyName: "",
        updateTime: "",
        createTime: "",
    }
};
layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#updateTime'
    });
    laydate.render({
        elem: '#createTime'
    });
    // 绑定日期选择器到指定输入框
    laydate.render({
        elem: '#zhouqiStart',
        trigger: 'click',
        done: function(value, date){
            // 结束日期不能早于开始日期
            laydate.render({
                elem: '#zhouqiEnd',
                min: value
            });
        }
    });

});



layui.use(['form', 'ax', 'upload', 'table', 'element'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var SettleInfluencerOrder = {
        tableId: "settleInfluencerOrderTable"
    };

    var element = layui.element;
    element.render('step');

    var destReqUrl3 = Feng.ctxPath + "/settleProjects/list?page=0&limit=10000";
    selector.init(destReqUrl3, 'project', 'id', 'projectName', 'projectName', function (data) {
        console.log('123123123' + data.value)
        var reqPurl = Feng.ctxPath + "/settleProjects/detail?settleProjectsId=" + data.value;
        var ajax = new $ax(reqPurl, function (rpsData) {
            console.log('rpsData ' + rpsData)

            $("#projectCode").val(rpsData.data.projectCode);
            $("#createTime").val(rpsData.data.createTime);
            $("#settleStatusName").val(rpsData.data.settleStatusName);

            $("#zhouqi").val(rpsData.data.projectStarttime + "-" + rpsData.data.projectEndtime);
            $("#compName").val(rpsData.data.compName);
            var pltstr = rpsData.data.yijiPlatform;
            if (rpsData.data.erjiPlatform) {
                pltstr = pltstr + "-" + rpsData.data.erjiPlatform
                if (rpsData.data.sanjiPlatform) {
                    pltstr = pltstr + "-" + rpsData.data.sanjiPlatform
                }
            }

            $("#servicePlat").val((pltstr));
            $("#projectTypeName").val((rpsData.data.projectTypeName));
            $("#createBy").val((rpsData.data.businessOwnerName));
        });
        ajax.start();

    });

    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/settlecore/add", function (data) {
            Feng.success("添加成功！");
            window.location.href = Feng.ctxPath + "/settleInfluencerOrder";
        }, function (data) {
            Feng.error("添加失败！" + data.responseJSON.message)
            window.location.href = Feng.ctxPath + "/settleInfluencerOrder";
        });
        var settleSrc = $('#fileList .file-item a').map(function() {
            return $(this).attr('srcSource');
        }).get().join(',');

        var fujianSrcs = $('#fuzhuUpdBut .file-item a').map(function() {
            return $(this).attr('srcSource').join(',');
        }).get();


        ajax.set(data.field);
        ajax.start();

        return false;
    });

    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/settleInfluencerOrder";
    });
    function resetHdVals(){
        var settleSrc = $('#fuzhuFile .file-item a').map(function() {
            return $(this).attr('srcSource');
        }).get().join(',');
        $("#fuzhuFiles").val(settleSrc);


        var settleSrcd = $('#fileList .file-item a').map(function() {
            return $(this).attr('srcSource');
        }).get().join(',');

        $("#jiesuanFile").val(settleSrcd);
    }

    //普通图片上传
    upload.render({
        elem: '#settleIdBut',
        url: Feng.ctxPath + '/oss/ff/uploadPri',
        accept: 'file',
        multiple: false,
        exts: 'xlsx|csv|xls|pdf',
        done: function (res) {
            $('#fileList').append(`
                <div class="file-item layui-inline">
                    <span class="file-name"><a class="layui-btn layui-btn-primary layui-btn-sm" href="`+res.data.src+`" srcSource="`+res.data.srcSource+`" download="`+res.data.title+`"> <i class="layui-icon layui-icon-download"></i>`+res.data.title+`</a></span>
                    <i class="layui-icon layui-icon-close delete-btn" data-id="`+res.data.title+`"></i>
                </div>
            `);

            resetHdVals();
            Feng.success(res.message);
        },

        error: function () {
            Feng.error("上传结算文件失败！");
        }
    });

    // 初始化上传组件‌:ml-citation{ref="8" data="citationList"}
    upload.render({
        elem: '#fuzhuUpdBut',
        multiple: true,
        accept: 'file',
        url: Feng.ctxPath + '/oss/ff/uploadPri',
        done: function (res) {
            // 插入带删除按钮的文件项‌:ml-citation{ref="5" data="citationList"}
            $('#fuzhuFile').append(`
                <div class="file-item layui-inline">
                    <span class="file-name"><a class="layui-btn layui-btn-primary layui-btn-sm" href="`+res.data.src+`" srcSource="`+res.data.srcSource+`" download="`+res.data.title+`"> <i class="layui-icon layui-icon-download"></i>`+res.data.title+`</a></span>
                    <i class="layui-icon layui-icon-close delete-btn" data-id="`+res.data.title+`"></i>
                </div>
            `);

            resetHdVals();
            Feng.success(res.message);
        }
    });

    // 删除事件委托‌:ml-citation{ref="2" data="citationList"}
    $(document).on('click', '.delete-btn', function () {
        const fileId = $(this).data('id');
        layer.confirm('确认删除该文件？', function (index) {
            // 调用删除接口‌:ml-citation{ref="7" data="citationList"}
            $(this).closest('.file-item').remove();
            resetHdVals();
        });
    });
});
