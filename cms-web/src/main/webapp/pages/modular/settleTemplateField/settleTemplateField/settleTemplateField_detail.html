@layout("/common/_container.html",{js:["/pages/modular/settleTemplateField/settleTemplateField/js/settleTemplateField_detail.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">模版字段详情</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="settleTemplateFieldForm" lay-filter="settleTemplateFieldForm" class="layui-form model-form"  style="max-width: 700px;margin: 40px auto;">
            <input name="" type="hidden"/>
 <div class="layui-form-item">        <div class="layui-inline">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label"></label>
            <div class="layui-input-inline">
                <input id="templateId" name="templateId" placeholder="请输入templateId" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">列标识(A/B/C)</label>
            <div class="layui-input-inline">
                <input id="columnCode" name="columnCode" placeholder="请输入columnCode" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">原始列名</label>
            <div class="layui-input-inline">
                <input id="originalName" name="originalName" placeholder="请输入originalName" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
 </div>  <div class="layui-form-item">        <div class="layui-inline">
            <label class="layui-form-label">字段注释</label>
            <div class="layui-input-inline">
                <input id="fieldComment" name="fieldComment" placeholder="请输入fieldComment" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">创建人ID</label>
            <div class="layui-input-inline">
                <input id="createId" name="createId" placeholder="请输入createId" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">创建人</label>
            <div class="layui-input-inline">
                <input id="createName" name="createName" placeholder="请输入createName" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">部门ID</label>
            <div class="layui-input-inline">
                <input id="deptId" name="deptId" placeholder="请输入deptId" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
 </div>  <div class="layui-form-item">        <div class="layui-inline">
            <label class="layui-form-label">部门</label>
            <div class="layui-input-inline">
                <input id="deptName" name="deptName" placeholder="请输入deptName" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">公司ID</label>
            <div class="layui-input-inline">
                <input id="companyId" name="companyId" placeholder="请输入companyId" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">公司名称</label>
            <div class="layui-input-inline">
                <input id="companyName" name="companyName" placeholder="请输入companyName" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">更新时间</label>
            <div class="layui-input-inline">
                <input id="updateTime" name="updateTime" placeholder="请输入updateTime" type="text" class="layui-input" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item  text-center">
            <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
        </div>
        </form>
    </div>
    </div>
</div>
@}
