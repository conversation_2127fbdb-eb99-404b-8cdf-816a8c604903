layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var BiWorktype = {
        tableId: "biWorktypeTable"
    };

    /**
     * 初始化表格的列
     */
    BiWorktype.initColumn = function () {
        return [[
            {type: 'checkbox'},
{field: 'id', minWidth: 100,hide: false,title: '编码'},
{field: 'typeName', minWidth: 100,hide: false,title: '分类名称'},
{field: 'createTime', minWidth: 100,hide: false,title: ''},
{field: 'updateTime', minWidth: 100,hide: false,title: ''},
            {align: 'center', toolbar: '#tableBar',fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BiWorktype.tableId,
        url: Feng.ctxPath + '/biWorktype/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: BiWorktype.initColumn()
    });
    /**
     * 点击查询按钮
     */
    BiWorktype.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(BiWorktype.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    BiWorktype.openAddDlg = function () {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加工单分类',
            content:  Feng.ctxPath + '/biWorktype/biWorktype_add',
            end: function () {
                layer.closeAll('loading');
                table.reload(BiWorktype.tableId);
            }
        });
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    BiWorktype.openEditDlg = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '添加工单分类',
            content:  Feng.ctxPath + '/biWorktype/biWorktype_update?biWorktypeId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BiWorktype.tableId);
            }
        });
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    BiWorktype.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/biWorktype/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(BiWorktype.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("biWorktypeId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 工单分类详情
     */
    BiWorktype.detail = function (data) {
        
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'], //宽高
            title: '工单分类详情',
            content:  Feng.ctxPath + '/biWorktype/biWorktype_detail?biWorktypeId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BiWorktype.tableId);
            }
        });
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BiWorktype.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        BiWorktype.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + BiWorktype.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            BiWorktype.openEditDlg(data);
        } else if (layEvent === 'delete') {
            BiWorktype.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            BiWorktype.detail(data);
        }
    });
});


