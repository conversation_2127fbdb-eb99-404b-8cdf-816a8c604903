/**
 * 详情对话框
 */
var SettleTemplateExcelInfoDlg = {
    data: {
        id: "",
        templateName: "",
        createId: "",
        createName: "",
        deptId: "",
        deptName: "",
        companyId: "",
        companyName: "",
        createdTime: "",
        updateTime: "",
    }
};

layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#createdTime'
    });
    laydate.render({
        elem: '#updateTime'
    });
});


let templateMap = {}; // 存储字段映射关系
let excelUplUrl = "";

layui.use(['form', 'ax', 'treeSelect', 'upload', 'table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var SettleTemplateExcel = {
        tableId: "settleTemplateExcelTable"
    };
    form.render("select");

    $("#submitBut").click(function (e) {
        e.preventDefault();
        const mapping = Object.keys(templateMap).map(col => ({
            originalCode: col,
            originalName: templateMap[col].original,
            targetName: templateMap[col].targetName
        }));

        var platIdVal = $("#platformId").val();
        var templateNameVal = $("#templateName").val();
        var duiyingFieldVa = $("#duiyingField").val();

        // 发送到后端
        $.ajax({
            url: Feng.ctxPath + '/settleTemplateExcel/add',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({columns: mapping,duiyingField:duiyingFieldVa, platId: platIdVal, templateName: templateNameVal, excelUplUrlSc: excelUplUrl}),
            success: function (res) {
                Feng.success("添加成功！");
                setTimeout(function () {
                    window.location = Feng.ctxPath + '/settleTemplateExcel';
                });
                window.location = Feng.ctxPath + '/settleTemplateExcel';
            }
        });
    });
    var treeSelect = layui.treeSelect;
    // 初始化下拉选择器
    treeSelect.render({
        // css选择器，推荐使用id
        elem: '#platformId',
        // 请求地址
        data: Feng.ctxPath + '/bizPlatform/listtree',
        // ajax请求方式：post/get
        type: 'post',
        // 返回数据中主键的属性名称，默认值为id
        key: {
            id: 'id',
        },
        // 节点点击回调函数
        click: function (d) {
            console.log(d);
            $("#platform").val(d.name);
        }
    });
    function renderTable(headers) {
        var $ = layui.jquery;
        const options = ['达人ID', '达人名称', '流水金额']; // 固定映射字段‌:ml-citation{ref="5,7" data="citationList"}
        $('#previewTable tbody').empty().html(
            headers.map(h => `
            <tr>
                <td>${h.col}</td>
                <td>${h.title}</td>
                <td>
                    <select lay-filter="seletProps" data-col="${h.col}" onchange="updateMap(this)">
                        <option value="">请选择</option>
                        ${options.map(o => `<option value="${o}">${o}</option>`).join('')}
                    </select>
                </td>
            </tr>`
            ).join('')
        );
        headers.map(h =>
            templateMap[h.col] = {
                original: h.title,
            }
        );

        form.render();
        form.on('select(seletProps)', function(data){
            console.log('选中值：', data.value); // 输出当前选中值
            var selectElement = data.elem;
            updateMap(selectElement)
        });
    }
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/settleTemplateExcel";
    });

    upload.render({
        elem: '#uploadBtn',
        accept: 'file',
        exts: 'xlsx|xls|csv',
        url: Feng.ctxPath + '/settleTemplateExcel/upload',
        done: function (res) {
            console.log('上传完成');
            excelUplUrl = res.data.ourl
            if (res.data.ourl.endsWith('.csv') || res.data.ourl.endsWith('.xlsx') || res.data.ourl.endsWith('.xls')) {
                renderTable(res.data.columnInfos);
                form.render("select");
            }
            //在此解析excel以及 pdf等
            Feng.success(res.message);
        },
        choose: function (obj) {
        },
        error: function () {
            // layui.layer.msg('文件上传接口异常');
        }
    });



});
function updateMap(select) {
    debugger
    var $ = layui.jquery;
    const col = $(select).data('col');
    templateMap[col] = {
        original: $(select).closest('tr').find('td:eq(1)').text(),
        targetName: $(select).val().trim()
    };
}
