@layout("/common/_container.html",{js:["/pages/modular/wfType/wfType/js/wfType_detail.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">流程类型详情</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="wfTypeForm" lay-filter="wfTypeForm" class="layui-form model-form" style="max-width: 700px;margin: 40px auto;">
                <input name="" type="hidden"/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">编号</label>
                        <div class="layui-input-inline">
                            <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <!-- 表单项容器 -->
                    <div class="layui-form-item">
                        <label class="layui-form-label layui-col-md6">流程类型名称</label>
                        <div class="layui-input-block layui-col-md6">
                            <input type="text" id="typeName" name="typeName"
                                   placeholder="请输入流程类型名称"
                                   class="layui-input"
                                   lay-verify="required"
                                   autocomplete="off">
                        </div>
                    </div>

                    <!-- 双列布局 -->
                    <div class="layui-row">
                        <div class="layui-col-md12">
                            <div class="layui-form-item">
                                <label class="layui-form-label layui-col-md6">是否启用</label>
                                <div class="layui-input-block layui-col-md6">
                                    <input type="checkbox" name="isActive"  id="isActive"
                                           lay-skin="switch"
                                           lay-text="启用|禁用"
                                           checked>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 多行文本域 -->
                    <div class="layui-form-item layui-col-md12">
                        <label class="layui-form-label layui-col-md6">备注说明</label>
                        <div class="layui-input-block layui-col-md6">
                          <textarea class="layui-textarea"
                                    id="description"
                                    name="description"
                                    placeholder="请输入流程说明（200字以内）"
                                    rows="3"
                                    style="resize: vertical;"></textarea>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">创建时间</label>
                        <div class="layui-input-inline">
                            <input id="createTime" name="createTime" placeholder="请输入createTime" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">更新时间</label>
                        <div class="layui-input-inline">
                            <input id="updateTime" name="updateTime" placeholder="请输入updateTime" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                    </div>
            </form>
        </div>
    </div>
</div>
@}
