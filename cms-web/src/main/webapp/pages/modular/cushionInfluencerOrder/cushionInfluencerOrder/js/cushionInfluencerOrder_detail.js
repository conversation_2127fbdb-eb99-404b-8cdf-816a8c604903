/**
 * 详情对话框
 */
var CushionInfluencerOrderInfoDlg = {
    data: {
       id:"",
       influencerName:"",
       influencerId:"",
       projectId:"",
       projectName:"",
       phoneNumber:"",
       companyTalentRatio:"",
       cushionmentBatch:"",
       platformNickname:"",
       platformId:"",
       earningsAmount:"",
       payableAmount:"",
       withdrawnAmount:"",
       unwithdrawnAmount:"",
       isArrived:"",
       arrivalIssue:"",
       workArea:"",
       groupName:"",
       businessContact:"",
       statusLog:"",
       createId:"",
       createName:"",
       deptId:"",
       deptName:"",
       companyId:"",
       companyName:"",
       updateTime:"",
       createTime:"",
    }
};
layui.use(['form','ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/cushionInfluencerOrder/detail?cushionInfluencerOrderId=" + Feng.getUrlParam("cushionInfluencerOrderId"));
    var result = ajax.start();
     form.val('cushionInfluencerOrderForm', result.data);
    //图片赋值
    $("#id").attr("disabled","disabled");
    $("#influencerName").attr("disabled","disabled");
    $("#influencerId").attr("disabled","disabled");
    $("#projectId").attr("disabled","disabled");
    $("#projectName").attr("disabled","disabled");
    $("#phoneNumber").attr("disabled","disabled");
    $("#companyTalentRatio").attr("disabled","disabled");
    $("#cushionmentBatch").attr("disabled","disabled");
    $("#platformNickname").attr("disabled","disabled");
    $("#platformId").attr("disabled","disabled");
    $("#earningsAmount").attr("disabled","disabled");
    $("#payableAmount").attr("disabled","disabled");
    $("#withdrawnAmount").attr("disabled","disabled");
    $("#unwithdrawnAmount").attr("disabled","disabled");
    $("#isArrived").attr("disabled","disabled");
    $("#arrivalIssue").attr("disabled","disabled");
    $("#workArea").attr("disabled","disabled");
    $("#groupName").attr("disabled","disabled");
    $("#businessContact").attr("disabled","disabled");
    $("#statusLog").attr("disabled","disabled");
    $("#createId").attr("disabled","disabled");
    $("#createName").attr("disabled","disabled");
    $("#deptId").attr("disabled","disabled");
    $("#deptName").attr("disabled","disabled");
    $("#companyId").attr("disabled","disabled");
    $("#companyName").attr("disabled","disabled");
    $("#updateTime").attr("disabled","disabled");
    $("#createTime").attr("disabled","disabled");
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/cushionInfluencerOrder";
    });
});
