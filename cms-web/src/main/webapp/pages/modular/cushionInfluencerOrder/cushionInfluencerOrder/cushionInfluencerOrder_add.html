@layout("/common/_container.html",{js:["/pages/modular/cushion/js/cushion_add.js"]}){
<style>
    .layui-card-header {
        padding: 15px 20px;
    }

    .status-tag {
        margin-left: 10px;
        color: #FF5722;
    }

    .upload-file {
        color: #1E9FFF;
    }

    .horizontal-flow {
        display: flex;
        justify-content: space-between;
        padding: 40px 20px;
        position: relative;
        background: white;
    }

    .flow-node {
        position: relative;
        width: 120px;
        text-align: center;
        z-index: 2;
    }

    .node-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #409EFF;
        margin: 0 auto;
        color: white;
        line-height: 40px;
        font-weight: bold;
    }

    .node-line {
        position: absolute;
        height: 2px;
        background: #000;
        top: 20px;
        left: 50%;
        right: -50%;
        transform: translateX(60px);
    }

    .node-title {
        margin-top: 10px;
        font-size: 16px;
        color: #333;
    }

    .node-status {
        color: #67C23A;
        margin-top: 5px;
    }

    .rejected .node-circle {
        background: #F56C6C;
    }

    .rejected .node-status {
        color: #F56C6C;
    }
    .file-item {
        margin: 5px 10px 5px 0;
        padding: 3px 8px;
        background: #f8f8f8;
        border-radius: 3px;
    }
    .delete-btn {
        margin-left: 5px;
        color: #ff5722;
        cursor: pointer;
    }
    .delete-btn:hover {
        color: #c00;
    }

</style>
<div class="layui-container">
    <!-- 基础信息模块 -->
    <form id="cushionInfluencerOrderForm" lay-filter="cushionInfluencerOrderForm" class="layui-form model-form">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-24">
                <div class="layui-card">
                    <div class="layui-row">
                        <!-- 项目核心信息 -->
                        <div class="layui-col-6">
                            <div class="layui-form-item">
                                <label class="layui-form-label">项目选择</label>
                                <div class="layui-input-block">
                                    <input type="hidden" id="projectName" name="projectName" lay-filter="projectName">
                                    <select id="project" name="project" lay-filter="project" lay-search="">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-header">基础信息</div>
                    <div class="layui-card-body">
                        <div class="layui-row">
                            <!-- 项目核心信息 -->
                            <div class="layui-col-6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">项目编号</label>
                                    <div class="layui-input-block">
                                        <input type="text" class="layui-input" id="projectCode" name="projectCode" disabled>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">创建时间</label>
                                    <div class="layui-input-block">
                                        <input type="text" class="layui-input" id="createTime" name="createTime" disabled>
                                    </div>
                                </div>
                            </div>

                            <!-- 状态与周期 -->
                            <div class="layui-col-6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">项目状态</label>
                                    <div class="layui-input-block">
                                        <input type="text" class="layui-input" id="settleStatusName" name="settleStatusName" disabled>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">项目周期</label>
                                    <div class="layui-input-block">
                                        <input type="text" class="layui-input" id="zhouqi" name="zhouqi" disabled>
                                    </div>
                                </div>
                            </div>

                            <!-- 参与主体 -->
                            <div class="layui-col-12">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">参与主体</label>
                                    <div class="layui-input-block">
                                        <textarea class="layui-textarea" disabled id="compName"></textarea>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">创建人</label>
                                    <div class="layui-input-block">
                                        <input type="text" class="layui-input" id="createBy" disabled>
                                    </div>
                                </div>

                            </div>

                            <div class="layui-col-6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">服务平台</label>
                                    <div class="layui-input-block">
                                        <input type="text" class="layui-input" id="servicePlat" disabled>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">项目类型</label>
                                    <div class="layui-input-block">
                                        <input type="text" class="layui-input" id="projectTypeName" disabled>
                                    </div>
                                </div>
                            </div>


                            <div class="layui-col-6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">备注</label>
                                    <div class="layui-input-block">
                                         <textarea  name="remark" id="remark"
                                                 placeholder="备注"
                                                 class="layui-textarea"
                                                 style="min-height: 100px;"></textarea>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 验收结算模块 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-24">
                <div class="layui-card">
                    <div class="layui-card-header">验收结算</div>
                    <div class="layui-card-body">
                        <div class="layui-row">
                            <div class="layui-col-12">
                                <!-- 删除重复的结算周期模块 -->
                                <div class="layui-form-item">
                                    <label class="layui-form-label">结算周期</label>
                                    <div class="layui-inline">
                                        <input id="zhouqiStart" name="zhouqiStart"
                                               placeholder="开始时间"
                                               class="layui-input date-picker"         style="width: 160px;">
                                    </div>
                                    <div class="layui-inline" style="margin: 0 5px;">至</div>
                                    <div class="layui-inline">
                                        <input id="zhouqiEnd" name="zhouqiEnd"
                                               placeholder="结束时间"
                                               class="layui-input date-picker"   style="width: 160px;">
                                    </div>
                                </div>

                                <!-- 文件上传区域 -->
                                <div class="layui-form-item">
                                    <label class="layui-form-label">结算文件</label>
                                    <div class="layui-input-block">
                                        <button type="button" class="layui-btn layui-btn-normal" id="cushionIdBut">
                                            <i class="layui-icon layui-icon-upload"></i>上传结算文件
                                        </button>
                                        <div class="file-list" id="fileList">
                                            <!-- 动态插入的文件项 -->
                                        </div>
                                        <div class="layui-word-aux">支持格式：.xlsx, .pdf（单个文件不超过10MB）</div>
                                        <input type="hidden" id="jiesuanFile" name="jiesuanFile"/>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">其他辅助文件</label>
                                    <div class="layui-input-block">
                                        <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="fuzhuUpdBut">
                                            <i class="layui-icon layui-icon-upload-drag"></i>上传文件
                                        </button>
                                        <div id="fuzhuFile" class="layui-clear">
                                            <!-- 动态插入文件项 -->
                                        </div>
                                        <input type="hidden" id="fuzhuFiles" name="fuzhuFiles"/>
                                    </div>
                                </div>

                            </div>
                            <div class="layui-col-12">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">备注说明</label>
                                    <div class="layui-input-block">
                                        <textarea class="layui-textarea"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核流程模块 -->
        <div class="horizontal-flow">
            <!-- 发起人 -->
            <div class="flow-node">
                <div class="node-circle">1</div>
                <div class="node-line"></div>
                <div class="node-title">郭部</div>
                <div class="node-status">发起结算</div>
            </div>

            <!-- 财务审核 -->
            <div class="flow-node rejected">
                <div class="node-circle">2</div>
                <div class="node-line"></div>
                <div class="node-title">财务xxx</div>
                <div class="node-status">审核未通过</div>
            </div>

            <!-- 最终审批 -->
            <div class="flow-node">
                <div class="node-circle">3</div>
                <div class="node-title">老板</div>
                <div class="node-status">审核通过</div>
            </div>
        </div>

        <!-- 操作区 -->
        <div class="layui-row">
            <div class="layui-col-24" style="text-align: center; padding: 20px 0;">
                <button class="layui-btn layui-btn-lg" id="submitBut" lay-filter="btnSubmit" lay-submit style="width: 200px;">提交</button>
                <button class="layui-btn layui-btn-primary layui-btn-lg" id="backupPage" ew-event="closeDialog"  style="width: 200px; margin-left: 30px;">取消</button>
            </div>
        </div>
    </form>
</div>


@}