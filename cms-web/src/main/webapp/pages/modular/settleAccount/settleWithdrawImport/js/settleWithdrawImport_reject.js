

layui.use(['form', 'ax', 'upload', 'table', 'element'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var element = layui.element;
    element.render('step');

    $(function() {
        const id = Feng.getUrlParam("id"); // 从URL参数获取
        $("#rejectId").val(id); // 设置到隐藏域
    });

    // 防止重复提交的标志
    var isSubmitting = false;

    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        // 防止重复提交
        if (isSubmitting) {
            return false;
        }

        isSubmitting = true;
        var loadIndex = layer.load(2, {shade: 0.5}); // 参数2表示加载图标样式
        const postData = JSON.stringify(data.field); // 包含隐藏域id

        $.ajax({
            url: Feng.ctxPath + "/settleWithdrawImport/reject",
            async: true, // 必须为异步
            data: postData,
            method: "post",
            dataType: "json",
            contentType: "application/json",
            success: function (res) {
                layer.close(loadIndex); // 关闭加载层
                Feng.success("驳回成功！");

                // 正确关闭弹窗
                setTimeout(function() {
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }, 1000);
            },
            error: function (xhr, status, err) { // 新增错误处理
                layer.close(loadIndex); // 关闭加载层
                isSubmitting = false; // 重置提交状态

                const errorData = xhr.responseJSON || {};
                const errorMsg = errorData.message || xhr.responseText || "未知错误";

                console.error("提交失败:", xhr);
                Feng.error("驳回失败！" + errorMsg);
            }
        });

        return false;
    });

    //返回按钮
    $("#backupPage").click(function (e) {
        e.preventDefault();  // 阻止默认跳转
        window.location.href = Feng.ctxPath + "/settleOrder";
    });

    function resetHdVals() {
        var settleSrc = $('#rejectFileDiv .file-item a').map(function () {
            return $(this).attr('srcSource');
        }).get().join(',');
        console.log('隐藏input值：'+$("#rejectFile").val());
        console.log('上传文件的url：'+settleSrc)
        $("#rejectFile").val(settleSrc);
    }

    //普通图片上传
    upload.render({
        elem: '#rejectUpdBut',
        url: Feng.ctxPath + '/oss/ff/uploadPri',
        accept: 'file',
        multiple: false,
        // exts: 'xlsx|csv|xls|pdf',
        done: function (res) {
            $('#rejectFileDiv').append(`
                <div class="file-item layui-inline">
                    <span class="file-name"><a class="layui-btn layui-btn-primary layui-btn-sm" href="` + res.data.src + `" srcSource="` + res.data.srcSource + `" download="` + res.data.title + `"> <i class="layui-icon layui-icon-download"></i>` + res.data.title + `</a></span>
                    <i class="layui-icon layui-icon-close delete-btn" data-id="` + res.data.title + `"></i>
                </div>
            `);


            Feng.success(res.message);
            resetHdVals();
        },

        error: function () {
            Feng.error("上传结算文件失败！");
        }
    });

});
