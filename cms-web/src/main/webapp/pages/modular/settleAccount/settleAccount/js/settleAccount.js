layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var SettleAccount = {
        tableId: "settleAccountTable"
    };
    function setFullHeight() {
        document.documentElement.style.height =  "1px";
        document.body.style.height =  "1px";
    }
    window.addEventListener('resize', setFullHeight);
    setFullHeight(); // 初始化‌:ml-citation{ref="2,3" data="citationList"}

    /**
     * 初始化表格的列
     */
    SettleAccount.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {field: 'id', minWidth: 100, hide: false, title: '主键（序号）'},
            {field: 'userId', minWidth: 100, hide: false, title: '达人ID'},
            {field: 'nickName', minWidth: 100, hide: false, title: '达人昵称'},
            {field: 'userTel', minWidth: 100, hide: false, title: '达人手机号'},
            {field: 'userName', minWidth: 100, hide: false, title: '达人姓名'},
            {field: 'accountNo', minWidth: 100, hide: false, title: '账户号'},
            {field: 'zongAmount', minWidth: 100, hide: false, title: '总收益'},
            {field: 'tixianAmount', minWidth: 100, hide: false, title: '提现金额'},
            {field: 'amount', minWidth: 100, hide: false, title: '可用余额'},
            {field: 'dianfuAmount', minWidth: 100, hide: false, title: '垫付收益'},
            {field: 'accountStatusName', minWidth: 100, hide: false, title: '账户状态'},
            {field: 'createId', minWidth: 100, hide: true, title: '创建人ID'},
            {field: 'createName', minWidth: 100, hide: true, title: '创建人'},
            {field: 'deptId', minWidth: 100, hide: true, title: '部门ID'},
            {field: 'deptName', minWidth: 100, hide: true, title: '部门'},
            {field: 'companyId', minWidth: 100, hide: true, title: '公司ID'},
            {field: 'companyName', minWidth: 100, hide: true, title: '公司名称'},
            {field: 'updateTime', minWidth: 100, hide: true, title: '更新时间'},
            {field: 'createTime', minWidth: 100, hide: false, title: '创建时间'},
            {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + SettleAccount.tableId,
        url: Feng.ctxPath + '/settleAccount/list',
        page: true,
        height: "full-150",
        limit: 20,
        cols: SettleAccount.initColumn()
    });
    /**
     * 点击查询按钮
     */
    SettleAccount.search = function () {
        var queryData = {};
        queryData['nickName'] = $("#nickName").val();
        queryData['darenId'] = $("#darenId").val();
        queryData['darenTel'] = $("#darenTel").val();
        queryData['darenshiming'] = $("#darenshiming").val();
        queryData['zijinStatus'] = $("#zijinStatus").val();
        queryData['zijinAmt'] = $("#zijinAmt").val();

        table.reload(SettleAccount.tableId, {where: queryData});
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    SettleAccount.dongjie = function (data,isDj) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/settleAccount/dongjie", function (data) {
                Feng.success("冻结成功!");
                table.reload(SettleAccount.tableId);
            }, function (data) {
                Feng.error("冻结失败!" + data.responseJSON.message + "!");
            });
            ajax.set("settleAccountId", data.id);
            ajax.set("dongjFlg", isDj);
            ajax.start();
        };
        Feng.confirm("是否冻结?", operation);
    };

    /**
     * 资金账户详情
     */
    SettleAccount.logList = function (data) {
        window.location = Feng.ctxPath + '/settleAccountLog?accountNo=' + data.accountNo
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        SettleAccount.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        SettleAccount.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + SettleAccount.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'dongjie') {
            SettleAccount.dongjie(data,1);
        } else if (layEvent === 'jiedong') {
            SettleAccount.dongjie(data,2);
        } else if (layEvent === 'zijinLiushui') {
            SettleAccount.logList(data);
        }
    });
});


