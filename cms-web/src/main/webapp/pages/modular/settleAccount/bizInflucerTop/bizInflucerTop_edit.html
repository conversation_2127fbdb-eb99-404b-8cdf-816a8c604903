@layout("/common/_container.html",{js:["/pages/modular/settleAccount/bizInflucerTop/js/bizInflucerTop_edit.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">修改达人排行</span>
</div>

<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizInflucerTopForm" lay-filter="bizInflucerTopForm" class="layui-form model-form"  style="max-width: 700px;margin: 40px auto;">
            <input name="" type="hidden"/>
 <div class="layui-form-item">                <div class="layui-inline">
                    <label class="layui-form-label">主键ID（自增）</label>
                    <div class="layui-input-inline">
                        <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">达人ID</label>
                    <div class="layui-input-inline">
                        <input id="inflcId" name="inflcId" placeholder="请输入inflcId" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">达人昵称</label>
                    <div class="layui-input-inline">
                        <input id="inflcName" name="inflcName" placeholder="请输入inflcName" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-inline">
                        <input id="serNum" name="serNum" placeholder="请输入serNum" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
 </div>  <div class="layui-form-item">                <div class="layui-inline">
                    <label class="layui-form-label">榜单类型</label>
                    <div class="layui-input-inline">
                        <input id="topType" name="topType" placeholder="请输入topType" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">达人标签</label>
                    <div class="layui-input-inline">
                        <input id="tagSt" name="tagSt" placeholder="请输入tagSt" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
            <div class="layui-form-item">
                <label class="layui-form-label">达人头像</label>
                <div class="layui-input-inline">
                    <div class="layui-upload">
                        <div class="layui-upload-list">
                            <img class="layui-upload-img" style="width: 200px;height: 200px" id="inflcPicImg">
                            <input type="hidden" name="inflcPic" id="inflcPicHidden">
                            <p id="demoText"></p>
                        </div>
                        <button type="button" class="layui-btn" id="uploadinflcPic">上传图片</button>
                    </div>
                </div>
            </div>
                <div class="layui-inline">
                    <label class="layui-form-label">平台ID</label>
                    <div class="layui-input-inline">
                        <input id="platId" name="platId" placeholder="请输入platId" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
 </div>  <div class="layui-form-item">                <div class="layui-inline">
                    <label class="layui-form-label">平台名称</label>
                    <div class="layui-input-inline">
                        <input id="platName" name="platName" placeholder="请输入platName" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">预估收益</label>
                    <div class="layui-input-inline">
                        <input id="shouyi" name="shouyi" placeholder="请输入shouyi" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">排行状态</label>
                    <div class="layui-input-inline">
                        <input id="topStatus" name="topStatus" placeholder="请输入topStatus" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">创建人ID</label>
                    <div class="layui-input-inline">
                        <input id="createId" name="createId" placeholder="请输入createId" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
 </div>  <div class="layui-form-item">                <div class="layui-inline">
                    <label class="layui-form-label">创建人</label>
                    <div class="layui-input-inline">
                        <input id="createName" name="createName" placeholder="请输入createName" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">部门ID</label>
                    <div class="layui-input-inline">
                        <input id="deptId" name="deptId" placeholder="请输入deptId" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">部门</label>
                    <div class="layui-input-inline">
                        <input id="deptName" name="deptName" placeholder="请输入deptName" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">公司ID</label>
                    <div class="layui-input-inline">
                        <input id="companyId" name="companyId" placeholder="请输入companyId" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
 </div>  <div class="layui-form-item">                <div class="layui-inline">
                    <label class="layui-form-label">公司名称</label>
                    <div class="layui-input-inline">
                        <input id="companyName" name="companyName" placeholder="请输入companyName" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">更新时间</label>
                    <div class="layui-input-inline">
                        <input id="updateTime" name="updateTime" placeholder="请输入updateTime" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">创建时间</label>
                    <div class="layui-input-inline">
                        <input id="createTime" name="createTime" placeholder="请输入createTime" type="text" class="layui-input" lay-verify="required" required/>
                    </div>
                </div>
            <div class="layui-form-item  text-center">
                <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
            </div>
            </div>
            </form>
        </div>
    </div>
</div>
@}
