@layout("/common/_container.html",{bg:"bg-white",js:["/assets/modular/system/menu/menu_add.js"]}){
<form id="menuForm" lay-filter="menuForm" class="layui-form model-form">
	<input name="menuId" type="hidden"/>
	<div class="layui-form-item">
		<label class="layui-form-label">名称<span style="color: red;">*</span></label>
		<div class="layui-input-block">
			<input name="name" placeholder="请输入名称" type="text" class="layui-input" lay-verify="required" required/>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">菜单编号<span style="color: red;">*</span></label>
		<div class="layui-input-block">
			<input name="code" placeholder="请输入菜单编号" type="text" class="layui-input" lay-verify="required" required/>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">父级编号<span style="color: red;">*</span></label>
		<div class="layui-input-block">
			<input id="pid" name="pid" type="hidden">
			<input id="pcodeName" name="pcodeName" placeholder="请输入父级编号" type="text" class="layui-input" autocomplete="off" lay-verify="required" required/>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">是否是菜单<span style="color: red;">*</span></label>
		<div class="layui-input-block">
			<input type="radio" name="menuFlag" value="Y" title="是" checked/>
			<input type="radio" name="menuFlag" value="N" title="不是"/>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">请求地址<span style="color: red;">*</span></label>
		<div class="layui-input-block">
			<input id="url" name="url" placeholder="请输入请求地址" type="text" class="layui-input" autocomplete="off"/>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">排序</label>
		<div class="layui-input-block">
			<input name="sort" placeholder="请输入排序" type="text" class="layui-input"/>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">图标</label>
		<div class="layui-input-block">
			<input name="icon" placeholder="请输入图标" type="text" class="layui-input"/>
		</div>
	</div>
	<div class="layui-form-item text-right">
		<button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
		<button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
	</div>
</form>
@}
