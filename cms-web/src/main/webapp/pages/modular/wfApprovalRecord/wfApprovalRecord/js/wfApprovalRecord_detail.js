/**
 * 详情对话框
 */
var WfApprovalRecordInfoDlg = {
    data: {
       id:"",
       requestId:"",
       approverId:"",
       approverName:"",
       approvalResult:"",
       approvalComment:"",
       createTime:"",
    }
};
layui.use(['form','ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/wfApprovalRecord/detail?wfApprovalRecordId=" + Feng.getUrlParam("wfApprovalRecordId"));
    var result = ajax.start();
     form.val('wfApprovalRecordForm', result.data);
    //图片赋值
    $("#id").attr("disabled","disabled");
    $("#requestId").attr("disabled","disabled");
    $("#approverId").attr("disabled","disabled");
    $("#approverName").attr("disabled","disabled");
    $("#approvalResult").attr("disabled","disabled");
    $("#approvalComment").attr("disabled","disabled");
    $("#createTime").attr("disabled","disabled");
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/wfApprovalRecord";
    });
});
