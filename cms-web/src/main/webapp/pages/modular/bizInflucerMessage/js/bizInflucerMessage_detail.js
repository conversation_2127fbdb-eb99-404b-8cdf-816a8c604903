/**
 * 详情对话框
 */
var BizInflucerMessageInfoDlg = {
    data: {
       id:"",
       influcerId:"",
       title:"",
       status:"",
       updateTime:"",
    }
};
layui.use(['form','ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/bizInflucerMessage/detail?bizInflucerMessageId=" + Feng.getUrlParam("bizInflucerMessageId"));
    var result = ajax.start();
     form.val('bizInflucerMessageForm', result.data);
    //图片赋值
    $("#id").attr("disabled","disabled");
    $("#influcerId").attr("disabled","disabled");
    $("#title").attr("disabled","disabled");
    $("#status").attr("disabled","disabled");
    $("#updateTime").attr("disabled","disabled");
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizInflucerMessage";
    });
});
