layui.use(['table', 'ax', 'form'], function () {
        var $ = layui.$;
        var table = layui.table;
        var $ax = layui.ax;
        var admin = layui.admin;
        var form = layui.form;
        var laydate = layui.laydate;

        /**
         * 字典类型表管理
         */
        var BizInflucer = {
            tableId: "bizInflucerTable",
            selectedIds: [] // 存储选中ID的数组
        };

        //日期时间范围
        laydate.render({
            elem: '#createTime'
            ,type: 'datetime'
            ,range: true
        });


    /**
         * 初始化表格的列
         */
        BizInflucer.initColumn = function () {
            return [[
                {type: 'checkbox'},
                {field: 'id', minWidth: 100, hide: true, title: '主键'},
                {
                    align: 'center', title: '序号', minWidth: 30, templet: function (d) {
                        return d.LAY_INDEX + 1;
                    }
                },
                {align: 'center', field: 'influcerId', minWidth: 100, hide: false, title: '达人ID'},
                {align: 'center', field: 'loginName', minWidth: 100, hide: true, title: '登录名'},
                {align: 'center', field: 'nikeName', minWidth: 100, hide: false, title: '头像/昵称', templet: renderUserInfo},
                {align: 'center', field: 'loginTel', minWidth: 100, hide: false, title: '手机号'},

                {align: 'center', field: 'isVerification', sort: true, title: '授权平台及认证状态', templet: renderAuthStatus},
                {align: 'center', field: 'businessName', sort: true, title: '授权商务', templet: renderBusiness},

                {align: 'center', field: 'isVerification', sort: true, title: '实名状态', templet: renderVerificationStatus},
                {align: 'center', field: 'platformAuth', sort: true, title: '是否有明月号', templet: renderCheckAuth},
                {align: 'center', field: 'createTime', minWidth: 100, hide: false, title: '注册时间'},

                // {field: 'password', minWidth: 100, hide: true, title: '密码'},
                // {field: 'authToken', minWidth: 100, hide: true, title: '登录凭证'},
                // {field: 'accid', minWidth: 100, hide: true, title: '云信accid'},
                // {field: 'acctoken', minWidth: 100, hide: true, title: '云信token'},
                // {field: 'name', minWidth: 100, hide: true, title: '姓名'},
                // {field: 'card', minWidth: 100, hide: true, title: '身份证号'},

                {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 240, title: '操作'}
            ]];
        };

        //用户信息显示逻辑
        function renderUserInfo(d) {
            if (d.icon == null || d.icon === ''){
                return `
                    ${d.nikeName || '-'}
                `;
            }
            return `
                <img style="width:32px;height: 32px;border-radius:50%;object-fit:cover;" src="${d.icon || '-'}" class="layui-upload-img">
                ${d.nikeName || '-'}
            `;
        }

        //实名认证显示逻辑
        function renderVerificationStatus(d) {

            var color = '';
            var text = '';

            if (d.isVerification === 1) {
                color = 'green';
                text = '已实名';
            } else if (d.isVerification === -1) {
                color = 'red';
                text = '实名失败';
            } else {
                color = 'grey';
                text = '待实名';
            }

            return `
            <div style="
                text-align: center;
                width: 100%;
                color: ${color};
            ">
                <span style="
                    display: inline-block;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background-color: ${color};
                    margin-right: 6px;
                "></span>
                ${text}
            </div>
        `;
        }

        //logo显示逻辑
        function renderCheckAuth(d) {

            let pa = d.platformAuth;
            let imgStyle = "";

            // 如果状态为1，添加灰色滤镜
            if (pa == null || pa === 0) {
                imgStyle += "filter: grayscale(100%); opacity: 0.6;";
            }

            return `
            <img style="${imgStyle}"  src="${d.platformLogo}" class="layui-upload-img">
        `;
        }

        //平台认证显示逻辑
        function renderAuthStatus(d) {

            // 判断imgPic是字符串还是数组
            var business = d.authPlatforms;

            return `
            <div style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: 100%;
                gap: 5px;
            ">

            ${business.map(bus => {
                let color = '';
                switch (bus.platformStatus) {
                    case '未认证':
                        color = 'blue';
                        break;
                    case '待审核':
                        color = 'orange';
                        break;
                    case '已认证':
                        color = 'green';
                        break;
                    case '未通过':
                        color = 'red';
                        break;
                    default:
                        color = 'black';
                }

                return `
                    <div style="
                        text-align: center;
                        width: 100%;
                    ">
                        <img style="width:20px;height: 20px;" src="${bus.platformImg}" class="layui-upload-img">
                        <span>${bus.platformName || '-'}</span>
                        <span style="
                            color: ${color};
                            display: inline-block;
                            padding: 0px 2px;
                            border: 1px solid;
                            border-color: ${color};
                            border-radius: 10px; /* 圆角形成圈的样式，可按需调整大小 */
                            margin-right: 5px;
                            margin-left: 10px;
                        ">${bus.platformStatus || '-'}
                        </span>
                    </div>
                `;
            }).join('')
            }
            </div>
        `;
        }

        //平台商务显示逻辑
        function renderBusiness(d) {

            // 判断imgPic是字符串还是数组
            var business = d.businessName;

            return `
            <div style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: 100%;
                gap: 5px;
            ">
            <div style="
                        text-align: center;
                        width: 100%;
                    ">
                        <span style="
                            display: inline-block;
                            padding: 1px 1px;
                            border-radius: 10px;
                            margin-right: 5px;
                        ">${business || '-'}
                        </span>
                    </div>
            </div>
        `;
        }

        // 渲染表格
        var tableResult = table.render({
            elem: '#' + BizInflucer.tableId,
            url: Feng.ctxPath + '/bizInflucer/list',
            page: true,
            height: "full-98",
            limit: 20,
            cols: BizInflucer.initColumn(),

            done: function () {
                // 调整表格单元格高度
                $('.layui-table-cell').css('height', 'auto');
            }
        });

        // 全选监听
        form.on('checkbox(allChoose)', function (obj) {
            var tableElem = $(obj.elem).closest('.layui-table-view');
            var checkboxes = tableElem.find('tbody input[type="checkbox"]');

            checkboxes.each(function () {
                this.checked = obj.elem.checked;
                table.setCheckbox(this.parentNode, this.checked ? 'true' : '');
            });

            form.render('checkbox');
        });

        // 获取选中数据
        form.on('submit(getSelected)', function () {

            var checkStatus = table.checkStatus('bizInflucerTable');
            var selectedData = checkStatus.data;

            if (selectedData.length === 0) {
                layer.msg('请至少选择一行数据');
                return;
            }

            var ids = selectedData.map(function (item) {
                return item.id;
            });
            // layer.alert('选中ID：' + ids.join(', ') + '<br>完整数据：<pre>' + JSON.stringify(selectedData, null, 2) + '</pre>', {
            //     title: '选中数据',
            //     area: ['600px', '400px']
            // });

            var influcerIds = ids.join(', ');//BizInflucer.selectedIds.map(String).join(',');

            BizInflucer.openPushDlg(influcerIds);
        });


        //授权平台
        var platformReqUrl = Feng.ctxPath + "/bizInflucerPlatform/platformList";
        selector.initSelected(platformReqUrl, 'platformId', 'id', 'name', 'questTypeName', null);

        //认证状态
        const approveOptions = [
            {"value": 0, "name": "待审核"},
            {"value": 1, "name": "已认证"},
            {"value": -1, "name": "未通过"}
        ]
        selector.initStaticSelected('platformStatus', 'value', 'name', approveOptions, null, null);

        //实名状态
        const statusOptions = [
            {"value": 1, "name": "已实名"},
            {"value": -1, "name": "实名失败"},
            {"value": 0, "name": "待实名"}
        ]
        selector.initStaticSelected('isVerification', 'value', 'name', statusOptions, null, null);

        //授权商务
        var roleReqUrl = Feng.ctxPath + "/mgr/userList?roleName=商务";
        selector.initSelected(roleReqUrl, 'businessId', 'userId', 'name', 'questTypeName', null);




        /**
         * 点击查询按钮
         */
        BizInflucer.search = function () {
            var queryData = {};
            queryData['keywords'] = $("#keywords").val();
            queryData['phone'] = $("#phone").val();
            queryData['platformId'] = $("#platformId").val();
            queryData['platformStatus'] = $("#platformStatus").val();

            queryData['isVerification'] = $("#isVerification").val();
            queryData['businessId'] = $("#businessId").val();
            queryData['createTime'] = $("#createTime").val();
            table.reload(BizInflucer.tableId, {where: queryData});
        };

        // 添加按钮点击事件
        $('#btnReset').click(function () {

            //关键词
            var keywordsInput = document.getElementById('keywords');
            keywordsInput.value = '';
            form.render('input');
            //手机号
            var phoneInput = document.getElementById('phone');
            phoneInput.value = '';
            form.render('input');

            //授权平台
            var platformIdInput = document.getElementById('platformId');
            platformIdInput.value = '';
            form.render('select');

            //认证状态
            var platformStatusInput = document.getElementById('platformStatus');
            platformStatusInput.value = '';
            form.render('select');

            //实名状态
            var isVerificationSelect = document.getElementById('isVerification');
            isVerificationSelect.value = '';
            form.render('select');

            //授权商务
            var businessIdSelect = document.getElementById('businessId');
            businessIdSelect.value = '';
            form.render('select');

            //注册时间
            var createTimeInput = document.getElementById('createTime');
            createTimeInput.value = '';
        });
        /**
         * 弹出添加对话框
         */
        BizInflucer.openAddDlg = function () {
            admin.putTempData('formOk', false);
            top.layui.admin.open({
                type: 2,
                area: ['950px', '800px'], //宽高
                title: '达人认证新增',
                content: Feng.ctxPath + '/bizInflucer/bizInflucer_add',
                end: function () {
                    layer.closeAll('loading');
                    table.reload(BizInflucer.tableId);
                }
            });
        };

        /**
         * 点击编辑
         *
         * @param data 点击按钮时候的行数据
         */
        BizInflucer.openEditDlg = function (data) {
            admin.putTempData('formOk', false);
            top.layui.admin.open({
                type: 2,
                area: ['950px', '600px'], //宽高
                title: '添加达人认证',
                content: Feng.ctxPath + '/bizInflucer/bizInflucer_update?bizInflucerId=' + data.id,
                end: function () {
                    layer.closeAll('loading');
                    table.reload(BizInflucer.tableId);
                }
            });
        };

        /**
         * 点击删除
         *
         * @param data 点击按钮时候的行数据
         */
        BizInflucer.onDeleteItem = function (data) {
            var operation = function () {
                var ajax = new $ax(Feng.ctxPath + "/bizInflucer/delete", function (data) {
                    Feng.success("删除成功!");
                    table.reload(BizInflucer.tableId);
                }, function (data) {
                    Feng.error("删除失败!" + data.responseJSON.message + "!");
                });
                ajax.set("bizInflucerId", data.id);
                ajax.start();
            };
            Feng.confirm("是否删除?", operation);
        };


        /**
         * 达人认证详情
         */
        BizInflucer.detail = function (data) {
            admin.putTempData('formOk', false);
            top.layui.admin.open({
                type: 2,
                area: ['1050px', '800px'], //宽高
                title: '达人详情',
                content: Feng.ctxPath + '/bizInflucer/bizInflucer_detail?bizInflucerId=' + data.id,
                end: function () {
                    layer.closeAll('loading');
                    table.reload(BizInflucer.tableId);
                }
            });
        };

        BizInflucer.reAuth = function (data) {
            top.layui.admin.open({
                type: 2,
                area: ['950px', '800px'], //宽高
                title: '达人重新认证',
                content: Feng.ctxPath + '/bizInflucer/bizInflucer_reauth?bizInflucerId='+ data.id,
                end: function () {
                    layer.closeAll('loading');
                    table.reload(BizInflucer.tableId);
                }
            });
        };

        /**
         * 客服与达人沟通
         * @param data
         */
        BizInflucer.butTlk = function (data) {
            window.open(Feng.ctxPath + "/im/customer/toImWorkspace?inflerId=" + data.id);
        };

        //获取当前被选中的行数据
        BizInflucer.tableDataOncheck = function (){
            var checkStatus = table.checkStatus(BizInflucer.tableId); // test 是表格的 lay-id 属性值
            return  checkStatus.data; // 获取当前所有被选中的行数据
    };

        // 搜索按钮点击事件
        $('#btnSearch').click(function () {
            BizInflucer.search();
        });

        // 添加按钮点击事件
        $('#btnAdd').click(function () {
            BizInflucer.openAddDlg();
        });

        //生成邀请码
        $('#btnCrtInvate').click(function (){
            $.ajax({
                url: Feng.ctxPath + '/bizInflucer/generate-invate-code',
                type: 'GET',
                success: function(res) {
                    var data = res.data;
                    if (data){
                        layer.alert(data)
                    }else {
                        layer.msg(res.message, { icon: 1 });
                    }
                },
                error: function(err) {
                    layer.msg('回收失败', { icon: 2 });
                }
            });
        });

        //分配商务
        $('#btnBound').click(function (){
            var dataOncheck = BizInflucer.tableDataOncheck();
            var ids = dataOncheck.map(function(item) { return item.id; });
            if (!dataOncheck || dataOncheck.length<1){
                layui.layer.alert('请选择要分配的任达人!');
                return;
            }
            layer.open({
                type: 1,
                area: ['420px', '420px'], // 宽高
                // area: ['600px', '400px'], // 宽高
                title: '选择商务人员',
                content:`<div class="layui-form" lay-filter="filter-test-layer" style="margin: 16px;">
                            <div class="demo-login-container">
                                <div class="layui-form-item">
                                    <div class="layui-input-wrap">
                                        <div class="layui-input-prefix">
                                            <i class="layui-icon layui-icon-username"></i>
                                        </div>
                                        <select id="boundBusinessId" name="boundBusinessId"  lay-search=""></select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <button id="btnSubmit" class="layui-btn layui-btn-fluid" lay-submit lay-filter="bound-business">确认分配</button>
                                </div>
                            </div>
                        </div>`,
                success: function (){
                    form.render();
                    form.on('submit(bound-business)',function (data){
                        var boundBusinessId = $("#boundBusinessId").val();
                        $.ajax({
                            url: Feng.ctxPath + '/bizInflucer/bind-influcer',
                            type: 'POST',
                            contentType: 'application/json', // 发送 JSON 数据
                            // contentType: 'multipart/form-data',
                            // data: {'influcerIds':ids,'businessId':boundBusinessId},
                            data: JSON.stringify({'influcerIds':ids,'businessId':boundBusinessId}),
                            // data: JSON.stringify({'influcerIds':ids,'businessId':boundBusinessId}),
                            success: function(res) {
                                var data = res.data;
                                if (data){
                                    layer.alert(data)
                                }else {
                                    layer.msg(res.message, { icon: 1 });
                                }
                            },
                            error: function(err) {
                                layer.msg('分配失败', { icon: 2 });
                            }
                        });
                    });
                }
            });
            selector.initSelected(roleReqUrl, 'boundBusinessId', 'userId', 'name', 'questTypeName', null);
        });

        // 工具条点击事件
        table.on('tool(' + BizInflucer.tableId + ')', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'edit') {
                BizInflucer.openEditDlg(data);
            } else if (layEvent === 'delete') {
                BizInflucer.onDeleteItem(data);
            } else if (layEvent === 'detail') {
                BizInflucer.detail(data);
            } else if (layEvent === 'butTlk') {
                //butTlk
                BizInflucer.butTlk(data)
            }else if (layEvent === 'reAuth') {
                //butTlk
                BizInflucer.reAuth(data)
            }
        });


        // // 新增：监听复选框变化事件
        // table.on('checkbox(' + BizInflucer.tableId + ')', function (obj) {
        //     var checked = obj.checked; // 当前是否选中状态
        //     var data = obj.data; // 当前行数据
        //
        //     if (checked) {
        //         // 选中时添加ID到数组
        //         if (BizInflucer.selectedIds.indexOf(data.id) === -1) {
        //             BizInflucer.selectedIds.push(data.id);
        //         }
        //     } else {
        //         // 取消选中时从数组移除
        //         var index = BizInflucer.selectedIds.indexOf(data.id);
        //         if (index !== -1) {
        //             BizInflucer.selectedIds.splice(index, 1);
        //         }
        //     }
        //
        //     console.log("当前选中IDs：", BizInflucer.selectedIds);
        // });

        // // 新增：监听全选/全不选事件
        // table.on('checkbox(all)', function (obj) {
        //     var checked = obj.checked; // 是否全选
        //     var data = table.cache[BizInflucer.tableId]; // 获取当前页所有数据
        //
        //     if (checked) {
        //         // 全选时添加所有ID
        //         data.forEach(function (item) {
        //             if (BizInflucer.selectedIds.indexOf(item.id) === -1) {
        //                 BizInflucer.selectedIds.push(item.id);
        //             }
        //         });
        //     } else {
        //         // 取消全选时移除当前页所有ID
        //         data.forEach(function (item) {
        //             var index = BizInflucer.selectedIds.indexOf(item.id);
        //             if (index !== -1) {
        //                 BizInflucer.selectedIds.splice(index, 1);
        //             }
        //         });
        //     }
        //
        //     console.log("全选操作后选中IDs：", BizInflucer.selectedIds);
        // });

        // 添加按钮点击事件
        // $('#btnPush').click(function () {
        //
        //     if (BizInflucer.selectedIds.length === 0) {
        //         layer.msg('请至少选择一行数据');
        //         return;
        //     }
        //
        //     // console.log("准备推送的IDs：", BizInflucer.selectedIds);
        //
        //     var influcerIds = BizInflucer.selectedIds.map(String).join(',');
        //
        //     BizInflucer.openPushDlg(influcerIds);
        // });


        BizInflucer.openPushDlg = function (influcerIds) {
            admin.putTempData('formOk', false);

            top.layui.admin.open({
                type: 2,
                area: ['950px', '600px'], //宽高
                title: 'Push达人认证',
                content: Feng.ctxPath + '/bizInflucer/bizInflucer_push?influcerIds=' + influcerIds,
                end: function () {
                    layer.closeAll('loading');
                    table.reload(BizInflucer.tableId);
                }
            });
        };
    }
)
;


