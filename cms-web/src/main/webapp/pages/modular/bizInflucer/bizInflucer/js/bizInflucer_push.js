/**
 * 详情对话框
 */
var BizInflucerInfoDlg = {
    data: {
        influcerIds: "",
        title: ""
    }
};
layui.use('laydate', function () {
    var laydate = layui.laydate;
    // laydate.render({
    //         elem: '#createTime'
    //  });
    // laydate.render({
    //         elem: '#updateTime'
    //  });
});
layui.use(['form', 'ax', 'upload', 'table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var BizInflucer = {
        tableId: "bizInflucerTable"
    };

    //获取详情信息，填充表单
    // var ajax = new $ax(Feng.ctxPath + "/bizInflucer/detail?bizInflucerId=" + Feng.getUrlParam("bizInflucerId"));
    // var result = ajax.start();
    // form.val('bizInflucerForm', result.data);

    //图片赋值

    //表单提交事件
    // form.on('submit(btnSubmit)', function (data) {
    //
    //     console.log("dddddddddddddddddddddd" + data)
    //
    //     var ajax = new $ax(Feng.ctxPath + "/bizInflucer/push", function (data) {
    //         Feng.success("更新成功！");
    //         setTimeout(function () {
    //             top.layer.closeAll();
    //         }, 1000);
    //     }, function (data) {
    //         Feng.error("更新失败！" + data.responseJSON.message);
    //         setTimeout(function () {
    //             top.layer.closeAll();
    //         }, 1000);
    //     });
    //     ajax.set(data.field);
    //     ajax.start();
    //
    //     return false;
    // });


    /**
     * 表单提交方法
     */
    form.on('submit(btnSubmit)', function (data) {
        var loading = layer.load(2); // 显示加载中

        var influcerIds = $("#influcerIds").val();
        var title = data.field.title;
        var context = data.field.context;

        console.log("aaaaaaaaaaaaaaaaaaaaaaaaaa")
        if (!title) {
            layer.close(loading);
            Feng.error("推送内容不能为空");
            return false;
        }

        // 构造请求数据
        var requestData = {
            "influcerIds": influcerIds,
            "title": title,
            "context": context
        };

        // 发送AJAX请求
        $.ajax({
            url: Feng.ctxPath + "/bizInflucer/push",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(requestData),
            success: function (res) {
                layer.close(loading);
                if (res.success) {
                    Feng.success("推送成功！");
                    // 关闭当前弹窗
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                } else {
                    Feng.error("推送失败：" + res.message);
                }
            },
            error: function (error) {
                layer.close(loading);
                Feng.error("推送请求异常：" + error.responseJSON.message);
            }
        });

        return false; // 阻止表单默认提交
    });


    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/bizInflucer";
    });
});
