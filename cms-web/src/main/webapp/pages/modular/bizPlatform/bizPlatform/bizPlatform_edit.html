@layout("/common/_container.html",{js:["/pages/modular/bizPlatform/bizPlatform/js/bizPlatform_edit.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">修改平台设置</span>
</div>

<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizPlatformForm" lay-filter="bizPlatformForm" class="layui-form model-form" style="max-width: 700px;margin: 40px auto;">
                <input name="" type="hidden"/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">id</label>
                        <div class="layui-input-inline">
                            <input id="id" name="id" placeholder="请输入id" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">父级</label>
                        <div class="layui-input-inline">
                            <input type="hidden" id="parentName" name="parentName" lay-filter="parentName">
                            <input type="text" id="parentId"  name="parentId" lay-filter="parentId" placeholder="选择父节点" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">第几层</label>
                        <div class="layui-input-inline">
                            <input id="levels" name="levels" placeholder="请输入levels" type="text" class="layui-input" lay-verify="required" required disabled/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">平台名称</label>
                        <div class="layui-input-inline">
                            <input id="platName" name="platName" placeholder="请输入platName" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">平台简称</label>
                        <div class="layui-input-inline">
                            <input id="jiancheng" name="jiancheng" placeholder="请输入jiancheng" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">平台简介</label>
                        <div class="layui-input-inline">
                          <textarea placeholder="请输入平台简介" class="layui-textarea" id="introduction"
                                    name="introduction"></textarea>
                        </div>
                    </div>

                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">平台图标</label>
                    <div class="layui-input-inline">
                        <div class="layui-upload">
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" style="width: 200px;height: 200px" id="iconPicImg">
                                <input type="hidden" name="iconPic" id="iconPicHidden">
                                <p id="demoText"></p>
                            </div>
                            <button type="button" class="layui-btn" id="uploadiconPic">上传图片</button>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@}
