@layout("/common/_container.html",{js:["/pages/modular/bizOperateLog/bizOperateLog/js/bizOperateLog.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">操作日志管理</span>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <input type="hidden" id="bizOperateLogId" value="bizOperateLogId"/>
                                <input id="staffName" class="layui-input" type="text" placeholder="操作人名称" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <input id="staffLoginName" class="layui-input" type="text" placeholder="操作人账号" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <input id="module" class="layui-input" type="text" placeholder="模块名称" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <select id="operationType" lay-search="">
                                    <option value="">操作类型</option>
                                    <option value="1">新增</option>
                                    <option value="2">修改</option>
                                    <option value="3">删除</option>
                                    <option value="4">查询</option>
                                    <option value="5">导出</option>
                                    <option value="6">导入</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input id="targetName" class="layui-input" type="text" placeholder="操作对象" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <input id="menu1" class="layui-input" type="text" placeholder="一级菜单" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <input id="startTime" class="layui-input" type="text" placeholder="开始时间" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <input id="endTime" class="layui-input" type="text" placeholder="结束时间" autocomplete="off"/>
                            </div>
                            <div class="layui-inline">
                                <button id="btnSearch" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                            </div>
                        </div>
                    </div>
                    <table class="layui-table" id="bizOperateLogTable" lay-filter="bizOperateLogTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail">查看详情</a>
</script>
@}
