layui.use(['table', 'ax', 'laydate', 'form'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    var laydate = layui.laydate;
    var form = layui.form;

    /**
     * 操作日志管理
     */
    var BizOperateLog = {
        tableId: "bizOperateLogTable"
    };

    /**
     * 初始化表格的列
     */
    BizOperateLog.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {field: 'id', hide: true, title: 'ID'},
            {field: 'staffName', minWidth: 100, title: '操作人'},
            {field: 'staffLoginName', minWidth: 120, title: '操作账号'},
            {field: 'module', minWidth: 100, title: '模块名称'},
            {field: 'operationTypeName', minWidth: 80, title: '操作类型'},
            {field: 'targetName', minWidth: 120, title: '操作对象'},
            {field: 'menu1', minWidth: 100, title: '一级菜单'},
            {field: 'menu2', minWidth: 100, title: '二级菜单'},
            {field: 'menu3', minWidth: 100, title: '三级菜单'},
            {field: 'operation1', minWidth: 100, title: '一级操作'},
            {field: 'operation2', minWidth: 100, title: '二级操作'},
            {field: 'operationDetail', minWidth: 150, title: '操作详情'},
            {field: 'ip', minWidth: 120, title: 'IP地址'},
            {field: 'createTime', minWidth: 160, title: '操作时间', templet: function(d) {
                return d.createTime ? d.createTime.replace('T', ' ') : '';
            }},
            {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };

    // 初始化日期选择器
    laydate.render({
        elem: '#startTime',
        type: 'date'
    });

    laydate.render({
        elem: '#endTime',
        type: 'date'
    });

    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BizOperateLog.tableId,
        url: Feng.ctxPath + '/bizOperateLog/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: BizOperateLog.initColumn()
    });

    /**
     * 点击查询按钮
     */
    BizOperateLog.search = function () {
        var queryData = {};
        queryData['staffName'] = $("#staffName").val();
        queryData['staffLoginName'] = $("#staffLoginName").val();
        queryData['module'] = $("#module").val();
        queryData['operationType'] = $("#operationType").val();
        queryData['targetName'] = $("#targetName").val();
        queryData['menu1'] = $("#menu1").val();
        queryData['startTime'] = $("#startTime").val();
        queryData['endTime'] = $("#endTime").val();
        table.reload(BizOperateLog.tableId, {where: queryData});
    };



    /**
     * 操作日志详情
     */
    BizOperateLog.detail = function (data) {
        top.layui.admin.open({
            type: 2,
            area: ['950px', '600px'],
            title: '操作日志详情',
            content: Feng.ctxPath + '/bizOperateLog/bizOperateLog_detail?bizOperateLogId=' + data.id,
            end: function () {
                layer.closeAll('loading');
                table.reload(BizOperateLog.tableId);
            }
        });
    };

    /**
     * 清空操作日志
     */
    BizOperateLog.clearLogs = function () {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizOperateLog/clear", function (data) {
                Feng.success("清空成功!");
                table.reload(BizOperateLog.tableId);
            }, function (data) {
                Feng.error("清空失败!" + data.responseJSON.message + "!");
            });
            ajax.start();
        };
        Feng.confirm("确定要清空所有操作日志吗？此操作不可恢复！", operation);
    };

    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BizOperateLog.search();
    });



    // 清空日志按钮点击事件
    $('#btnClear').click(function () {
        BizOperateLog.clearLogs();
    });

    // 工具条点击事件
    table.on('tool(' + BizOperateLog.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'detail') {
            BizOperateLog.detail(data);
        }
    });
});
