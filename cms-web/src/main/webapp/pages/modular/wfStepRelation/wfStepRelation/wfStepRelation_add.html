@layout("/common/_container.html",{js:["/pages/modular/wfStepRelation/wfStepRelation/js/wfStepNodeAdd.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">添加步骤关联</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="wfStepRelationForm" lay-filter="wfStepRelationForm" class="layui-form model-form" style="max-width: 700px;margin: 40px auto;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">流程类型</label>
                        <div class="layui-input-block">
                            <select name="nodeTyped" id="nodeTyped" lay-verify="required">
                                <option value="">节点类型</option>
                                <option value="1">起点</option>
                                <option value="2">过程</option>
                                <option value="3">结束</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">节点类型</label>
                        <div class="layui-input-block">
                            <input type="hidden" id="typeName" name="typeName" lay-filter="typeName">
                            <select id="typeId" name="typeId" lay-filter="typeId" lay-search="">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">当前步骤</label>
                        <div class="layui-input-inline">
                            <input type="hidden" id="currentStepName" name="currentStepName" lay-filter="currentStepName">
                            <select id="currentStepId" name="currentStepId" lay-filter="currentStepId" lay-search="">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">下一步骤</label>
                        <div class="layui-input-inline">
                            <input type="hidden" id="nextStepName" name="nextStepName" lay-filter="nextStepName">
                            <select id="nextStepId" name="nextStepId" lay-filter="nextStepId" lay-search="">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@}
