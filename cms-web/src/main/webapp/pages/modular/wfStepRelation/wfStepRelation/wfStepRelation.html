@layout("/common/_container.html",{js:["/pages/modular/wfStepRelation/wfStepRelation/js/wfStepRelation.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">步骤关联管理</span>
</div>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-sm12 layui-col-md12 layui-col-lg12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-form toolbar">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <input type="hidden" id="typeName" name="typeName" lay-filter="questTypeName">
                                <select id="typeId" name="typeId" lay-filter="typeId" lay-search="">
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button id="btnSearch" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>搜索</button>
                                 @if(shiro.hasPermission("/wfStepRelation/wfStepRelation_add")){
                                <button id="btnAdd" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i>添加</button>
                                 @}
                                <button id="btnBack" class="layui-btn icon-btn layui-btn-warm"><i class="layui-icon">&#x1006;</i>关闭</button>
                            </div>
                        </div>
                    </div>
                    <table class="layui-table" id="wfStepRelationTable" lay-filter="wfStepRelationTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
</script>
@}

