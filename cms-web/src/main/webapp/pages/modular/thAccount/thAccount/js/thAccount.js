layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var ThAccount = {
        tableId: "thAccountTable"
    };

    /**
     * 初始化表格的列
     */
    ThAccount.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {field: 'accountId', minWidth: 100, hide: false, title: '账户ID'},
            {field: 'homepageUrl', minWidth: 100, hide: false, title: '主页链接'},
            {field: 'nickname', minWidth: 100, hide: false, title: '主播昵称'},
            {field: 'uid', minWidth: 100, hide: false, title: '外站UID'},
            {field: 'lastPubTime', minWidth: 100, hide: false, title: '作品末次更新时间'},
            {field: 'outPlatform', minWidth: 100, hide: false, title: '外站平台'},
            {field: 'publishPlatform', minWidth: 100, hide: false, title: '发布平台'},
            {field: 'fansNum', minWidth: 100, hide: false, title: '外站粉丝数'},
            {field: 'worksNum', minWidth: 100, hide: false, title: '外站作品数'},
            {field: 'firstCategory', minWidth: 100, hide: false, title: '外站一级分类'},
            {field: 'secondCategory', minWidth: 100, hide: false, title: '外站二级分类'},
            {field: 'thirdCategory', minWidth: 100, hide: false, title: '外站三级分类'},
            {field: 'avgLikes', minWidth: 100, hide: false, title: '篇均点赞'},
            {field: 'lastTenAvgLikes', minWidth: 100, hide: false, title: '近10篇篇均点赞'},
            {field: 'accountLikes', minWidth: 100, hide: false, title: '账号总赞数'},
            {field: 'account', minWidth: 100, hide: false, title: '账号'},
            {field: 'approveInfo', minWidth: 100, hide: false, title: '认证信息'},
            {field: 'personalProfile', minWidth: 100, hide: false, title: '个人简介'},
            {field: 'province', minWidth: 100, hide: false, title: '省会'},
            {field: 'city', minWidth: 100, hide: false, title: '城市'},
            {field: 'ipArea', minWidth: 100, hide: false, title: 'IP归属地'},
            {field: 'age', minWidth: 100, hide: false, title: '年龄'},
            {field: 'email', minWidth: 100, hide: false, title: '邮箱'},
            {field: 'isShop', minWidth: 100, hide: false, title: '是否开通橱窗：0为否，1为是，默认为0'},
            {field: 'relatedShop', minWidth: 100, hide: false, title: '关联店铺'},
            {field: 'follows', minWidth: 100, hide: false, title: '关注数'},
            {field: 'postsLast30d', minWidth: 100, hide: false, title: '最近30天发文数'},
            {field: 'postsLast60d', minWidth: 100, hide: false, title: '最近60天发文数'},
            {field: 'postsLast90d', minWidth: 100, hide: false, title: '最近90天发文数'},
            {field: 'accountType', minWidth: 100, hide: false, title: '账号类型'},
            {field: 'isDeleted', minWidth: 100, hide: false, title: '是否删除(1:未删除 2：已删除)'},
            {field: 'platform', minWidth: 100, hide: false, title: '外站平台中文'},
            {field: 'insertTime', minWidth: 100, hide: false, title: '账号入库时间'},
            {field: 'monitorType', minWidth: 100, hide: false, title: '监控方式: 1-监控新增, 2-监控历史及新增'},
            {field: 'page', minWidth: 100, hide: false, title: '分页'},
            {field: 'pageNumber', minWidth: 100, hide: false, title: '分页数量'},
            {field: 'nextPage', minWidth: 100, hide: false, title: ''},
            {field: 'isFreezed', minWidth: 100, hide: false, title: '是否获取完历史数据： 1=历史账号未跑完，2=历史账号已跑完'},
            {field: 'mobile', minWidth: 100, hide: false, title: '手机号'},
            {field: 'wxNumber', minWidth: 100, hide: false, title: '微信号'},
            {field: 'avatar', minWidth: 100, hide: false, title: '头像'},
            {field: 'mcn', minWidth: 100, hide: false, title: 'MCN'},
            {field: 'extendPlatform', minWidth: 100, hide: false, title: '可拓展平台'},
            {field: 'releationId', minWidth: 100, hide: false, title: '关联ID'},
            {field: 'ownerProject', minWidth: 100, hide: false, title: '所属项目：1-视频号，2-百家号，3-拼多多；多个平台用逗号隔开'},
            {field: 'isApprove', minWidth: 100, hide: false, title: '1为黄V，2为蓝V'},
            {field: 'leatestMonth', minWidth: 100, hide: false, title: '1为无，2为有'},
            {field: 'contact', minWidth: 100, hide: false, title: '联系方式（快手）'},
            {field: 'comment', minWidth: 100, hide: false, title: '备注'},
            {field: 'errCount', minWidth: 100, hide: false, title: '错误统计次数'},
            {field: 'parseState', minWidth: 100, hide: false, title: '账号解析状态：1-未解析，2-解析成功，3-解析失败'},
            {field: 'lastParseTime', minWidth: 100, hide: false, title: '上一次解析时间'},
            {field: 'parseErrMsg', minWidth: 100, hide: false, title: '解析异常信息'},
            {field: 'parseErrNums', minWidth: 100, hide: false, title: '解析失败次数'},
            {field: 'realWorkNums', minWidth: 100, hide: false, title: '实际作品数'},
            {align: 'center', toolbar: '#tableBar', fixed: 'right', minWidth: 170, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + ThAccount.tableId,
        url: Feng.ctxPath + '/thAccount/list',
        page: true,
        height: "full-150",
        limit: 20,
        cols: ThAccount.initColumn()
    });
    /**
     * 点击查询按钮
     */
    ThAccount.search = function () {
        var queryData = {};
        queryData['condition'] = $("#condition").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(ThAccount.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    ThAccount.openAddDlg = function () {
        window.location.href = Feng.ctxPath + '/thAccount/thAccount_add';
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    ThAccount.openEditDlg = function (data) {
        window.location.href = Feng.ctxPath + '/thAccount/thAccount_update?thAccountId=' + data.accountId;
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    ThAccount.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/thAccount/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(ThAccount.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("thAccountId", data.accountId);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 达人账户详情
     */
    ThAccount.detail = function (data) {
        window.location.href = Feng.ctxPath + '/thAccount/thAccount_detail?thAccountId=' + data.accountId;
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        ThAccount.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        ThAccount.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + ThAccount.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            ThAccount.openEditDlg(data);
        } else if (layEvent === 'delete') {
            ThAccount.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            ThAccount.detail(data);
        }
    });
});


