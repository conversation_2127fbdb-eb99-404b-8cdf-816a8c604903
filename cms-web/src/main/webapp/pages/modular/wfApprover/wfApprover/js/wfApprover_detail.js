/**
 * 详情对话框
 */
var WfApproverInfoDlg = {
    data: {
       id:"",
       stepId:"",
       stepName:"",
       assignType:"",
       assignValue:"",
       createTime:"",
       updateTime:"",
    }
};
layui.use(['form','ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/wfApprover/detail?wfApproverId=" + Feng.getUrlParam("wfApproverId"));
    var result = ajax.start();
     form.val('wfApproverForm', result.data);
    //图片赋值
    $("#id").attr("disabled","disabled");
    $("#stepId").attr("disabled","disabled");
    $("#stepName").attr("disabled","disabled");
    $("#assignType").attr("disabled","disabled");
    $("#assignValue").attr("disabled","disabled");
    $("#createTime").attr("disabled","disabled");
    $("#updateTime").attr("disabled","disabled");
    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/wfApprover";
    });
});
