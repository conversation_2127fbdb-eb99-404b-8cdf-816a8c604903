/**
 * 详情对话框
 */
var WfApproverInfoDlg = {
    data: {
         id:"",
         stepId:"",
         stepName:"",
         assignType:"",
         assignValue:"",
         createTime:"",
         updateTime:"",
    }
};
layui.use('laydate', function(){
    var laydate = layui.laydate;
        laydate.render({
                elem: '#createTime'
         });
        laydate.render({
                elem: '#updateTime'
         });
});


layui.use(['form', 'ax','upload','table'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var WfApprover = {
            tableId: "wfApproverTable"
     };
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/wfApprover/add", function (data) {
            Feng.success("添加成功！");
            setTimeout(function () {
                            top.layer.closeAll();
                        }, 1000);
        }, function (data) {
            Feng.error("添加失败！" + data.responseJSON.message)
           setTimeout(function () {
                           top.layer.closeAll();
                       }, 1000);
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });

    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/wfApprover";
    });

});
