/**
 * 详情对话框
 */
var SettleProjectsInfoDlg = {
    data: {
        id: "",
        serialNumber: "",
        projectName: "",
        projectCode: "",
        projectTypeId: "",
        projectTypeName: "",
        commissionRate: "",
        revenueType: "",
        flowId: "",
        platform: "",
        quantTarget: "",
        settlementCycle: "",
        startDate: "",
        projectDuration: "",
        agencyRevenue: "",
        businessOwner: "",
        createdBy: "",
        settleStatus: "",
        settleStatusName: "",
        operations: "",
        projectDesc: "",
        projectZhixingFa: "",
        createTime: "",
        updateTime: "",
    }
};

layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#createTime'
    });
    laydate.render({
        elem: '#updateTime'
    });
    laydate.render({
        elem: '#projectStarttime'
    });
    laydate.render({
        elem: '#projectEndtime'
    });
    laydate.render({
        elem: '#startDate'
    });
});

function assignNs(assignIds, assignNames) {
    var $ = layui.jquery;
    $("#businessOwner").val(assignIds);
    $("#businessOwnerName").val(assignNames);
}

layui.use(['form', 'ax', 'treeSelect', 'upload', 'table','laydate'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var admin = layui.admin;
   var laydate = layui.laydate;

    $('#businessOwnerName').on('click', function () {
        
        top.layui.admin.open({
            type: 2,
            area: ['1200px', '800px'], //宽高
            title: '选择业务负责人',
            content: Feng.ctxPath + '/settleProjects/addUser',
            end: function () {
                layer.closeAll();
            }
        });
    });


    var SettleProjects = {
        tableId: "settleProjectsTable"
    };
         SettleProjects.getHiddenFormStr=function(d,index){
                return `
                 <input type="hidden" name="fieldRequest[${index}].id" value="${d.id}"/>
                 <input type="hidden" name="fieldRequest[${index}].configId" value="${d.configId}"/>
                 <input type="hidden" name="fieldRequest[${index}].fieldName" value="${d.fieldName}"/>
                 <input type="hidden" name="fieldRequest[${index}].fieldRemark" value="${d.fieldRemark}"/>
                 <input type="hidden" name="fieldRequest[${index}].fieldType" value="${d.fieldType}"/>
                 <input type="hidden" name="fieldRequest[${index}].orderWith" value="${d.orderWith}"/>
                `
            }
            SettleProjects.getFormStr=function(d,index){
            var fieldType = d.fieldType;
            var options = d.options;
                var value = d.fieldValue ;
                if(fieldType==1){
                  return `
                    <input  type="text" value="${value}" name="fieldRequest[${index}].fieldValue" placeholder="文本框" class="layui-input">
                    `;
                }else if(fieldType==2){
                 return `
                   <textarea   name="fieldRequest[${index}].fieldValue" placeholder="多行文本框" class="layui-textarea">${value}</textarea>

                 `;
                }else if(fieldType==3){

                var optionsStr = [{'optionValue':''},...options].map(e=>`<option value='${e.optionValue}' selected=${value==e.optionValue}>${e.optionValue}</option>`).join('')
                 return `
                   <select  name="fieldRequest[${index}].fieldValue" >
                       ${optionsStr}
                     </select>
                 `;
                }else if(fieldType==4){
                 return `
                  <input  type="text" value="${value}" name="fieldRequest[${index}].fieldValue"  lay-verify="date" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input project-config-date">
                 `;
                }
                return '';
            }
            //初始化配置字段
          SettleProjects.initProjectConfigFiledId= function (project){
                        var data = project.fieldRequest;
                          if(!!data){
                          for(var i=0;i<data.length;i++){
                          var d = data[i];
                               var str = `
                                <div class="layui-form-item layui-form-text">
                                      <div class="layui-form-item">
                                         <div class="layui-block">
                                           <label class="layui-form-label">${d.fieldName}</label>
                                            <div class="layui-input-block">`;
                                var formStr =   SettleProjects.getFormStr(d,i);
                                var hideenFormStr =  SettleProjects.getHiddenFormStr(d,i);
                                var endStr = `</div></div></div></div>`;
                            $('#projectConfigFiledId').append(str+formStr+hideenFormStr+endStr);
                          }

                        }
            }
    var admin = layui.admin;

    //bizSettleFencheng/list
    var destReqUrl = Feng.ctxPath + "/bizNomalEnums/list?projName=项目类型&limit=800&page=0";
    selector.init(destReqUrl, 'projectTypeId', 'keyValue', 'keyType', 'projectTypeName');

    var destReqUrl3 = Feng.ctxPath + "/bizPlatform/listPage?limit=800&page=0";
    selector.init(destReqUrl3, 'platformId', 'id', 'platName', 'platform');


    var destReqUrl2 = Feng.ctxPath + "/bizNomalEnums/list?projName=达人结算周期&limit=800&page=0";
    selector.init(destReqUrl2, 'settlementCycle', 'keyValue', 'keyType', 'settlementCycleName');


    var destReqUrl4 = Feng.ctxPath + "/bizNomalEnums/list?projName=参与公司主体&limit=800&page=0";
    selector.init(destReqUrl4, 'compNameId', 'keyValue', 'keyType', 'compName');

    var destReqUrl6 = Feng.ctxPath + "/bizNomalEnums/list?projName=收益类型&limit=800&page=0";
    selector.init(destReqUrl6, 'revenueType', 'keyValue', 'keyType', 'revenueTypeName');

    var destReqUrl7 = Feng.ctxPath + "/wfType/flowList";
    selector.init(destReqUrl7, 'flowId', 'id', 'typeName', 'typeName');

    //获取详情信息，填充表单
    var ajax = new $ax(Feng.ctxPath + "/settleProjects/detail?settleProjectsId=" + Feng.getUrlParam("settleProjectsId"));
    var result = ajax.start();



    var treeSelect = layui.treeSelect;
    // 初始化下拉选择器
    treeSelect.render({
        // css选择器，推荐使用id
        elem: '#platformId',
        // 请求地址
        data: Feng.ctxPath + '/bizPlatform/listtree',
        // ajax请求方式：post/get
        type: 'post',
        // 返回数据中主键的属性名称，默认值为id
        key: {
            id: 'id',
        },
        success: function (d) {
            console.log(d);
//                选中节点，根据id筛选
            treeSelect.checkNode('platformId', result.data.platformId);
            form.val('settleProjectsForm', result.data);

            $("#id").val(result.data.id);
            $("#projectDesc").val(result.data.projectDesc);
            $("#projectZhixingFa").val(result.data.projectZhixingFa);

            $("#projectStarttime").val(result.data.projectStarttime);
            $("#projectEndtime").val(result.data.projectEndtime);

            //附件赋值 beijingFile  zhixingFiles
            //beijingFileDiv zhixingFileDiv
            $("#beijingFile").val(result.data.beijingFile);
            $("#zhixingFiles").val(result.data.zhixingFiles);

            setFilesVal(result.data)
            $("#commissionRateId").val(result.data.commissionRateId);
            console.log(result.data.agencyRevenue); // 确认值是否为"1"或"2"（字符串）
            console.log(typeof result.data.agencyRevenue); // 确保类型正确（通常应为string）
            const value = result.data.agencyRevenue.toString(); // 强制转为字符串

            // 调试输出
            console.log("当前值:", value,typeof result.data.agencyRevenue);
            console.log("匹配元素数量:", $('input[name="agencyRevenue"][value="' + value + '"]').length);

            $('input[name="agencyRevenue"][value="' + value + '"]').prop('checked', true);
            form.render('radio');
            SettleProjects.initProjectConfigFiledId(result.data);
           form.render('select'); // 仅渲染 select 元素
           laydate.render({
                        elem: '.project-config-date'
                    });
        },
        // 节点点击回调函数
        click: function (d) {
            console.log(d);
            $("#platform").val(d.current.name);
            Feng.success($("#platform").val())
        }
    });


    //返回按钮
    //图片赋值
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/settleProjects/update", function (data) {
            Feng.success("更新成功！");
            window.location.href = Feng.ctxPath + "/settleProjects";
        }, function (data) {
            Feng.error("更新失败！" + data.responseJSON.message);
            window.location.href = Feng.ctxPath + "/settleProjects";
        });
        ajax.set(data.field);
        ajax.start();
        return false;
    });


    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/settleProjects";
    });

});
