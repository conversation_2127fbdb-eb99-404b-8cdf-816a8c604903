<style>
    .approval-flow {
        display: flex;
        padding: 20px 0;
        overflow-x: auto;
    }

    .flow-node {
        position: relative;
        flex: 1;
        min-width: 160px;
        text-align: center;
        padding: 0 15px;
    }

    /* 连接线 */
    .flow-node:not(:first-child)::before {
        content: '';
        position: absolute;
        left: -50%;
        right: 50%;
        top: 14px;
        height: 2px;
        background: #e8e8e8;
        z-index: 0;
    }

    .node-circle {
        width: 30px;
        height: 30px;
        line-height: 30px;
        border-radius: 50%;
        margin: 0 auto 10px;
        position: relative;
        z-index: 1;
        font-size: 14px;
        color: white;
    }

    .node-title {
        margin-bottom: 6px;
        font-weight: bold;
    }

    /* 状态颜色 */
    .pending .node-circle { background: #c0c4cc; }   /* 未提交 */
    .submitted .node-circle { background: #409eff; } /* 已提交 */
    .rejected .node-circle { background: #f56c6c; }  /* 已拒绝 */
    .completed .node-circle { background: #67c23a; } /* 已完成 */

    /* 当前状态标记 */
    .current .node-circle {
        transform: scale(1.2);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .node-status {
        color: #666;
        font-size: 12px;
    }

    .node-actions {
        margin-top: 10px;
    }

    .action-btn {
        display: inline-block;
        padding: 4px 12px;
        font-size: 12px;
        border-radius: 3px;
        cursor: pointer;
        margin: 0 3px;
    }

    .submit-btn {
        background: #409eff;
        color: white;
        border: none;
    }

    .cancel-btn {
        background: #f56c6c;
        color: white;
        border: none;
    }
</style>
<!-- 审核流程模块 -->
<div class="approval-flow" id="approvalFlow">
    @var showProStats = "";
    @if (wfRequestRsp!=null) {
        @for (wfApproval in wfRequestRsp.wfApprovalRecordList) {
            <div class='flow-node
            @if(wfApproval.id == null){
                current pending
            @}else{
                submitted
            @}
            '>
            <div class="node-circle">${wfApprovalLP.index}</div>
            <div class="node-title">${wfApproval.stepName}</div>
            <div class="node-status">${wfApproval.approvalStatusName}</div>
            </div>
        @}
    @}

</div>

<!-- 在HTML中添加拒绝原因弹窗 -->
<div id="rejectModal" style="display:none;padding:20px;position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;z-index:999;border:1px solid #ccc;box-shadow:0 0 10px rgba(0,0,0,0.1)">
    <h3 style="margin-bottom:15px;">请输入拒绝理由</h3>
    <textarea id="rejectReason" rows="4" cols="40" style="width:300px;margin-bottom:15px;"></textarea>
    <div>
        <button id="confirmReject" style="margin-right:10px;">确认</button>
        <button id="cancelReject">取消</button>
    </div>
</div>
<div id="modalOverlay" style="display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:998;"></div>
<input  type="hidden" id="settleProjectsId" value="${item.id}">

<div class="layui-form-item  text-center"  style="text-align: center; margin-bottom: 80px;padding: 10px 0;">
    @if(Objects.equals(isShowSubmit,1)){
    <button class="layui-btn"   id="submitOrder">提交申请</button>

    @}
    @if(Objects.equals(isShowChehui,1)){
    <button type="button" class="layui-btn layui-bg-purple"  id="chehuiOrder">撤回</button>
    @}
    @if(Objects.equals(isShenpiBohui,1)){
    <button class="layui-btn layui-btn-normal" id="tongguoBut"  data-wfApprovalId="${wfApproval.id!''}" >通过</button>
    <button class="layui-btn layui-btn-danger" id="jujueBut"  data-wfApprovalId="${wfApproval.id!''}">驳回</button>
    @}
    <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
</div>
<script type="text/javascript">
    function loadAudsJs(){
        var $ = layui.jquery;
        var $ax = layui.ax;
        $("#submitOrder").click(function (e) {
            e.preventDefault();  // 阻止默认跳转/预览行为
            var ajax = new $ax(Feng.ctxPath + "/settleProjects/tishen?settleProjectsId=${item.id}", function (data) {
                Feng.success("提审成功!");
                window.location.href = Feng.ctxPath + "/settleProjects";
            }, function (data) {
                Feng.error("提审失败!" + data.responseJSON.message + "!");
                window.location.href = Feng.ctxPath + "/settleProjects";
            });
            ajax.start();
        });


        $("#chehuiOrder").click(function (e) {
            e.preventDefault();  // 阻止默认跳转/预览行为
            var ajax = new $ax(Feng.ctxPath + "/settleProjects/chehui", function (data) {
                Feng.success("撤回成功!");
                window.location.href = Feng.ctxPath + "/settleProjects";
            }, function (data) {
                Feng.error("撤回失败!" + data.responseJSON.message + "!");
                window.location.href = Feng.ctxPath + "/settleProjects";
            });
            ajax.set("settleProjectsId", ${item.id});
            ajax.set("wfApprovalId", ${item.id});
            ajax.start();
        });


        $("#tongguoBut").click(function (e) {
            e.preventDefault();  // 阻止默认跳转/预览行为
            var wfApprovalId = $(this).attr("data-wfApprovalId"); // 或者 .attr("data-settle-id")
            var ajax = new $ax(Feng.ctxPath + "/settleProjects/shhe?wfApprovalId="+wfApprovalId+"&settleProjectsId=${item.id}&tg=1", function (data) {
                Feng.success("审核成功!");
                window.location.href = Feng.ctxPath + "/settleProjects";
            }, function (data) {
                Feng.error("审核失败!" + data.responseJSON.message + "!");
                window.location.href = Feng.ctxPath + "/settleProjects";
            });
            ajax.start();
        });




        $("#jujueBut").click(function (e) {
            e.preventDefault();
            // 保存当前审批ID到全局变量
            window.currentWfApprovalId = $(this).attr("data-wfApprovalId");
            window.currentSettleId = document.getElementById("settleProjectsId").value;

            // 显示弹窗和遮罩层
            $("#rejectModal, #modalOverlay").show();
        });

        // 确认拒绝操作
        $("#confirmReject").click(function() {
            const reason = $("#rejectReason").val().trim();
            if (!reason) {
                Feng.error("拒绝理由不能为空！");
                return;
            }

            // 构造请求URL
            const url = Feng.ctxPath + "/settleProjects/shhe" +
                "?wfApprovalId=" + window.currentWfApprovalId +
                "&settleProjectsId=" + window.currentSettleId +
                "&tg=2" +
                "&jureark=" + encodeURIComponent(reason);

            const ajax = new $ax(url, function (data) {
                Feng.success("审核成功!");
                 window.location.href = Feng.ctxPath + "/settleProjects";
            }, function (data) {
                Feng.error("审核失败!" + data.responseJSON.message + "!");
                  window.location.href = Feng.ctxPath + "/settleProjects";
            });

            ajax.start();
            $("#rejectModal, #modalOverlay").hide();
        });

        // 取消拒绝操作
        $("#cancelReject, #modalOverlay").click(function() {
            $("#rejectModal, #modalOverlay").hide();
            $("#rejectReason").val(""); // 清空输入内容
        });
    }

</script>