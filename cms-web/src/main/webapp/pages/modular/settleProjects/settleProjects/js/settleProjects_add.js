/**
 * 详情对话框
 */
var SettleProjectsInfoDlg = {
    data: {
        id: "",
        serialNumber: "",
        projectName: "",
        projectCode: "",
        projectTypeId: "",
        projectTypeName: "",
        commissionRate: "",
        revenueType: "",
        flowId: "",
        platform: "",
        quantTarget: "",
        settlementCycle: "",
        startDate: "",
        projectDuration: "",
        agencyRevenue: "",
        businessOwner: "",
        createdBy: "",
        settleStatus: "",
        settleStatusName: "",
        operations: "",
        projectDesc: "",
        projectZhixingFa: "",
        createTime: "",
        updateTime: "",
    }
};
layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
        elem: '#createTime'
    });
    laydate.render({
        elem: '#updateTime'
    });
    laydate.render({
        elem: '#projectStarttime'
    });
    laydate.render({
        elem: '#projectEndtime'
    });
    laydate.render({
        elem: '#startDate'
    });

});

function assignNs(assignIds, assignNames){
    var $ = layui.jquery;
    $("#businessOwner").val(assignIds);
    $("#businessOwnerName").val(assignNames);
}

layui.use(['form', 'ax', 'treeSelect','upload', 'table', 'admin','laydate'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var upload = layui.upload;
    var admin = layui.admin;
    var laydate = layui.laydate;

    var SettleProjects = {
        tableId: "settleProjectsTable"
    };
    SettleProjects.getHiddenFormStr=function(d,index){

       var options = d.options;
       if(!!!d.options){
         options = []
       }
        var optionsStr = '';

       for(var i = 0 ;i<options.length;i++){
       var optionValue =options[i]['optionValue'] ;
       var orderWith = options[i]['orderWith'] ;
       optionsStr+=`
       <input type="hidden" name="fieldRequest[${index}].options[${i}].optionValue" value="${optionValue}"/>
       <input type="hidden" name="fieldRequest[${index}].options[${i}].orderWith" value="${orderWith}"/>
       <input type="hidden" name="fieldRequest[${index}].options[${i}].id" value=""/>
       `;
       }
        return `
         <input type="hidden" name="fieldRequest[${index}].id" value=""/>
         <input type="hidden" name="fieldRequest[${index}].configId" value="${d.id}"/>
         <input type="hidden" name="fieldRequest[${index}].fieldName" value="${d.fieldName}"/>
         <input type="hidden" name="fieldRequest[${index}].fieldRemark" value="${d.fieldRemark}"/>
         <input type="hidden" name="fieldRequest[${index}].fieldType" value="${d.fieldType}"/>
         <input type="hidden" name="fieldRequest[${index}].orderWith" value="${d.orderWith}"/>
         ${optionsStr}
        `
    }
    SettleProjects.getFormStr=function(d,index){
    var fieldType = d.fieldType;
    var options = d.options;
        if(fieldType==1){
          return `
            <input type="text" name="fieldRequest[${index}].fieldValue" placeholder="文本框" class="layui-input">
            `;
        }else if(fieldType==2){
         return `
           <textarea name="fieldRequest[${index}].fieldValue" placeholder="多行文本框" class="layui-textarea"></textarea>

         `;
        }else if(fieldType==3){

        var optionsStr = [{'optionValue':''},...options].map(e=>`<option value='${e.optionValue}'>${e.optionValue}</option>`)
         return `
           <select name="fieldRequest[${index}].fieldValue">
               ${optionsStr}
             </select>
         `;
        }else if(fieldType==4){
         return `
          <input type="text" name="fieldRequest[${index}].fieldValue"  lay-verify="date" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input project-config-date">
         `;
        }
        return '';
    }
    //初始化配置字段
  SettleProjects.initProjectConfigFiledId= function (){
    var ajax = new $ax(Feng.ctxPath + "/settleProjectsConfig/queryAllUseConfig", function (data) {
            console.log(data);
            if(!!data){
            for(var i=0;i<data.length;i++){
            var d = data[i];
                 var str = `
                  <div class="layui-form-item layui-form-text">
                        <div class="layui-form-item">
                           <div class="layui-block">
                             <label class="layui-form-label">${d.fieldName}</label>
                              <div class="layui-input-block">`;
                  var formStr =   SettleProjects.getFormStr(d,i);
                  var hideenFormStr =  SettleProjects.getHiddenFormStr(d,i);
                  var endStr = `</div></div></div></div>`;
              $('#projectConfigFiledId').append(str+formStr+hideenFormStr+endStr);
            }
           laydate.render({
                elem: '.project-config-date'
            });
          }
        }, function (data) {
               console.error(data)
        });
        ajax.start();
    }
     SettleProjects.initProjectConfigFiledId();
    $('#businessOwnerName').on('click', function () {
        top.layui.admin.open({
            type: 2,
            area: ['1200px', '800px'], //宽高
            title: '选择业务负责人',
            content: Feng.ctxPath + '/settleProjects/addUser',
            end: function () {
                layer.closeAll();
            }
        });
    });


    var treeSelect = layui.treeSelect;
    // 初始化下拉选择器
    treeSelect.render({
        // css选择器，推荐使用id
        elem: '#platformId',
        // 请求地址
        data: Feng.ctxPath + '/bizPlatform/listtree',
        // ajax请求方式：post/get
        type: 'post',
        // 返回数据中主键的属性名称，默认值为id
        key: {
            id: 'id',
        },
        // 节点点击回调函数
        click: function (d) {
            console.log(d);
            $("#platform").val(d.name);
        }
    });
    //表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/settleProjects/add?projectStatusInt=0", function (data) {
            Feng.success("添加成功！");
            window.location.href = Feng.ctxPath + "/settleProjects";
        }, function (data) {
            Feng.error("添加失败！" + data.responseJSON.message)
            window.location.href = Feng.ctxPath + "/settleProjects";
        });
        ajax.set(data.field);
        ajax.start();

        return false;
    });

    /**
     * 提审
     */
    form.on('submit(btnAudit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/settleProjects/add?projectStatusInt=1", function (data) {
            Feng.success("添加成功！");
            window.location.href = Feng.ctxPath + "/settleProjects";
        }, function (data) {
            Feng.error("添加失败！" + data.responseJSON.message)
            window.location.href = Feng.ctxPath + "/settleProjects";
        });

        ajax.set(data.field);
        ajax.start();

        return false;
    });

    //bizSettleFencheng/list
    var destReqUrl = Feng.ctxPath + "/bizNomalEnums/list?projName=项目类型&limit=800&page=0";
    selector.init(destReqUrl, 'projectTypeId', 'keyValue', 'keyType', 'projectTypeName');

    // var destReqUrl3 = Feng.ctxPath + "/bizPlatform/listPage?limit=800&page=0";
    // selector.init(destReqUrl3, 'platformId', 'id', 'platName', 'platform');


    var destReqUrl2 = Feng.ctxPath + "/bizNomalEnums/list?projName=达人结算周期&limit=800&page=0";
    selector.init(destReqUrl2, 'settlementCycle', 'keyValue', 'keyType', 'settlementCycleName');


    var destReqUrl4 = Feng.ctxPath + "/bizNomalEnums/list?projName=参与公司主体&limit=800&page=0";
    selector.init(destReqUrl4, 'compNameId', 'keyValue', 'keyType', 'compName');


    var destReqUrl6 = Feng.ctxPath + "/bizNomalEnums/list?projName=收益类型&limit=800&page=0";
    selector.init(destReqUrl6, 'revenueType', 'keyValue', 'keyType', 'revenueTypeName');

    var destReqUrl7 = Feng.ctxPath + "/wfType/flowList";
    selector.init(destReqUrl7, 'flowId', 'id', 'typeName', 'null');


    form.on('radio(agencyRevenue)', function(data){
        if(data.value == 1) { // 选中"是"
            $('#commissionToComp').val(100); // jQuery方式
            // 或原生JS方式：
            $('#commissionToDaren').val(0); // jQuery方式
            // 重要：更新LayUI表单渲染
            form.render('input');
        }
    });

    //返回按钮
    $("#backupPage").click(function () {
        window.location.href = Feng.ctxPath + "/settleProjects";
    });

});
