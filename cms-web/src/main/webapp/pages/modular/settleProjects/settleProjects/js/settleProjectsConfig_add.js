layui.use(['table', 'ax','treeSelect'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var form = layui.form;
    var admin = layui.admin;
    var id =   Feng.getUrlParam("id");
    function add_option_html(index){
       var str = `
        <div class="layui-form-item">
        <div class="layui-inline">
               <label class="layui-form-label"></label>
                   <div class="layui-input-inline layui-input-wrap">
                            <input name="options[${index}].optionValue" placeholder="请输入选项" type="text" class="layui-input" />
                   </div>
                   <div class="layui-form-mid" style="padding: 0!important;line-height:2">
                            <i  class="layui-icon layui-icon-delete remove-project-config-option" lay-tips="删除选项"></i>
                   </div>
               </div>
            </div>
            `;
      $("#selectOptionInputId").append(str);
    }
    function hideOrShowOptions(value){
        if(value==3){
           $("#selectOptionInputId").css("display","block");
        }else{
           $("#selectOptionInputId").css("display","none");
        }}
    // 删除动态添加的 input 输入框
    $("body").on('click', ".remove-project-config-option", function () {
        var parentEle = $(this).parent().parent();
        parentEle.remove();
    });
    var options_length = 0;
    hideOrShowOptions('');
      if(!!id){
        //获取用户信息
        var ajax = new $ax(Feng.ctxPath + "/settleProjectsConfig/getSettleProjectsConfigById?id=" + id);
        var result = ajax.start();
        console.log(result);
        var obj =result.data
        var arr = obj['options']
        if(!!arr){
      for(var i = 0;i<arr.length;i++){
            if(i>0){
                    add_option_html(i);
            }
            obj['options['+i+'].optionValue']=arr[i].optionValue
           ++options_length;
            }
        }
        form.val('settleProjectsConfigForm',obj );
        hideOrShowOptions(obj.fieldType);
      }


    form.on('submit(settleProjectsConfigFormBtn)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/settleProjectsConfig/settleProjectsConfigAdd", function (data) {
            Feng.success(!!data.id?"更新成功！":"添加成功");
           //传给上个页面，刷新table用
            admin.putTempData('formOk', true);

           //关掉对话框
           admin.closeThisDialog();
        }, function (data) {
            Feng.error("操作失败！" + data.responseJSON.message);
            window.location.href = Feng.ctxPath + "/settleProjectsConfig";
        });
        ajax.set(data.field);
        ajax.start();
        return false;
    });

  $("#selectOptionInputIdAdd").click(function(e){
    add_option_html(++options_length);
  })
  // select 事件
  form.on('select(fieldTypeId)', function(data){
    var elem = data.elem; // 获得 select 原始 DOM 对象
    var value = data.value; // 获得被选中的值
    var othis = data.othis; // 获得 select 元素被替换后的 jQuery 对象
    hideOrShowOptions(value);
  });
});


