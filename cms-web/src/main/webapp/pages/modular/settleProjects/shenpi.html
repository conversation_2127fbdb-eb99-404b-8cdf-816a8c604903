<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>水平审批流程</title>
  <style>
  .approval-flow {
    display: flex;
    padding: 20px 0;
    overflow-x: auto;
  }

  .flow-node {
    position: relative;
    flex: 1;
    min-width: 160px;
    text-align: center;
    padding: 0 15px;
  }

  /* 连接线 */
  .flow-node:not(:first-child)::before {
    content: '';
    position: absolute;
    left: -50%;
    right: 50%;
    top: 14px;
    height: 2px;
    background: #e8e8e8;
    z-index: 0;
  }

  .node-circle {
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    margin: 0 auto 10px;
    position: relative;
    z-index: 1;
    font-size: 14px;
    color: white;
  }

  .node-title {
    margin-bottom: 6px;
    font-weight: bold;
  }

  /* 状态颜色 */
  .pending .node-circle { background: #c0c4cc; }   /* 未提交 */
  .submitted .node-circle { background: #409eff; } /* 已提交 */
  .rejected .node-circle { background: #f56c6c; }  /* 已拒绝 */
  .completed .node-circle { background: #67c23a; } /* 已完成 */

  /* 当前状态标记 */
  .current .node-circle {
    transform: scale(1.2);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  }

  .node-status {
    color: #666;
    font-size: 12px;
  }

  .node-actions {
    margin-top: 10px;
  }

  .action-btn {
    display: inline-block;
    padding: 4px 12px;
    font-size: 12px;
    border-radius: 3px;
    cursor: pointer;
    margin: 0 3px;
  }

  .submit-btn {
    background: #409eff;
    color: white;
    border: none;
  }

  .cancel-btn {
    background: #f56c6c;
    color: white;
    border: none;
  }
  </style>
</head>
<body>

<div class="approval-flow">
  <!-- 未提交 -->
  <div class="flow-node current pending">
    <div class="node-circle">1</div>
    <div class="node-title">未提交</div>
    <div class="node-status">待发起申请</div>
    <div class="node-actions">
      <button class="action-btn submit-btn" onclick="submit()">提交</button>
    </div>
  </div>

  <!-- 已提交 -->
  <div class="flow-node submitted">
    <div class="node-circle">2</div>
    <div class="node-title">已提交</div>
    <div class="node-status">审批中</div>
    <div class="node-actions">
      <button class="action-btn cancel-btn" onclick="cancel()">撤回</button>
    </div>
  </div>

  <!-- 已拒绝 -->
  <div class="flow-node rejected">
    <div class="node-circle">3</div>
    <div class="node-title">已拒绝</div>
    <div class="node-status">需重新提交</div>
    <div class="node-actions">
      <button class="action-btn submit-btn">修改</button>
    </div>
  </div>

  <!-- 已完成 -->
  <div class="flow-node completed">
    <div class="node-circle">4</div>
    <div class="node-title">已完成</div>
    <div class="node-status">审批通过</div>
    <div class="node-actions">
      <span style="color:#67c23a">✓</span>
    </div>
  </div>
</div>

<script>
function submit() {
  alert('提交成功！');
}

function cancel() {
  if(confirm('确定要撤回申请吗？')) {
    alert('已撤回申请');
  }
}
</script>

</body>
</html>
