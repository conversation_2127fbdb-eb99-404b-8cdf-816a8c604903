@layout("/common/_container.html",{js:["/pages/modular/bizImSensitiveWords/js/bizImSensitiveWords_edit.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">修改bizImSensitiveWords</span>
</div>

<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizImSensitiveWordsForm" lay-filter="bizImSensitiveWordsForm" class="layui-form model-form">
                <input name="" type="hidden"/>
                <input id="id" name="id" placeholder="请输入id"  type="hidden"  required/>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">敏感词</label>
                        <div class="layui-input-inline">
                            <input id="sensitiveWords" name="sensitiveWords" placeholder="请输入sensitiveWords"
                                   type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-form-item  text-center">
                        <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                        <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog"
                                id="backupPage">返回
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@}
