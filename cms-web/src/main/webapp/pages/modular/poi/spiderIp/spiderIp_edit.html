@layout("/common/_container.html",{js:["/assets/modular/poi/spiderIp/spiderIp_edit.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">修改爬虫IP</span>
</div>

<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="spiderIpForm" lay-filter="spiderIpForm" class="layui-form model-form" >
                <input name="spiderIpId" type="hidden"/>
                     <div class="layui-form-item">
                         <div class="layui-inline">
                           <label class="layui-form-label">主键</label>
                           <div class="layui-input-inline">
                             <input id="id" name="id" placeholder="请输入主键" type="text" class="layui-input" lay-verify="required" required/>
                           </div>
                         </div>
                         <div class="layui-inline">
                               <label class="layui-form-label">IP地址</label>
                               <div class="layui-input-inline">
                                  <input id="ip" name="ip" placeholder="请输入IP地址" type="text" class="layui-input" lay-verify="required" required/>
                               </div>
                             </div>
                     </div>                     <div class="layui-form-item">
                         <div class="layui-inline">
                           <label class="layui-form-label">port</label>
                           <div class="layui-input-inline">
                             <input id="port" name="port" placeholder="请输入port" type="text" class="layui-input" lay-verify="required" required/>
                           </div>
                         </div>
                         <div class="layui-inline">
                               <label class="layui-form-label">状态</label>
                               <div class="layui-input-inline">
                                  <input id="ipStatus" name="ipStatus" placeholder="请输入状态" type="text" class="layui-input" lay-verify="required" required/>
                               </div>
                             </div>
                     </div>                     <div class="layui-form-item">
                         <div class="layui-inline">
                           <label class="layui-form-label">使用次数</label>
                           <div class="layui-input-inline">
                             <input id="ipUseNum" name="ipUseNum" placeholder="请输入使用次数" type="text" class="layui-input" lay-verify="required" required/>
                           </div>
                         </div>
                         <div class="layui-inline">
                               <label class="layui-form-label">创建时间</label>
                               <div class="layui-input-inline">
                                  <input id="createTime" name="createTime" placeholder="请输入创建时间" type="text" class="layui-input" lay-verify="required" required/>
                               </div>
                             </div>
                     </div>                     <div class="layui-form-item">
                         <div class="layui-inline">
                           <label class="layui-form-label">更新时间</label>
                           <div class="layui-input-inline">
                             <input id="updateTime" name="updateTime" placeholder="请输入更新时间" type="text" class="layui-input" lay-verify="required" required/>
                           </div>
                         </div>
                <div class="layui-form-item  text-center">
                    <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                    <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                </div>
            </form>
        </div>
    </div>
</div>

@}
