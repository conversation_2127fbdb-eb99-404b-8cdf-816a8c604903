layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    var form = layui.form;
    /**
     * 字典类型表管理
     */
    var BizPoi = {
        tableId: "bizPoiTable"
    };

    /**
     * 初始化表格的列
     */
    BizPoi.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {field: 'title', minWidth: 100, hide: false, title: '主题'},
            {field: 'telNo', minWidth: 100, hide: false, title: '电话'},
            {field: 'destName', minWidth: 100, hide: false, title: '目的地名称'},
            {field: 'preCostTime', minWidth: 100, hide: false, title: '耗时'},
            {field: 'trafficInfo', minWidth: 100, hide: false, title: '交通状况'},
            {field: 'ticketInfo', minWidth: 100, hide: false, title: '票信息'},
            {field: 'openInfo', minWidth: 100, hide: false, title: '开放时间'},
            {field: 'poiDes', minWidth: 100, hide: false, title: '简介'},
            {
                field: 'createTime',
                minWidth: 50,
                hide: false, title: '创建时间'
            },
            {align: 'center', toolbar: '#tableBar', minWidth: 200, title: '操作'}
        ]];
    };

    var ajax = new $ax(Feng.ctxPath + '/bizDest/list?limit=1000&page=0',
        function (data) {
            for (var i = 0; i < data.data.length; i++) {
                $("#destId").append("<option value='" + data.data[i].id + "'>"+data.data[i].destName+"</option>");
            }
            form.render();
        }, function (data) {
            Feng.error("获取城市信息失败!" + data.responseJSON.message + "!");
        });
    ajax.start();
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BizPoi.tableId,
        url: Feng.ctxPath + '/bizPoi/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: BizPoi.initColumn()
    });
    /**
     * 点击查询按钮
     */
    BizPoi.search = function () {
        var queryData = {};
        queryData['poiName'] = $("#poiName").val();
        queryData['destId'] = $("#destId").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(BizPoi.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    BizPoi.openAddDlg = function () {
        window.location.href = Feng.ctxPath + '/bizPoi/bizPoi_add';
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    BizPoi.openEditDlg = function (data) {
        window.location.href = Feng.ctxPath + '/bizPoi/bizPoi_update?bizPoiId=' + data.id;
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    BizPoi.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizPoi/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(BizPoi.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("bizPoiId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * POI详情
     */
    BizPoi.detail = function (data) {
        window.location.href = Feng.ctxPath + '/bizPoi/bizPoi_detail?bizPoiId=' + data.id;
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BizPoi.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        BizPoi.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + BizPoi.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            BizPoi.openEditDlg(data);
        } else if (layEvent === 'delete') {
            BizPoi.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            BizPoi.detail(data);
        }
    });
});


