@layout("/common/_container.html",{js:["/pages/modular/poi/bizDest/js/bizDest_add.js"]}){
<div class="layui-body-header">
    <span class="layui-body-header-title">添加目的地</span>
</div>
<div class="layui-fluid " style="">
    <div class="layui-card">
        <div class="layui-card-body">
            <form id="bizDestForm" lay-filter="bizDestForm" class="layui-form model-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">目的地名称</label>
                        <div class="layui-input-inline">
                            <input id="destName" name="destName" placeholder="请输入目的地名称" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">副标题</label>
                        <div class="layui-input-inline">
                            <input id="subName" name="subName" placeholder="请输入副标题" type="text" class="layui-input" lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea placeholder="请输入描述" class="layui-textarea" id="destDesc" name="destDesc"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">图片</label>
                    <div class="layui-input-inline">
                        <div class="layui-upload">
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" style="width: 200px;height: 200px" id="picImg">
                                <input type="hidden" name="pic" id="picHidden">
                                <p id="demoText"></p>
                            </div>
                            <button type="button" class="layui-btn" id="uploadpic">上传图片</button>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item  text-center">
                    <button class="layui-btn" lay-filter="btnSubmit" lay-submit>保存</button>
                    <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog" id="backupPage">返回</button>
                </div>
            </form>
        </div>
    </div>
</div>

@}
