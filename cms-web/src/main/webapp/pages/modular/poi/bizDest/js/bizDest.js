layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var BizDest = {
        tableId: "bizDestTable"
    };

    /**
     * 初始化表格的列
     */
    BizDest.initColumn = function () {
        return [[
            {type: 'checkbox'},
            {field: 'id', minWidth: 20, hide: false, title: '主键'},
            {field: 'thirdId', minWidth: 100, hide: false, title: '第三方ID'},
            {field: 'destName', minWidth: 100, hide: false, title: '目的地名称'},
            {field: 'subName', minWidth: 100, hide: false, title: '副标题'},
            {field: 'destDesc', minWidth: 300, hide: false, title: '描述'},
            {
                field: 'createTime',
                minWidth: 200,
                hide: false, title: '创建时间'
            },
            {align: 'center', toolbar: '#tableBar', minWidth: 100, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BizDest.tableId,
        url: Feng.ctxPath + '/bizDest/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: BizDest.initColumn()
    });
    /**
     * 点击查询按钮
     */
    BizDest.search = function () {
        var queryData = {};
        queryData['destName'] = $("#destName").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(BizDest.tableId, {where: queryData});
    };
    /**
     * 弹出添加对话框
     */
    BizDest.openAddDlg = function () {
        window.location.href = Feng.ctxPath + '/bizDest/bizDest_add';
    };

    /**
     * 点击编辑
     *
     * @param data 点击按钮时候的行数据
     */
    BizDest.openEditDlg = function (data) {
        window.location.href = Feng.ctxPath + '/bizDest/bizDest_update?bizDestId=' + data.id;
    };

    /**
     * 点击删除
     *
     * @param data 点击按钮时候的行数据
     */
    BizDest.onDeleteItem = function (data) {
        var operation = function () {
            var ajax = new $ax(Feng.ctxPath + "/bizDest/delete", function (data) {
                Feng.success("删除成功!");
                table.reload(BizDest.tableId);
            }, function (data) {
                Feng.error("删除失败!" + data.responseJSON.message + "!");
            });
            ajax.set("bizDestId", data.id);
            ajax.start();
        };
        Feng.confirm("是否删除?", operation);
    };
    /**
     * 目的地详情
     */
    BizDest.detail = function (data) {
        window.location.href = Feng.ctxPath + '/bizDest/bizDest_detail?bizDestId=' + data.id;
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BizDest.search();
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        BizDest.openAddDlg();
    });

    // 工具条点击事件
    table.on('tool(' + BizDest.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'edit') {
            BizDest.openEditDlg(data);
        } else if (layEvent === 'delete') {
            BizDest.onDeleteItem(data);
        } else if (layEvent === 'detail') {
            BizDest.detail(data);
        }
    });
});


