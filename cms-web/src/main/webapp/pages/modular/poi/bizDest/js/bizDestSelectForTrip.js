var BizDest = {
    data: {
        id:"",
        thirdId:"",
        destName:"",
        subName:"",
        destDesc:"",
        pic:"",
        createTime:"",
        updateTime:"",
    }
};
layui.use(['table', 'ax'], function () {
    var $ = layui.$;
    var table = layui.table;
    var $ax = layui.ax;
    var admin = layui.admin;
    /**
     * 字典类型表管理
     */
    var BizDest = {
        tableId: "bizDestTable"
    };

    /**
     * 初始化表格的列
     */
    BizDest.initColumn = function () {
        return [[
               {field: 'id',minWidth: 100,  hide: false,title: '主键'},
               {field: 'thirdId',minWidth: 100,  hide: false,title: '第三方ID'},
               {field: 'destName',minWidth: 100,  hide: false,title: '目的地名称'},
               {field: 'subName',minWidth: 100,  hide: false,title: '副标题'},
               {field: 'destDesc',minWidth: 100,  hide: false,title: '描述'},
            {align: 'center', toolbar: '#tableBar',minWidth: 80, title: '操作'}
        ]];
    };
    // 渲染表格
    var tableResult = table.render({
        elem: '#' + BizDest.tableId,
        url: Feng.ctxPath + '/bizDest/list',
        page: true,
        height: "full-98",
        limit: 20,
        cols: BizDest.initColumn()
    });
    /**
     * 点击查询按钮
     */
    BizDest.search = function () {
        var queryData = {};
        queryData['destName'] = $("#destName").val();
        queryData['systemFlag'] = $("#systemFlag").val();
        queryData['status'] = $("#status").val();
        table.reload(BizDest.tableId, {where: queryData});
    };
    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        BizDest.search();
    });

    // 工具条点击事件
    table.on('tool(' + BizDest.tableId + ')', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'selectCity') {
        	var desNum = getUrlParam('desNum');
        	if(window.parent.BizTripInfoDlg){
        		window.parent.BizTripInfoDlg.setCityInfo(data,desNum);
        	}else if(window.parent.EditBizTripInfoDlg){
        		window.parent.EditBizTripInfoDlg.setCityInfo(data,desNum);
        	}
        }
    });
});


