layui.use(['form', 'ax', 'layer'], function () {

    var $ = layui.$;
    var $ax = layui.ax;
    var layer = layui.layer;
    var table = layui.table;
    var form = layui.form;

    var ajax = new $ax(Feng.ctxPath + "/bizInflucer/detail?bizInflucerId=" + Feng.getUrlParam("bizInflucerId"));
    var result = ajax.start();

    if (result.code === 200) {

        const data = result.data;
        $("#influcerId").val(data.id);
        $("#icon").attr("src", data.icon);

        renderTalentInfo(data);

        //待审批列表
        renderPlatformList(data.authPlatforms)

        //全局操作按钮
        renderGlobalActions(data)
    }

    // 渲染达人信息
    function renderTalentInfo(data) {

        const userDetails = document.getElementById('user-details');

        userDetails.innerHTML = `
                <div class="detail-item">
                    <span class="detail-label">达人ID:</span>
                    <span>${data.influcerId}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">手机号:</span>
                    <span>${data.loginTel}</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">昵称:</span>
                    <span>${data.nikeName || '-'}</span>
                </div>
            `;
    }

    // 渲染平台列表
    function renderPlatformList(platforms) {

        var $scrollContainer = $('.scroll-container');

        $scrollContainer.empty(); // 清空原有内容

        if (platforms && platforms.length > 0) {
            platforms.forEach(function (platform) {
                //有效数据-需要认证，后续扩展，只有待审核状态的数据
                if (null != platform.platformId && '' !== platform.platformId) {
                    $scrollContainer.append(renderPlatformCard(platform));
                }
            });
        } else {
            $scrollContainer.html('<div class="layui-empty">暂无平台数据</div>');
        }
    }

    // 渲染全局按钮
    function renderGlobalActions(influcer) {

        var $globalActions = $('.global-actions');

        $globalActions.empty(); // 清空原有内容

        //有效数据-需要认证，后续扩展，只有待审核状态的数据
        if (null != influcer.id && '' !== influcer.id) {
            $globalActions.append(
                `
                ${influcer.enableCancelApprove ?
                    `<button class="layui-btn layui-btn-normal " id="batch-cancel">一键作废</button>`
                    :
                    `<button class="layui-btn layui-btn-normal" id="batch-approve">一键通过</button>
                    <button class="layui-btn layui-btn-danger" id="batch-reject">一键驳回</button>`
                }
                `
            );
        }
    }

    /**
     * 生成平台卡片HTML
     * @param {Object} platformData 平台数据
     * @returns {string} 平台卡片HTML
     */
    function renderPlatformCard(platformData) {

        let color = '';
        switch (platformData.platformStatus) {
            case '未认证':
                color = 'blue';
                break;
            case '待审批':
                color = 'orange';
                break;
            case '已认证':
                color = 'green';
                break;
            case '未通过':
                color = 'red';
                break;
            default:
                color = 'black';
        }
        return `
				<div class="layui-card platform-card" data-platform="${platformData.platformName}">
					<div class="platform-header">
                        ${platformData.platformName} 
                        <span style="
                                color: ${color};
                                display: inline-block;
                                padding: 0px 2px;
                                border: 1px solid;
                                border-color: ${color};
                                border-radius: 10px;
                                margin-right: 5px;
                                float: right;
                            ">${platformData.platformStatus || '-'}
                        </span>
                    </div>
					
					<div class="platform-body">
						<div class="two-columns">
							<div class="left-column">
								<div class="platform-title">认证信息</div>
								<input type="hidden" name="${platformData.platformName}_id" value="${platformData.id}">
								<div class="layui-form-item">
									<label class="layui-form-label">授权平台</label>
									<div class="layui-input-block">
									    <img src="${platformData.platformImg}" style="height: 20px; margin-left: 10px;">
										<span>${platformData.platformName}</span>
									</div>
								</div>
								
								${platformData.affirmEnable ?
            `<div class="layui-form-item">
                                        <label class="layui-form-label">主页链接地址</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="${platformData.platformName}" class="layui-input" value="${platformData.homeUrl || '-'}" readonly>
                                        </div>
                                    </div>` :
            `<div class="layui-form-item">
                                        <label class="layui-form-label">账号ID</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="${platformData.platformName}" class="layui-input" value="${platformData.account || '-'}" readonly>
                                        </div>
                                    </div>`
        }
								
								<div class="layui-form-item">
									<label class="layui-form-label">创作者端截图</label>
									<div class="layui-input-block">
										<div class="media-preview">
											<img src="${platformData.homeImg}" style="width: 200px;height: 100px;">
										</div>
									</div>
								</div>
								
								${platformData.affirmEnable ?
                                `<div class="layui-form-item">
                                        <label class="layui-form-label">私信验证截图</label>
                                        <div class="layui-input-block">
                                            <div class="media-preview">
                                                <img src="${platformData.affirmImg}" style="width: 200px;height: 100px;">
                                            </div>
                                        </div>
                                    </div>` : ""
                                }
								
								${platformData.videoEnable ?
                                `<div class="layui-form-item">
									<label class="layui-form-label">认证视频</label>
									<div class="layui-input-block">
										<div class="media-preview">
											<video controls>
												<source src="${platformData.videoUrls}" type="video/mp4" style="width: 200px;height: 100px;">
											</video>
										</div>
									</div>
								</div>` : ''
        }
							</div>
							<div class="right-column">
								<div>
									<div class="platform-title">外站信息</div>
									<div class="layui-form-item">
										<label class="layui-form-label">外站ID</label>
										<div class="layui-input-block">
											<input type="text" name="${platformData.outstationId}" class="layui-input" value="${platformData.externalId || ''}">
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">外站昵称</label>
										<div class="layui-input-block">
											<input type="text" name="${platformData.outstationName}" class="layui-input" value="${platformData.externalNick || ''}">
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">外站粉丝数</label>
										<div class="layui-input-block">
											<input type="text" name="${platformData.outstationFans}" class="layui-input" value="${platformData.fansCount || ''}">
										</div>
									</div>
								</div>
								<div style="margin-top: 20px;">
									<div class="platform-title">审核信息</div>
									<div class="layui-form-item">
										<label class="layui-form-label">提交时间</label>
										<div class="layui-input-block">
											<input type="text" name="${platformData.platformName}" class="layui-input" readonly value="${platformData.createTime || '-'}">
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">审批时间</label>
										<div class="layui-input-block">
											<input type="text" name="${platformData.platformName}" class="layui-input" readonly value="${platformData.authTime || '-'}">
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">审核人</label>
										<div class="layui-input-block">
											<input type="text" name="${platformData.platformName}" class="layui-input" readonly value="${platformData.approveUserName || '-'}">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="action-buttons">
							<div class="reject-reason" id="${platformData.platformName}_rejectReason">
								<textarea name="${platformData.platformName}_rejectReason" placeholder="请输入驳回原因" class="layui-textarea"></textarea>
							</div>
							
							${platformData.platformStatus === '已认证' ? 
                            `
                                <div class="single-actions">
                                    <button class = "layui-btn layui-btn-normal approve-cancel" data-platform="${platformData.platformName}">作废</button>
                                </div>
                            `
                            : platformData.platformStatus === '未通过'?
                            `
                                <div class="single-actions">
                                    <button class = "layui-btn layui-btn-normal approve-cancel" data-platform="${platformData.platformName}">作废</button>
                                </div>
                                <div class="rejection-reason-box">
                                    <label class="layui-form-label">驳回原因：</label>
                                    <textarea style="width:90%; height:50px; color:red;" readonly>${platformData.reason}</textarea>
                                </div>
                            `
                            : platformData.platformStatus !== '已认证' && platformData.platformStatus !== '未通过'?
                            `
                                <div class="single-actions">
                                    <button class="layui-btn layui-btn-normal approve-btn" data-platform="${platformData.platformName}">通过</button>
                                    <button class="layui-btn layui-btn-danger reject-btn" data-platform="${platformData.platformName}">驳回</button>
                                </div>
                            `
                            :''
                            }
						</div>
					</div>
				</div>
				`;
    }

    // 单个平台通过
    $('.approve-btn').on('click', function () {

        var platform = $(this).data('platform');
        var platformName = $(this).closest('.platform-card').find('.platform-header').text();

        layer.confirm('此操作将认证' + platform + '的数据审核通过，请确认，是否继续？', {
            title: '提示',
            btn: ['确定', '取消']
        }, function () {

            // 更新审批信息
            let val = $('input[name="' + platform + '_id"]').val();
            //layer.msg("提交的数据id"+val);

            // 构建最终提交数据
            var formData = {
                ids: [],
                agree: true,
                region: ''
            };
            formData.ids.push(val);
            approve(formData)

            layer.msg(platform + "平台审核已通过", {icon: 1});
            console.log('通过平台:', platform);
        });
    });

    // 单个平台驳回
    $('.reject-btn').on('click', function () {

        var platform = $(this).data('platform');
        var platformName = $(this).closest('.platform-card').find('.platform-header').text();

        layer.confirm('此操作将认证' + platform + '的数据进行驳回，请确认，是否继续？', {
            title: '提示',
            btn: ['确定', '取消']
        }, function () {

            // 创建自定义弹层内容
            var content = [
                '<div style="position: relative; padding: 15px; box-sizing: border-box; max-height: 80vh; overflow: hidden; display: flex; flex-direction: column;">',
                '  <div style="flex: 1; overflow-y: auto; margin-bottom: 10px;">',
                '    <textarea id="rejectReasonText" style="width: 100%; height: 150px; resize: none; padding: 8px; box-sizing: border-box; border: 1px solid #e6e6e6;"',
                '      placeholder="请输入驳回原因(最多200字)" maxlength="200"></textarea>',
                '  </div>',
                '  <div style="display: flex; justify-content: space-between; align-items: center; ">',
                '    <div id="charCount" style="color: #999; font-size: 12px;margin-left: 90%;">0/200</div>',
                '  </div>',
                '</div>'
            ].join('');

            layer.open({
                type: 1,
                title: '请输入驳回原因',
                area: ['500px', 'auto'],
                content: content,
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    var reason = $('#rejectReasonText').val().trim();
                    if (!reason) {
                        layer.msg('请填写驳回原因');
                        return false;
                    }

                    // 构建最终提交数据
                    var formData = {
                        ids: [],
                        agree: false,
                        region: reason
                    };
                    let val = $('input[name="' + platform + '_id"]').val();
                    formData.ids.push(val);

                    approve(formData);

                    layer.msg(platform + '平台审核已驳回', {icon: 2});
                    console.log(platform + '平台认证已驳回，原因:', reason);
                    layer.close(index);
                },
                success: function () {
                    // 绑定输入事件
                    $('#rejectReasonText').on('input', function () {
                        var length = $(this).val().length;
                        $('#charCount').text(length + '/200');

                        // 可选：超过限制时改变颜色
                        if (length > 200) {
                            $('#charCount').css('color', 'red');
                        } else {
                            $('#charCount').css('color', '#999');
                        }
                    });
                }
            });
        });
    });

    // 一键通过
    $('#batch-approve').on('click', function () {

        layer.confirm('确定要通过所有平台的审核吗？', {
            title: '一键通过确认',
            btn: ['确定', '取消']
        }, function () {

            // 构建最终提交数据
            var formData = {
                ids: [],
                agree: true,
                region: ''
            };

            $('.platform-card').each(function () {
                var platform = $(this).data('platform');
                let val = $('input[name="' + platform + '_id"]').val();
                formData.ids.push(val);
            });

            approve(formData)

            layer.msg('所有平台审核已通过', {icon: 1});
            console.log('一键通过所有平台');
        });
    });

    // 一键驳回
    $('#batch-reject').on('click', function () {
        layer.confirm('确定要驳回所有平台的审核吗？', {
            title: '一键驳回确认',
            btn: ['确定', '取消']
        }, function () {


            // 创建自定义弹层内容
            var content = [
                '<div style="position: relative; padding: 15px; box-sizing: border-box; max-height: 80vh; overflow: hidden; display: flex; flex-direction: column;">',
                '  <div style="flex: 1; overflow-y: auto; margin-bottom: 10px;">',
                '    <textarea id="rejectReasonText" style="width: 100%; height: 150px; resize: none; padding: 8px; box-sizing: border-box; border: 1px solid #e6e6e6;"',
                '      placeholder="请输入驳回原因(最多200字)" maxlength="200"></textarea>',
                '  </div>',
                '  <div style="display: flex; justify-content: space-between; align-items: center; ">',
                '    <div id="charCount" style="color: #999; font-size: 12px;margin-left: 90%;">0/200</div>',
                '  </div>',
                '</div>'
            ].join('');

            layer.open({
                type: 1,
                title: '请输入驳回原因',
                area: ['500px', 'auto'],
                content: content,
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    var reason = $('#rejectReasonText').val().trim();
                    if (!reason) {
                        layer.msg('请填写驳回原因');
                        return false;
                    }

                    // 构建最终提交数据
                    var formData = {
                        ids: [],
                        agree: false,
                        region: reason
                    };

                    $('.platform-card').each(function () {
                        var platform = $(this).data('platform');
                        let val = $('input[name="' + platform + '_id"]').val();
                        formData.ids.push(val);
                    });

                    approve(formData);

                    layer.msg('所有平台审核已驳回', {icon: 2});
                    console.log('一键驳回所有平台，原因:', reason);
                    layer.close(index);
                },
                success: function () {
                    // 绑定输入事件
                    $('#rejectReasonText').on('input', function () {
                        var length = $(this).val().length;
                        $('#charCount').text(length + '/200');

                        // 可选：超过限制时改变颜色
                        if (length > 200) {
                            $('#charCount').css('color', 'red');
                        } else {
                            $('#charCount').css('color', '#999');
                        }
                    });
                }
            });
        })
    });

    //审批接口
    function approve(data) {

        //这里替换为实际的提交逻辑
        $.ajax({
            url: Feng.ctxPath + '/bizInflucerPlatform/influcerPlatformApprove',
            type: 'POST',
            data: JSON.stringify(data),
            contentType: 'application/json',
            success: function (res) {
                if (res.code === 200) {
                    setTimeout(function () {
                        top.layer.closeAll();
                    }, 1000);
                } else {
                    setTimeout(function () {
                        top.layer.closeAll();
                    }, 1500);
                }
            },
            error: function () {

                Feng.success("审核失败！");
                setTimeout(function () {
                    top.layer.closeAll();
                    //window.location.href = Feng.ctxPath + "/bizInflucer";
                }, 1000);
            }
        });
    }

    // 单个平台作废
    $('.approve-cancel').on('click', function () {

        var platform = $(this).data('platform');

        layer.confirm('此操作将' + platform + '的审核数据进行作废，请确认，是否继续？', {
            title: '提示',
            btn: ['确定', '取消']
        }, function () {

            // 达人id
            let influcerId = $('#influcerId').val();
            let id = $('input[name="' + platform + '_id"]').val();

            var ajax = new $ax(Feng.ctxPath + "/bizInflucerPlatform/cancel?bizInflucerId=" + influcerId + "&id=" +id, function (data) {
                //Feng.success("作废成功!");
                layer.msg(platform+"平台作废审核已成功", {icon: 1});
                setTimeout(function () {
                    top.layer.closeAll();
                }, 1000);
            }, function (data) {
                layer.msg(platform+"平台作废审核失败", {icon: 1});
                //Feng.error("作废失败!" + data.responseJSON.message + "!");
                setTimeout(function () {
                    top.layer.closeAll();
                }, 1000);
            });
            ajax.start();

            Feng.confirm("是否作废审核?", operation);
        });
    });

    $('#batch-cancel').on('click', function () {

        layer.confirm('此操作将所有数据审核进行作废，请确认，是否继续？', {
            title: '提示',
            btn: ['确定', '取消']
        }, function () {

            // 更新审批信息
            let influcerId = $('#influcerId').val();

            var ajax = new $ax(Feng.ctxPath + "/bizInflucerPlatform/cancel?bizInflucerId=" + influcerId, function (data) {
                //Feng.success("作废成功!");
                layer.msg("作废审核已成功", {icon: 1});
                setTimeout(function () {
                    top.layer.closeAll();
                }, 1000);
            }, function (data) {
                layer.msg("作废审核失败", {icon: 2});
                //Feng.error("作废失败!" + data.responseJSON.message + "!");
                setTimeout(function () {
                    top.layer.closeAll();
                }, 1000);
            });
            ajax.start();

            Feng.confirm("是否作废审核?", operation);
        });
    });




    function padZero(num) {
        return num < 10 ? '0' + num : num;
    }
});