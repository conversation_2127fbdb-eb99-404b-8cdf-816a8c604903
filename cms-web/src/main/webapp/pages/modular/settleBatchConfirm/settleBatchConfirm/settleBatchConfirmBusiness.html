@layout("/common/_container.html",{js:["/pages/modular/settleProjects/settleProjects/js/fileupd.js","/pages/modular/settleBatchConfirm/settleBatchConfirm/js/settleBatchConfirmBusiness.js"]}){

<div class="layui-container" style="width: 100%">
    <!-- 基础信息模块 -->
    <form id="settleBatchConfirmBusinessForm" lay-filter="settleBatchConfirmBusinessForm" class="layui-form model-form">
        <!-- 确认结算信息 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-24">
                <div class="layui-card">
                    <div class="layui-card-header">确认结算</div>
                    <div class="layui-card-body">
                        <div class="layui-row">
                            <div class="layui-col-12">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">输入二次结算金额</label>
                                    <div class="layui-inline">
                                        <input id="businessAmount" name="businessAmount" placeholder="请输入" required class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">上传平台结算图片</label>
                                    <div class="layui-input-inline">
                                        <div class="layui-upload">
                                            <div class="layui-upload-list">
                                                <img class="layui-upload-img" style="width: 200px;height: 200px" id="businessFileImg">
                                                <input type="hidden" name="businessFile" id="businessFile">
                                                <p id="demoText"></p>
                                            </div>
                                            <button type="button" class="layui-btn" id="uploadimgPic">上传图片</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-12">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">备注</label>
                                        <div class="layui-input-block">
                                            <textarea class="layui-textarea" name="businessRemark" id="businessRemark"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <input type="hidden" id="recordId" name="recordId" value="${wfApprovalRecord.id}"/>
            <input type="hidden" id="batchId" name="batchId" value="${item.id}"/>
        </div>

        <div class="layui-col-24">
            <div class="layui-card">
                @ include("/modular/settleOrder/settleOrder/settlepjs.html"){}
                <div class="layui-col-24" style="text-align: center; /*! padding: 20px 0; */margin-bottom: 80px;padding: 10px 0;">
                    @if(showFlag){
                    <button class="layui-btn layui-btn-lg" id="submitBut" lay-filter="btnSubmit" lay-submit style="width: 200px;">提交</button>
                    @}
                    <button class="layui-btn layui-btn-primary layui-btn-lg" id="backupPage" ew-event="closeDialog" style="width: 200px; margin-left: 30px;">返回</button>
                </div>
            </div>
        </div>
    </form>
</div>


@}