<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover">
    <title>导游登录</title>
    <link rel="stylesheet" href="/pages/h5/weui-2.1.2/style/weui.css"/>
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
    <script src="https://res.wx.qq.com/open/libs/weuijs/1.2.1/weui.min.js"></script>

    <script src="/assets/common/libs/jquery/jquery-3.2.1.min.js"></script>
</head>
<body>
<div class="page">
    <div class="weui-form">
        <div class="weui-form__text-area">
            <h2 class="weui-form__title">司机登录</h2>
        </div>
        <form action="/pc/login" method="post">
            <div class="weui-form__control-area">
                <div class="weui-cells__group weui-cells__group_form">
                    <div class="weui-cells weui-cells_form">
                        <div class="weui-cell">
                            <div class="weui-cell__hd"><label class="weui-label">工号</label></div>
                            <div class="weui-cell__bd">
                                <input class="weui-input" type="text" name="username" id="username" placeholder="请输入工号" />
                            </div>
                            <div class="weui-cell__ft">
                                <button class="weui-btn_reset weui-btn_icon">
                                    <i id="showIOSDialog1" class="weui-icon-info-circle"></i>
                                </button>
                            </div>
                        </div>
                        <div class="weui-cell weui-cell_vcode">
                            <div class="weui-cell__hd"><label class="weui-label">密码</label></div>
                            <div class="weui-cell__bd">
                                <input class="weui-input" type="password" id="password" name="password" placeholder="输入验证码" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="weui-form__opr-area">
                <button class="weui-btn weui-btn_primary" type="submit" id="showTooltips">确定</button>
            </div>
        </form>
    </div>
    @if(StringUtils.isNotBlank(msg)){
    <div class="weui-flex" style="padding-left: 50px;color: red">
        ${msg}
    </div>
    @}
    <div id="js_toast" style="display: none;">
        <div class="weui-mask_transparent"></div>
        <div class="weui-toast">
            <i class="weui-icon-success-no-circle weui-icon_toast"></i>
            <p class="weui-toast__content">已完成</p>
        </div>
    </div>
    <div id="dialogs">
        <!--BEGIN dialog1-->
        <div class="js_dialog" id="iosDialog1" style="display: none;">
            <div class="weui-mask"></div>
            <div id="js_half_screen_dialog" class="weui-half-screen-dialog">
                <div class="weui-half-screen-dialog__hd">
                    <div class="weui-half-screen-dialog__hd__side">
                        <button id="dialogClose" class="weui-icon-btn weui-icon-btn_close">关闭</button>
                    </div>
                </div>
                <div class="weui-half-screen-dialog__bd">
                    若无工号，请联系管理员获取
                </div>
            </div>
        </div>
        <!--END dialog1-->
    </div>
</div>

<script type="text/javascript">
    $(function () {
        // var username = $('#username').val();
        // var password = $('#password').val();
        //
        // $('#showTooltips').on('click', function () {
        //     var reqUrl = "/pc/login?username=" + username + "&password=" + password;
        //     $.get(reqUrl, function (data, status) {
        //         alert("Data: " + data + "\nStatus: " + status);
        //     });
        //
        // });
        var $iosDialog1 = $('#iosDialog1');
        $('#dialogs').on('click', '.weui-mask', function () {
            $halfScreenDialog.removeClass('weui-half-screen-dialog_show');
            $(this).parents('.js_dialog').fadeOut(200);
        });
        $('#dialogClose').on('click', function () {
            $halfScreenDialog.removeClass('weui-half-screen-dialog_show');
            $(this).parents('.js_dialog').fadeOut(200);
        });
        $('#showIOSDialog1').on('click', function () {
            $iosDialog1.fadeIn(200);
            $halfScreenDialog.addClass('weui-half-screen-dialog_show');
        });
    });
</script>
</body>
</html>
