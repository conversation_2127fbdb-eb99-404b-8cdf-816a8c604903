@layout("/h5/common/_pccontainer.html",{js:["/pages/h5/order/js/orderlist.js"]}){
@ include("/h5/order/orderhead.html"){}
<br>
<div class="page__bd" >
    <div class="weui-pay">
    @if(isNotEmpty(pcOrderRspList)){
    @for(pcOrderRsp in pcOrderRspList){
    <ul class="weui-pay-u">
        <li>
            <span class="title">订单号</span>
            <span class="content">${pcOrderRsp.bizOrderGuide.orderNo}</span>
        </li>
        <li>
            <span class="title">订单类型</span>
            <span class="content">${pcOrderRsp.bizOrderCar.orderTypeName}</span>
        </li>
        <li>
            <span class="title">出发时间</span>
            <span class="content">${pcOrderRsp.bizOrderGuide.serviceTime,dateFormat="yyyy-MM-dd HH:mm:ss"}</span>
        </li>
        <li>
            <span class="title">结束时间</span>
            <span class="content">${pcOrderRsp.bizOrderGuide.serviceEndTime,dateFormat="yyyy-MM-dd HH:mm:ss"}</span>
        </li>
    </ul>
    <br>
    @}
    @}
    </div>
    @if(tool.isEmpty(pcOrderRspList)){
    <div class="weui-msgbox" style="padding-top: 250px" id="s3">
        <p>
            <i class="weui-icon-info-circle"></i>暂无订单
        </p>
    </div>
    @}
</div>

@}
