(function(a){a.fn.transitionEnd=function(f){var c=["webkitTransitionEnd","transitionend","oTransitionEnd","MSTransitionEnd","msTransitionEnd"],b,d=this;function e(g){if(g.target!==this){return}f.call(this,g);for(b=0;b<c.length;b++){d.off(c[b],e)}}if(f){for(b=0;b<c.length;b++){d.on(c[b],e)}}return this};a.support=(function(){var b={touch:!!(("ontouchstart" in window)||window.DocumentTouch&&document instanceof window.DocumentTouch)};return b})();a.touchEvents={start:a.support.touch?"touchstart":"mousedown",move:a.support.touch?"touchmove":"mousemove",end:a.support.touch?"touchend":"mouseup"};a.getTouchPosition=function(b){b=b.originalEvent||b;if(b.type==="touchstart"||b.type==="touchmove"||b.type==="touchend"){return{x:b.targetTouches[0].pageX,y:b.targetTouches[0].pageY}}else{return{x:b.pageX,y:b.pageY}}};a.fn.scrollHeight=function(){return this[0].scrollHeight};a.fn.transform=function(b){for(var d=0;d<this.length;d++){var c=this[d].style;c.webkitTransform=c.MsTransform=c.msTransform=c.MozTransform=c.OTransform=c.transform=b}return this};a.fn.transition=function(d){if(typeof d!=="string"){d=d+"ms"}for(var c=0;c<this.length;c++){var b=this[c].style;b.webkitTransitionDuration=b.MsTransitionDuration=b.msTransitionDuration=b.MozTransitionDuration=b.OTransitionDuration=b.transitionDuration=d}return this};a.getTranslate=function(f,e){var b,d,g,c;if(typeof e==="undefined"){e="x"}g=window.getComputedStyle(f,null);if(window.WebKitCSSMatrix){c=new WebKitCSSMatrix(g.webkitTransform==="none"?"":g.webkitTransform)}else{c=g.MozTransform||g.OTransform||g.MsTransform||g.msTransform||g.transform||g.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,");b=c.toString().split(",")}if(e==="x"){if(window.WebKitCSSMatrix){d=c.m41}else{if(b.length===16){d=parseFloat(b[12])}else{d=parseFloat(b[4])}}}if(e==="y"){if(window.WebKitCSSMatrix){d=c.m42}else{if(b.length===16){d=parseFloat(b[13])}else{d=parseFloat(b[5])}}}return d||0};a.requestAnimationFrame=function(b){if(window.requestAnimationFrame){return window.requestAnimationFrame(b)}else{if(window.webkitRequestAnimationFrame){return window.webkitRequestAnimationFrame(b)}else{if(window.mozRequestAnimationFrame){return window.mozRequestAnimationFrame(b)}else{return window.setTimeout(b,1000/60)}}}};a.cancelAnimationFrame=function(b){if(window.cancelAnimationFrame){return window.cancelAnimationFrame(b)}else{if(window.webkitCancelAnimationFrame){return window.webkitCancelAnimationFrame(b)}else{if(window.mozCancelAnimationFrame){return window.mozCancelAnimationFrame(b)}else{return window.clearTimeout(b)}}}};a.fn.join=function(b){return this.toArray().join(b)}})($);+function($){$.Template7=$.t7=(function(){function isArray(arr){return Object.prototype.toString.apply(arr)==="[object Array]"}function isObject(obj){return obj instanceof Object}function isFunction(func){return typeof func==="function"}var cache={};function helperToSlices(string){var helperParts=string.replace(/[{}#}]/g,"").split(" ");var slices=[];var shiftIndex,i,j;for(i=0;i<helperParts.length;i++){var part=helperParts[i];if(i===0){slices.push(part)}else{if(part.indexOf('"')===0){if(part.match(/"/g).length===2){slices.push(part)}else{shiftIndex=0;for(j=i+1;j<helperParts.length;j++){part+=" "+helperParts[j];if(helperParts[j].indexOf('"')>=0){shiftIndex=j;slices.push(part);break}}if(shiftIndex){i=shiftIndex}}}else{if(part.indexOf("=")>0){var hashParts=part.split("=");var hashName=hashParts[0];var hashContent=hashParts[1];if(hashContent.match(/"/g).length!==2){shiftIndex=0;for(j=i+1;j<helperParts.length;j++){hashContent+=" "+helperParts[j];if(helperParts[j].indexOf('"')>=0){shiftIndex=j;break}}if(shiftIndex){i=shiftIndex}}var hash=[hashName,hashContent.replace(/"/g,"")];slices.push(hash)}else{slices.push(part)}}}}return slices}function stringToBlocks(string){var blocks=[],i,j,k;if(!string){return[]}var _blocks=string.split(/({{[^{^}]*}})/);for(i=0;i<_blocks.length;i++){var block=_blocks[i];if(block===""){continue}if(block.indexOf("{{")<0){blocks.push({type:"plain",content:block})}else{if(block.indexOf("{/")>=0){continue}if(block.indexOf("{#")<0&&block.indexOf(" ")<0&&block.indexOf("else")<0){blocks.push({type:"variable",contextName:block.replace(/[{}]/g,"")});continue}var helperSlices=helperToSlices(block);var helperName=helperSlices[0];var helperContext=[];var helperHash={};for(j=1;j<helperSlices.length;j++){var slice=helperSlices[j];if(isArray(slice)){helperHash[slice[0]]=slice[1]==="false"?false:slice[1]}else{helperContext.push(slice)}}if(block.indexOf("{#")>=0){var helperStartIndex=i;var helperContent="";var elseContent="";var toSkip=0;var shiftIndex;var foundClosed=false,foundElse=false,foundClosedElse=false,depth=0;for(j=i+1;j<_blocks.length;j++){if(_blocks[j].indexOf("{{#")>=0){depth++}if(_blocks[j].indexOf("{{/")>=0){depth--}if(_blocks[j].indexOf("{{#"+helperName)>=0){helperContent+=_blocks[j];if(foundElse){elseContent+=_blocks[j]}toSkip++}else{if(_blocks[j].indexOf("{{/"+helperName)>=0){if(toSkip>0){toSkip--;helperContent+=_blocks[j];if(foundElse){elseContent+=_blocks[j]}}else{shiftIndex=j;foundClosed=true;break}}else{if(_blocks[j].indexOf("else")>=0&&depth===0){foundElse=true}else{if(!foundElse){helperContent+=_blocks[j]}if(foundElse){elseContent+=_blocks[j]}}}}}if(foundClosed){if(shiftIndex){i=shiftIndex}blocks.push({type:"helper",helperName:helperName,contextName:helperContext,content:helperContent,inverseContent:elseContent,hash:helperHash})}}else{if(block.indexOf(" ")>0){blocks.push({type:"helper",helperName:helperName,contextName:helperContext,hash:helperHash})}}}}return blocks}var Template7=function(template){var t=this;t.template=template;function getCompileFn(block,depth){if(block.content){return compile(block.content,depth)}else{return function(){return""}}}function getCompileInverse(block,depth){if(block.inverseContent){return compile(block.inverseContent,depth)}else{return function(){return""}}}function getCompileVar(name,ctx){var variable,parts,levelsUp=0,initialCtx=ctx;if(name.indexOf("../")===0){levelsUp=name.split("../").length-1;var newDepth=ctx.split("_")[1]-levelsUp;ctx="ctx_"+(newDepth>=1?newDepth:1);parts=name.split("../")[levelsUp].split(".")}else{if(name.indexOf("@global")===0){ctx="$.Template7.global";parts=name.split("@global.")[1].split(".")}else{if(name.indexOf("@root")===0){ctx="ctx_1";parts=name.split("@root.")[1].split(".")}else{parts=name.split(".")}}}variable=ctx;for(var i=0;i<parts.length;i++){var part=parts[i];if(part.indexOf("@")===0){if(i>0){variable+="[(data && data."+part.replace("@","")+")]"}else{variable="(data && data."+name.replace("@","")+")"}}else{if(isFinite(part)){variable+="["+part+"]"}else{if(part.indexOf("this")===0){variable=part.replace("this",ctx)}else{variable+="."+part}}}}return variable}function getCompiledArguments(contextArray,ctx){var arr=[];for(var i=0;i<contextArray.length;i++){if(contextArray[i].indexOf('"')===0){arr.push(contextArray[i])}else{arr.push(getCompileVar(contextArray[i],ctx))}}return arr.join(", ")}function compile(template,depth){depth=depth||1;template=template||t.template;if(typeof template!=="string"){throw new Error("Template7: Template must be a string")}var blocks=stringToBlocks(template);if(blocks.length===0){return function(){return""}}var ctx="ctx_"+depth;var resultString="(function ("+ctx+", data) {\n";if(depth===1){resultString+="function isArray(arr){return Object.prototype.toString.apply(arr) === '[object Array]';}\n";resultString+="function isFunction(func){return (typeof func === 'function');}\n";resultString+='function c(val, ctx) {if (typeof val !== "undefined") {if (isFunction(val)) {return val.call(ctx);} else return val;} else return "";}\n'}resultString+="var r = '';\n";var i,j,context;for(i=0;i<blocks.length;i++){var block=blocks[i];if(block.type==="plain"){resultString+="r +='"+(block.content).replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/'/g,"\\'")+"';";continue}var variable,compiledArguments;if(block.type==="variable"){variable=getCompileVar(block.contextName,ctx);resultString+="r += c("+variable+", "+ctx+");"}if(block.type==="helper"){if(block.helperName in t.helpers){compiledArguments=getCompiledArguments(block.contextName,ctx);resultString+="r += ($.Template7.helpers."+block.helperName+").call("+ctx+", "+(compiledArguments&&(compiledArguments+", "))+"{hash:"+JSON.stringify(block.hash)+", data: data || {}, fn: "+getCompileFn(block,depth+1)+", inverse: "+getCompileInverse(block,depth+1)+", root: ctx_1});"}else{if(block.contextName.length>0){throw new Error('Template7: Missing helper: "'+block.helperName+'"')}else{variable=getCompileVar(block.helperName,ctx);resultString+="if ("+variable+") {";resultString+="if (isArray("+variable+")) {";resultString+="r += ($.Template7.helpers.each).call("+ctx+", "+variable+", {hash:"+JSON.stringify(block.hash)+", data: data || {}, fn: "+getCompileFn(block,depth+1)+", inverse: "+getCompileInverse(block,depth+1)+", root: ctx_1});";resultString+="}else {";resultString+="r += ($.Template7.helpers.with).call("+ctx+", "+variable+", {hash:"+JSON.stringify(block.hash)+", data: data || {}, fn: "+getCompileFn(block,depth+1)+", inverse: "+getCompileInverse(block,depth+1)+", root: ctx_1});";resultString+="}}"}}}}resultString+="\nreturn r;})";return eval.call(window,resultString)}t.compile=function(template){if(!t.compiled){t.compiled=compile(template)}return t.compiled}};Template7.prototype={options:{},helpers:{"if":function(context,options){if(isFunction(context)){context=context.call(this)}if(context){return options.fn(this,options.data)}else{return options.inverse(this,options.data)}},unless:function(context,options){if(isFunction(context)){context=context.call(this)}if(!context){return options.fn(this,options.data)}else{return options.inverse(this,options.data)}},each:function(context,options){var ret="",i=0;if(isFunction(context)){context=context.call(this)}if(isArray(context)){if(options.hash.reverse){context=context.reverse()}for(i=0;i<context.length;i++){ret+=options.fn(context[i],{first:i===0,last:i===context.length-1,index:i})}if(options.hash.reverse){context=context.reverse()}}else{for(var key in context){i++;ret+=options.fn(context[key],{key:key})}}if(i>0){return ret}else{return options.inverse(this)}},"with":function(context,options){if(isFunction(context)){context=context.call(this)}return options.fn(context)},join:function(context,options){if(isFunction(context)){context=context.call(this)}return context.join(options.hash.delimiter||options.hash.delimeter)},js:function(expression,options){var func;if(expression.indexOf("return")>=0){func="(function(){"+expression+"})"}else{func="(function(){return ("+expression+")})"}return eval.call(this,func).call(this)},js_compare:function(expression,options){var func;if(expression.indexOf("return")>=0){func="(function(){"+expression+"})"}else{func="(function(){return ("+expression+")})"}var condition=eval.call(this,func).call(this);if(condition){return options.fn(this,options.data)}else{return options.inverse(this,options.data)}}}};var t7=function(template,data){if(arguments.length===2){var instance=new Template7(template);var rendered=instance.compile()(data);instance=null;return(rendered)}else{return new Template7(template)}};t7.registerHelper=function(name,fn){Template7.prototype.helpers[name]=fn};t7.unregisterHelper=function(name){Template7.prototype.helpers[name]=undefined;delete Template7.prototype.helpers[name]};t7.compile=function(template,options){var instance=new Template7(template,options);return instance.compile()};t7.options=Template7.prototype.options;t7.helpers=Template7.prototype.helpers;return t7})()}($);+function(a){var b;a.modal=function(h,c){h=a.extend({},b,h);var f=h.buttons;var g=f.map(function(k,j){return'<a href="javascript:;" class="weui-dialog__btn '+(k.className||"")+'">'+k.text+"</a>"}).join("");var d='<div class="weui-dialog"><div class="weui-dialog__hd"><strong class="weui-dialog__title">'+h.title+"</strong></div>"+(h.text?'<div class="weui-dialog__bd">'+h.text+"</div>":"")+'<div class="weui-dialog__ft">'+g+"</div></div>";var e=a.openModal(d,c);e.find(".weui-dialog__btn").each(function(j,l){var k=a(l);k.click(function(){if(h.autoClose){a.closeModal()}if(f[j].onClick){f[j].onClick.call(e)}})});return e};a.openModal=function(e,d){var c=a("<div class='weui-mask'></div>").appendTo(document.body);c.show();var f=a(e).appendTo(document.body);if(d){f.transitionEnd(function(){d.call(f)})}f.show();c.addClass("weui-mask--visible");f.addClass("weui-dialog--visible");return f};a.closeModal=function(){a(".weui-mask--visible").removeClass("weui-mask--visible").transitionEnd(function(){a(this).remove()});a(".weui-dialog--visible").removeClass("weui-dialog--visible").transitionEnd(function(){a(this).remove()})};a.alert=function(f,e,c){var d;if(typeof f==="object"){d=f}else{if(typeof e==="function"){c=arguments[1];e=undefined}d={text:f,title:e,onOK:c}}return a.modal({text:d.text,title:d.title,buttons:[{text:b.buttonOK,className:"primary",onClick:d.onOK}]})};a.confirm=function(g,f,c,e){var d;if(typeof g==="object"){d=g}else{if(typeof f==="function"){e=arguments[2];c=arguments[1];f=undefined}d={text:g,title:f,onOK:c,onCancel:e}}return a.modal({text:d.text,title:d.title,buttons:[{text:b.buttonCancel,className:"default",onClick:d.onCancel},{text:b.buttonOK,className:"primary",onClick:d.onOK}]})};a.prompt=function(i,h,d,g,c){var e;if(typeof i==="object"){e=i}else{if(typeof h==="function"){c=arguments[3];g=arguments[2];d=arguments[1];h=undefined}e={text:i,title:h,input:c,onOK:d,onCancel:g,empty:false}}var f=a.modal({text:'<p class="weui-prompt-text">'+(e.text||"")+'</p><input type="text" class="weui-input weui-prompt-input" id="weui-prompt-input" value="'+(e.input||"")+'" />',title:e.title,autoClose:false,buttons:[{text:b.buttonCancel,className:"default",onClick:function(){a.closeModal();e.onCancel&&e.onCancel.call(f)}},{text:b.buttonOK,className:"primary",onClick:function(){var j=a("#weui-prompt-input").val();if(!e.empty&&(j===""||j===null)){f.find(".weui-prompt-input").focus()[0].select();return false}a.closeModal();e.onOK&&e.onOK.call(f,j)}}]},function(){this.find(".weui-prompt-input").focus()[0].select()});return f};a.login=function(i,h,c,g,j,e){var d;if(typeof i==="object"){d=i}else{if(typeof h==="function"){e=arguments[4];j=arguments[3];g=arguments[2];c=arguments[1];h=undefined}d={text:i,title:h,username:j,password:e,onOK:c,onCancel:g}}var f=a.modal({text:'<p class="weui-prompt-text">'+(d.text||"")+'</p><input type="text" class="weui-input weui-prompt-input" id="weui-prompt-username" value="'+(d.username||"")+'" placeholder="输入用户名" /><input type="password" class="weui-input weui-prompt-input" id="weui-prompt-password" value="'+(d.password||"")+'" placeholder="输入密码" />',title:d.title,autoClose:false,buttons:[{text:b.buttonCancel,className:"default",onClick:function(){a.closeModal();d.onCancel&&d.onCancel.call(f)}},{text:b.buttonOK,className:"primary",onClick:function(){var l=a("#weui-prompt-username").val();var k=a("#weui-prompt-password").val();if(!d.empty&&(l===""||l===null)){f.find("#weui-prompt-username").focus()[0].select();return false}if(!d.empty&&(k===""||k===null)){f.find("#weui-prompt-password").focus()[0].select();return false}a.closeModal();d.onOK&&d.onOK.call(f,l,k)}}]},function(){this.find("#weui-prompt-username").focus()[0].select()});return f};b=a.modal.prototype.defaults={title:"提示",text:undefined,buttonOK:"确定",buttonCancel:"取消",buttons:[{text:"确定",className:"primary"}],autoClose:true}}($);+function(c){var d;var a=function(i,j){j=j||"";var f=c("<div class='weui-mask_transparent'></div>").appendTo(document.body);var g='<div class="weui-toast '+j+'">'+i+"</div>";var h=c(g).appendTo(document.body);h.addClass("weui-toast--visible");h.show()};var b=function(f){c(".weui-mask_transparent").remove();c(".weui-toast--visible").removeClass("weui-toast--visible").transitionEnd(function(){var g=c(this);g.remove();f&&f(g)})};c.toast=function(j,g,k){if(typeof g==="function"){k=g}var f,h="weui-icon-success-no-circle";var i=e.duration;if(g=="cancel"){f="weui-toast_cancel";h="weui-icon-cancel"}else{if(g=="forbidden"){f="weui-toast--forbidden";h="weui-icon-warn"}else{if(g=="text"){f="weui-toast--text"}else{if(typeof g===typeof 1){i=g}}}}a('<i class="'+h+' weui-icon_toast"></i><p class="weui-toast_content">'+(j||"已经完成")+"</p>",f);setTimeout(function(){b(k)},i)};c.showLoading=function(g){var f='<div class="weui_loading">';f+='<i class="weui-loading weui-icon_toast"></i>';f+="</div>";f+='<p class="weui-toast_content">'+(g||"数据加载中")+"</p>";a(f,"weui_loading_toast")};c.hideLoading=function(){b()};var e=c.toast.prototype.defaults={duration:1000}}($);+function(c){var d;var a=function(k){var e=c("<div class='weui-mask weui-actions_mask'></div>").appendTo(document.body);var j=k.actions||[];var h=j.map(function(m,l){return'<div class="weui-actionsheet__cell '+(m.className||"")+'">'+m.text+"</div>"}).join("");var i="";if(k.title){i='<div class="weui-actionsheet__title"><p class="weui-actionsheet__title-text">'+k.title+"</p></div>"}var f='<div class="weui-actionsheet " id="weui-actionsheet">'+i+'<div class="weui-actionsheet__menu">'+h+'</div><div class="weui-actionsheet__action"><div class="weui-actionsheet__cell weui-actionsheet_cancel">取消</div></div></div>';var g=c(f).appendTo(document.body);g.find(".weui-actionsheet__menu .weui-actionsheet__cell, .weui-actionsheet__action .weui-actionsheet__cell").each(function(l,m){c(m).click(function(){c.closeActions();k.onClose&&k.onClose();if(j[l]&&j[l].onClick){j[l].onClick()}})});e.show();g.show();e.addClass("weui-mask--visible");g.addClass("weui-actionsheet_toggle")};var b=function(){c(".weui-mask").removeClass("weui-mask--visible").transitionEnd(function(){c(this).remove()});c(".weui-actionsheet").removeClass("weui-actionsheet_toggle").transitionEnd(function(){c(this).remove()})};c.actions=function(e){e=c.extend({},d,e);a(e)};c.closeActions=function(){b()};c(document).on("click",".weui-actions_mask",function(){c.closeActions()});var d=c.actions.prototype.defaults={title:undefined,onClose:undefined}}($);+function(b){var a=function(e,d){if(typeof d===typeof function(){}){d={onRefresh:d}}if(typeof d===typeof"a"){d=undefined}this.opt=b.extend(a.defaults,d||{});this.container=b(e);this.attachEvents()};a.defaults={distance:50,onRefresh:undefined,onPull:undefined};a.prototype.touchStart=function(f){if(this.container.hasClass("refreshing")){return}var d=b.getTouchPosition(f);this.start=d;this.diffX=this.diffY=0};a.prototype.touchMove=function(f){if(this.container.hasClass("refreshing")){return}if(!this.start){return false}if(this.container.scrollTop()>0){return}var d=b.getTouchPosition(f);this.diffX=d.x-this.start.x;this.diffY=d.y-this.start.y;if(Math.abs(this.diffX)>Math.abs(this.diffY)){return true}if(this.diffY<0){return}this.container.addClass("touching");f.preventDefault();f.stopPropagation();this.diffY=Math.pow(this.diffY,0.75);this.container.css("transform","translate3d(0, "+this.diffY+"px, 0)");this.triggerPull(this.diffY)};a.prototype.touchEnd=function(){this.start=false;if(this.diffY<=0||this.container.hasClass("refreshing")){return}this.container.removeClass("touching");this.container.removeClass("pull-down pull-up");this.container.css("transform","");if(Math.abs(this.diffY)<=this.opt.distance){}else{this.triggerPullToRefresh()}};a.prototype.triggerPullToRefresh=function(){this.triggerPull(this.opt.distance);this.container.removeClass("pull-up").addClass("refreshing");if(this.opt.onRefresh){this.opt.onRefresh.call(this)}this.container.trigger("pull-to-refresh")};a.prototype.triggerPull=function(d){if(d<this.opt.distance){this.container.removeClass("pull-up").addClass("pull-down")}else{this.container.removeClass("pull-down").addClass("pull-up")}if(this.opt.onPull){this.opt.onPull.call(this,Math.floor(d/this.opt.distance*100))}this.container.trigger("pull")};a.prototype.pullToRefreshDone=function(){this.container.removeClass("refreshing")};a.prototype.attachEvents=function(){var d=this.container;d.addClass("weui-pull-to-refresh");d.on(b.touchEvents.start,b.proxy(this.touchStart,this));d.on(b.touchEvents.move,b.proxy(this.touchMove,this));d.on(b.touchEvents.end,b.proxy(this.touchEnd,this))};var c=function(d){b(d).removeClass("refreshing")};b.fn.pullToRefresh=function(d){return this.each(function(){var e=b(this);var f=e.data("ptr");if(!f){e.data("ptr",f=new a(this,d))}if(typeof d===typeof"a"){f[d].call(f)}})};b.fn.pullToRefreshDone=function(){return this.each(function(){c(this)})}}($);+function(d){var c=function(e){var f=e[0].tagName.toUpperCase();var g;if(f==="BODY"||f==="HTML"){g=e.scrollTop()||d(window).scrollTop()}else{g=e.scrollTop()}var h=e.scrollHeight()-(d(window).height()+g);console.log(h);return h};var b=function(e,f){this.container=d(e);this.container.data("infinite",this);this.distance=f||50;this.attachEvents()};b.prototype.scroll=function(){var e=this.container;this._check()};b.prototype.attachEvents=function(g){var f=this.container;var e=(f[0].tagName.toUpperCase()==="BODY"?d(document):f);e[g?"off":"on"]("scroll",d.proxy(this.scroll,this))};b.prototype.detachEvents=function(e){this.attachEvents(true)};b.prototype._check=function(){var e=c(this.container);if(Math.abs(e)<=this.distance){this.container.trigger("infinite")}};var a=function(e){attachEvents(e)};d.fn.infinite=function(e){return this.each(function(){new b(this,e)})};d.fn.destroyInfinite=function(){return this.each(function(){var e=d(this).data("infinite");if(e&&e.detachEvents){e.detachEvents()}})}}($);+function(c){var b="weui-bar__item--on";var a=function(d){var g=c(d);if(g.hasClass(b)){return}var e=g.attr("href");if(!/^#/.test(e)){return}g.parent().find("."+b).removeClass(b);g.addClass(b);var f=g.parents(".weui-tab").find(".weui-tab__bd");f.find(".weui-tab__bd-item--active").removeClass("weui-tab__bd-item--active");c(e).addClass("weui-tab__bd-item--active")};c.showTab=a;c(document).on("click",".weui-navbar__item, .weui-tabbar__item",function(g){var f=c(g.currentTarget);var d=f.attr("href");if(f.hasClass(b)){return}if(!/^#/.test(d)){return}g.preventDefault();a(f)})}($);+function(a){a(document).on("click touchstart",".weui-search-bar__label",function(b){a(b.target).parents(".weui-search-bar").addClass("weui-search-bar_focusing").find("input").focus()}).on("click",".weui-search-bar__cancel-btn",function(b){var c=a(b.target).parents(".weui-search-bar").removeClass("weui-search-bar_focusing").find(".weui-search-bar__input").val("").blur()}).on("click",".weui-icon-clear",function(b){var c=a(b.target).parents(".weui-search-bar").find(".weui-search-bar__input").val("").focus()})}($);(function(h){var d={};var c=navigator.userAgent;var f=c.match(/(Android);?[\s\/]+([\d.]+)?/);var m=c.match(/(iPad).*OS\s([\d_]+)/);var n=c.match(/(iPod)(.*OS\s([\d_]+))?/);var l=!m&&c.match(/(iPhone\sOS)\s([\d_]+)/);d.ios=d.android=d.iphone=d.ipad=d.androidChrome=false;if(f){d.os="android";d.osVersion=f[2];d.android=true;d.androidChrome=c.toLowerCase().indexOf("chrome")>=0}if(m||l||n){d.os="ios";d.ios=true}if(l&&!n){d.osVersion=l[2].replace(/_/g,".");d.iphone=true}if(m){d.osVersion=m[2].replace(/_/g,".");d.ipad=true}if(n){d.osVersion=n[3]?n[3].replace(/_/g,"."):null;d.iphone=true}if(d.ios&&d.osVersion&&c.indexOf("Version/")>=0){if(d.osVersion.split(".")[0]==="10"){d.osVersion=c.toLowerCase().split("version/")[1].split(" ")[0]}}d.webView=(l||m||n)&&c.match(/.*AppleWebKit(?!.*Safari)/i);if(d.os&&d.os==="ios"){var j=d.osVersion.split(".");d.minimalUi=!d.webView&&(n||l)&&(j[0]*1===7?j[1]*1>=1:j[0]*1>7)&&h('meta[name="viewport"]').length>0&&h('meta[name="viewport"]').attr("content").indexOf("minimal-ui")>=0}var e=h(window).width();var b=h(window).height();d.statusBar=false;if(d.webView&&(e*b===screen.width*screen.height)){d.statusBar=true}else{d.statusBar=false}var a=[];d.pixelRatio=window.devicePixelRatio||1;a.push("pixel-ratio-"+Math.floor(d.pixelRatio));if(d.pixelRatio>=2){a.push("retina")}if(d.os){a.push(d.os,d.os+"-"+d.osVersion.split(".")[0],d.os+"-"+d.osVersion.replace(/\./g,"-"));if(d.os==="ios"){var k=parseInt(d.osVersion.split(".")[0],10);for(var g=k-1;g>=6;g--){a.push("ios-gt-"+g)}}}if(d.statusBar){a.push("with-statusbar-overlay")}else{h("html").removeClass("with-statusbar-overlay")}if(a.length>0){h("html").addClass(a.join(" "))}h.device=d})($);+function(b){var a=function(g){var e=this;var h={updateValuesOnMomentum:false,updateValuesOnTouchmove:true,rotateEffect:false,momentumRatio:7,freeMode:false,scrollToInput:true,inputReadOnly:true,toolbar:true,toolbarCloseText:"完成",title:"请选择",toolbarTemplate:'<div class="toolbar">          <div class="toolbar-inner">          <a href="javascript:;" class="picker-button close-picker">{{closeText}}</a>          <h1 class="title">{{title}}</h1>          </div>          </div>'};g=g||{};for(var f in h){if(typeof g[f]==="undefined"){g[f]=h[f]}}e.params=g;e.cols=[];e.initialized=false;e.inline=e.params.container?true:false;var i=b.device.ios||(navigator.userAgent.toLowerCase().indexOf("safari")>=0&&navigator.userAgent.toLowerCase().indexOf("chrome")<0)&&!b.device.android;function c(){var n=false;if(!e.params.convertToPopover&&!e.params.onlyInPopover){return n}if(!e.inline&&e.params.input){if(e.params.onlyInPopover){n=true}else{if(b.device.ios){n=b.device.ipad?true:false}else{if(b(window).width()>=768){n=true}}}}return n}function m(){if(e.opened&&e.container&&e.container.length>0&&e.container.parents(".popover").length>0){return true}else{return false}}e.setValue=function(o,q){var p=0;for(var n=0;n<e.cols.length;n++){if(e.cols[n]&&!e.cols[n].divider){e.cols[n].setValue(o[p],q);p++}}};e.updateValue=function(){var p=[];var n=[];for(var o=0;o<e.cols.length;o++){if(!e.cols[o].divider){p.push(e.cols[o].value);n.push(e.cols[o].displayValue)}}if(p.indexOf(undefined)>=0){return}e.value=p;e.displayValue=n;if(e.params.onChange){e.params.onChange(e,e.value,e.displayValue)}if(e.input&&e.input.length>0){b(e.input).val(e.params.formatValue?e.params.formatValue(e,e.value,e.displayValue):e.value.join(" "));b(e.input).trigger("change")}};e.initPickerCol=function(A,y){var E=b(A);var D=E.index();var p=e.cols[D];if(p.divider){return}p.container=E;p.wrapper=p.container.find(".picker-items-col-wrapper");p.items=p.wrapper.find(".picker-item");var L,I;var H,G,r,S,o;p.replaceValues=function(T,U){p.destroyEvents();p.values=T;p.displayValues=U;var V=e.columnHTML(p,true);p.wrapper.html(V);p.items=p.wrapper.find(".picker-item");p.calcSize();p.setValue(p.values[0]||"",0,true);p.initEvents()};p.calcSize=function(){if(!p.values.length){return}if(e.params.rotateEffect){p.container.removeClass("picker-items-col-absolute");if(!p.width){p.container.css({width:""})}}var T,U;T=0;U=p.container[0].offsetHeight;H=p.wrapper[0].offsetHeight;G=p.items[0].offsetHeight;r=G*p.items.length;S=U/2-r+G/2;o=U/2-G/2;if(p.width){T=p.width;if(parseInt(T,10)===T){T=T+"px"}p.container.css({width:T})}if(e.params.rotateEffect){if(!p.width){p.items.each(function(){var V=b(this);V.css({width:"auto"});T=Math.max(T,V[0].offsetWidth);V.css({width:""})});p.container.css({width:(T+2)+"px"})}p.container.addClass("picker-items-col-absolute")}};p.calcSize();p.wrapper.transform("translate3d(0,"+o+"px,0)").transition(0);var K=0;var v;p.setValue=function(V,W,T){if(typeof W==="undefined"){W=""}var X=p.wrapper.find('.picker-item[data-picker-value="'+V+'"]').index();if(typeof X==="undefined"||X===-1){p.value=p.displayValue=V;return}var U=-X*G+o;p.wrapper.transition(W);p.wrapper.transform("translate3d(0,"+(U)+"px,0)");if(e.params.updateValuesOnMomentum&&p.activeIndex&&p.activeIndex!==X){b.cancelAnimationFrame(v);p.wrapper.transitionEnd(function(){b.cancelAnimationFrame(v)});u()}p.updateItems(X,U,W,T)};p.updateItems=function(T,Z,Y,V){if(typeof Z==="undefined"){Z=b.getTranslate(p.wrapper[0],"y")}if(typeof T==="undefined"){T=-Math.round((Z-o)/G)}if(T<0){T=0}if(T>=p.items.length){T=p.items.length-1}var W=p.activeIndex;p.activeIndex=T;p.wrapper.find(".picker-selected").removeClass("picker-selected");if(e.params.rotateEffect){p.items.transition(Y)}var X=p.items.eq(T).addClass("picker-selected").transform("");if(V||typeof V==="undefined"){p.value=X.attr("data-picker-value");p.displayValue=p.displayValues?p.displayValues[T]:p.value;if(W!==T){if(p.onChange){p.onChange(e,p.value,p.displayValue)}e.updateValue()}}if(!e.params.rotateEffect){return}var U=(Z-(Math.floor((Z-o)/G)*G+o))/G;p.items.each(function(){var ad=b(this);var af=ad.index()*G;var ab=o-Z;var ae=af-ab;var aa=ae/G;var ac=Math.ceil(p.height/G/2)+1;var ag=(-18*aa);if(ag>180){ag=180}if(ag<-180){ag=-180}if(Math.abs(aa)>ac){ad.addClass("picker-item-far")}else{ad.removeClass("picker-item-far")}ad.transform("translate3d(0, "+(-Z+o)+"px, "+(i?-110:0)+"px) rotateX("+ag+"deg)")})};function u(){v=b.requestAnimationFrame(function(){p.updateItems(undefined,undefined,0);u()})}if(y){p.updateItems(0,o,0)}var F=true;var M,x,P,q,s,O,Q,R,B,J,n,N;function w(U){if(x||M){return}U.preventDefault();M=true;var T=b.getTouchPosition(U);P=q=T.y;s=(new Date()).getTime();F=true;Q=B=b.getTranslate(p.wrapper[0],"y")}function z(V){if(!M){return}V.preventDefault();F=false;var T=b.getTouchPosition(V);q=T.y;if(!x){b.cancelAnimationFrame(v);x=true;Q=B=b.getTranslate(p.wrapper[0],"y");p.wrapper.transition(0)}V.preventDefault();var U=q-P;B=Q+U;R=undefined;if(B<S){B=S-Math.pow(S-B,0.8);R="min"}if(B>o){B=o+Math.pow(B-o,0.8);R="max"}p.wrapper.transform("translate3d(0,"+B+"px,0)");p.updateItems(undefined,B,0,e.params.updateValuesOnTouchmove);n=B-J||B;N=(new Date()).getTime();J=B}function C(W){if(!M||!x){M=x=false;return}M=x=false;p.wrapper.transition("");if(R){if(R==="min"){p.wrapper.transform("translate3d(0,"+S+"px,0)")}else{p.wrapper.transform("translate3d(0,"+o+"px,0)")}}O=new Date().getTime();var V,U;if(O-s>300){U=B}else{V=Math.abs(n/(O-N));U=B+n*e.params.momentumRatio}U=Math.max(Math.min(U,o),S);var T=-Math.floor((U-o)/G);if(!e.params.freeMode){U=-T*G+o}p.wrapper.transform("translate3d(0,"+(parseInt(U,10))+"px,0)");p.updateItems(T,U,"",true);if(e.params.updateValuesOnMomentum){u();p.wrapper.transitionEnd(function(){b.cancelAnimationFrame(v)})}setTimeout(function(){F=true},100)}function t(U){if(!F){return}b.cancelAnimationFrame(v);var T=b(this).attr("data-picker-value");p.setValue(T)}p.initEvents=function(T){var U=T?"off":"on";p.container[U](b.touchEvents.start,w);p.container[U](b.touchEvents.move,z);p.container[U](b.touchEvents.end,C);p.items[U]("click",t)};p.destroyEvents=function(){p.initEvents(true)};p.container[0].f7DestroyPickerCol=function(){p.destroyEvents()};p.initEvents()};e.destroyPickerCol=function(n){n=b(n);if("f7DestroyPickerCol" in n[0]){n[0].f7DestroyPickerCol()}};function j(){if(!e.opened){return}for(var n=0;n<e.cols.length;n++){if(!e.cols[n].divider){e.cols[n].calcSize();e.cols[n].setValue(e.cols[n].value,0,false)}}}b(window).on("resize",j);e.columnHTML=function(r,o){var p="";var n="";if(r.divider){n+='<div class="picker-items-col picker-items-col-divider '+(r.textAlign?"picker-items-col-"+r.textAlign:"")+" "+(r.cssClass||"")+'">'+r.content+"</div>"}else{for(var q=0;q<r.values.length;q++){p+='<div class="picker-item" data-picker-value="'+r.values[q]+'">'+(r.displayValues?r.displayValues[q]:r.values[q])+"</div>"}n+='<div class="picker-items-col '+(r.textAlign?"picker-items-col-"+r.textAlign:"")+" "+(r.cssClass||"")+'"><div class="picker-items-col-wrapper">'+p+"</div></div>"}return o?p:n};e.layout=function(){var r="";var q="";var p;e.cols=[];var n="";for(p=0;p<e.params.cols.length;p++){var o=e.params.cols[p];n+=e.columnHTML(e.params.cols[p]);e.cols.push(o)}q="weui-picker-modal picker-columns "+(e.params.cssClass||"")+(e.params.rotateEffect?" picker-3d":"")+(e.params.cols.length===1?" picker-columns-single":"");r='<div class="'+(q)+'">'+(e.params.toolbar?e.params.toolbarTemplate.replace(/{{closeText}}/g,e.params.toolbarCloseText).replace(/{{title}}/g,e.params.title):"")+'<div class="picker-modal-inner picker-items">'+n+'<div class="picker-center-highlight"></div></div></div>';e.pickerHTML=r};function d(t){t.preventDefault();if(e.opened){return}e.open();if(e.params.scrollToInput&&!c()){var n=e.input.parents(".content");if(n.length===0){return}var v=parseInt(n.css("padding-top"),10),q=parseInt(n.css("padding-bottom"),10),r=n[0].offsetHeight-v-e.container.height(),o=n[0].scrollHeight-v-e.container.height(),s;var u=e.input.offset().top-v+e.input[0].offsetHeight;if(u>r){var p=n.scrollTop()+u-r;if(p+r>o){s=p+r-o+q;if(r===o){s=e.container.height()}n.css({"padding-bottom":(s)+"px"})}n.scrollTop(p,300)}}}function l(n){if(m()){return}if(e.input&&e.input.length>0){if(n.target!==e.input[0]&&b(n.target).parents(".weui-picker-modal").length===0){e.close()}}else{if(b(n.target).parents(".weui-picker-modal").length===0){e.close()}}}if(e.params.input){e.input=b(e.params.input);if(e.input.length>0){if(e.params.inputReadOnly){e.input.prop("readOnly",true)}if(!e.inline){e.input.on("click",d)}if(e.params.inputReadOnly){e.input.on("focus mousedown",function(n){n.preventDefault()})}}}if(!e.inline){b("html").on("click",l)}function k(){e.opened=false;if(e.input&&e.input.length>0){e.input.parents(".page-content").css({"padding-bottom":""})}if(e.params.onClose){e.params.onClose(e)}e.container.find(".picker-items-col").each(function(){e.destroyPickerCol(this)})}e.opened=false;e.open=function(){var n=c();if(!e.opened){e.layout();if(n){e.pickerHTML='<div class="popover popover-picker-columns"><div class="popover-inner">'+e.pickerHTML+"</div></div>";e.popover=b.popover(e.pickerHTML,e.params.input,true);e.container=b(e.popover).find(".weui-picker-modal");b(e.popover).on("close",function(){k()})}else{if(e.inline){e.container=b(e.pickerHTML);e.container.addClass("picker-modal-inline");b(e.params.container).append(e.container)}else{e.container=b(b.openPicker(e.pickerHTML));b(e.container).on("close",function(){k()})}}e.container[0].f7Picker=e;e.container.find(".picker-items-col").each(function(){var o=true;if((!e.initialized&&e.params.value)||(e.initialized&&e.value)){o=false}e.initPickerCol(this,o)});if(!e.initialized){if(e.params.value){e.setValue(e.params.value,0)}}else{if(e.value){e.setValue(e.value,0)}}}e.opened=true;e.initialized=true;if(e.params.onOpen){e.params.onOpen(e)}};e.close=function(n){if(!e.opened||e.inline){return}if(m()){b.closePicker(e.popover);return}else{b.closePicker(e.container);return}};e.destroy=function(){e.close();if(e.params.input&&e.input.length>0){e.input.off("click focus",d);b(e.input).data("picker",null)}b("html").off("click",l);b(window).off("resize",j)};if(e.inline){e.open()}return e};b(document).on("click",".close-picker",function(){var c=b(".weui-picker-modal.weui-picker-modal-visible");if(c.length>0){b.closePicker(c)}});b(document).on(b.touchEvents.move,".picker-modal-inner",function(c){c.preventDefault()});b.openPicker=function(d,f,g){if(typeof f==="function"){g=f;f=undefined}b.closePicker();var c=b("<div class='weui-picker-container "+(f||"")+"'></div>").appendTo(document.body);c.show();c.addClass("weui-picker-container-visible");var e=b(d).appendTo(c);e.width();e.addClass("weui-picker-modal-visible");g&&c.on("close",g);return e};b.updatePicker=function(d){var c=b(".weui-picker-container-visible");if(!c[0]){return false}c.html("");var e=b(d).appendTo(c);e.addClass("weui-picker-modal-visible");return e};b.closePicker=function(c,d){if(typeof c==="function"){d=c}b(".weui-picker-modal-visible").removeClass("weui-picker-modal-visible").transitionEnd(function(){b(this).parent().remove();d&&d()}).trigger("close")};b.fn.picker=function(d){var c=arguments;return this.each(function(){if(!this){return}var h=b(this);var f=h.data("picker");if(!f){d=b.extend({input:this},d||{});var e=h.val();if(d.value===undefined&&e!==""){d.value=(d.cols&&d.cols.length>1)?e.split(" "):[e]}var g=b.extend({input:this},d);f=new a(g);h.data("picker",f)}if(typeof d===typeof"a"){f[d].apply(f,Array.prototype.slice.call(c,1))}})}}($);+function(b){var c;var a=[];var d=function(f,g){var e=this;this.config=g;this.data={values:"",titles:"",origins:[],length:0};this.$input=b(f);this.$input.prop("readOnly",true);this.initConfig();g=this.config;this.$input.click(b.proxy(this.open,this));a.push(this)};d.prototype.initConfig=function(){this.config=b.extend({},c,this.config);var e=this.config;if(!e.items||!e.items.length){return}e.items=e.items.map(function(g,f){if(typeof g==typeof"a"){return{title:g,value:g}}return g});this.tpl=b.t7.compile("<div class='weui-picker-modal weui-select-modal'>"+e.toolbarTemplate+(e.multi?e.checkboxTemplate:e.radioTemplate)+"</div>");if(e.input!==undefined){this.$input.val(e.input)}this.parseInitValue();this._init=true};d.prototype.updateInputValue=function(f,j){var e,g;if(this.config.multi){e=f.join(this.config.split);g=j.join(this.config.split)}else{e=f[0];g=j[0]}var i=[];this.config.items.forEach(function(k){f.each(function(m,l){if(k.value==l){i.push(k)}})});this.$input.val(g).data("values",e);this.$input.attr("value",g).attr("data-values",e);var h={values:e,titles:g,valuesArray:f,titlesArray:j,origins:i,length:i.length};this.data=h;this.$input.trigger("change",h);this.config.onChange&&this.config.onChange.call(this,h)};d.prototype.parseInitValue=function(){var h=this.$input.val();var e=this.config.items;if(!this._init&&(h===undefined||h==null||h==="")){return}var k=this.config.multi?h.split(this.config.split):[h];for(var g=0;g<e.length;g++){e[g].checked=false;for(var f=0;f<k.length;f++){if(e[g].title===k[f]){e[g].checked=true}}}};d.prototype._bind=function(g){var e=this,f=this.config;g.on("change",function(j){var i=g.find("input:checked");var h=i.map(function(){return b(this).val()});var k=i.map(function(){return b(this).data("title")});e.updateInputValue(h,k);if(f.autoClose&&!f.multi){e.close()}}).on("click",".close-select",function(){e.close()})};d.prototype.update=function(e){this.config=b.extend({},this.config,e);this.initConfig();if(this._open){this._bind(b.updatePicker(this.getHTML()))}};d.prototype.open=function(e,k){if(this._open){return}for(var h=0;h<a.length;h++){var j=a[h];if(j===this){continue}if(j._open){if(!j.close()){return false}}}this.parseInitValue();var f=this.config;var g=this.dialog=b.openPicker(this.getHTML());this._bind(g);this._open=true;if(f.onOpen){f.onOpen(this)}};d.prototype.close=function(h,g){if(!this._open){return false}var e=this,f=this.config.beforeClose;if(typeof h===typeof true){g===h}if(!g){if(f&&typeof f==="function"&&f.call(this,this.data.values,this.data.titles)===false){return false}if(this.config.multi){if(this.config.min!==undefined&&this.data.length<this.config.min){b.toast("请至少选择"+this.config.min+"个","text");return false}if(this.config.max!==undefined&&this.data.length>this.config.max){b.toast("最多只能选择"+this.config.max+"个","text");return false}}}b.closePicker(function(){e.onClose();h&&h()});return true};d.prototype.onClose=function(){this._open=false;if(this.config.onClose){this.config.onClose(this)}};d.prototype.getHTML=function(f){var e=this.config;return this.tpl({items:e.items,title:e.title,closeText:e.closeText})};b.fn.select=function(f,e){return this.each(function(){var h=b(this);if(!h.data("weui-select")){h.data("weui-select",new d(this,f))}var g=h.data("weui-select");if(typeof f===typeof"a"){g[f].call(g,e)}return g})};c=b.fn.select.prototype.defaults={items:[],input:undefined,title:"请选择",multi:false,closeText:"确定",autoClose:true,onChange:undefined,beforeClose:undefined,onClose:undefined,onOpen:undefined,split:",",min:undefined,max:undefined,toolbarTemplate:'<div class="toolbar">      <div class="toolbar-inner">      <a href="javascript:;" class="picker-button close-select">{{closeText}}</a>      <h1 class="title">{{title}}</h1>      </div>      </div>',radioTemplate:'<div class="weui-cells weui-cells_radio">              {{#items}}              <label class="weui-cell weui-check_label" for="weui-select-id-{{this.title}}">                <div class="weui-cell__bd weui-cell_primary">                  <p>{{this.title}}</p>                </div>                <div class="weui-cell__ft">                  <input type="radio" class="weui-check" name="weui-select" id="weui-select-id-{{this.title}}" value="{{this.value}}" {{#if this.checked}}checked="checked"{{/if}} data-title="{{this.title}}">                  <span class="weui-icon-checked"></span>                </div>              </label>              {{/items}}            </div>',checkboxTemplate:'<div class="weui-cells weui-cells_checkbox">              {{#items}}              <label class="weui-cell weui-check_label" for="weui-select-id-{{this.title}}">                <div class="weui-cell__bd weui-cell_primary">                  <p>{{this.title}}</p>                </div>                <div class="weui-cell__ft">                  <input type="checkbox" class="weui-check" name="weui-select" id="weui-select-id-{{this.title}}" value="{{this.value}}" {{#if this.checked}}checked="checked"{{/if}} data-title="{{this.title}}" >                  <span class="weui-icon-checked"></span>                </div>              </label>              {{/items}}            </div>'}}($);+function(b){var e=false;var d;var a=function(h,g){var h=new Date(h),g=new Date(g);return h.getFullYear()===g.getFullYear()&&h.getMonth()===g.getMonth()&&h.getDate()===g.getDate()};var f=function(k){var h=this;k=k||{};for(var j in d){if(typeof k[j]==="undefined"){k[j]=d[j]}}h.params=k;h.initialized=false;h.inline=h.params.container?true:false;h.isH=h.params.direction==="horizontal";var l=h.isH?(e?-1:1):1;h.animating=false;function g(){var p=false;if(!h.params.convertToPopover&&!h.params.onlyInPopover){return p}if(!h.inline&&h.params.input){if(h.params.onlyInPopover){p=true}else{if(b.device.ios){p=b.device.ipad?true:false}else{if(b(window).width()>=768){p=true}}}}return p}function q(){if(h.opened&&h.container&&h.container.length>0&&h.container.parents(".popover").length>0){return true}else{return false}}function n(t){t=new Date(t);var u=t.getFullYear();var v=t.getMonth();var p=v+1;var r=t.getDate();var s=t.getDay();return h.params.dateFormat.replace(/yyyy/g,u).replace(/yy/g,(u+"").substring(2)).replace(/mm/g,p<10?"0"+p:p).replace(/m/g,p).replace(/MM/g,h.params.monthNames[v]).replace(/M/g,h.params.monthNamesShort[v]).replace(/dd/g,r<10?"0"+r:r).replace(/d/g,r).replace(/DD/g,h.params.dayNames[s]).replace(/D/g,h.params.dayNamesShort[s])}h.addValue=function(s){if(h.params.multiple){if(!h.value){h.value=[]}var r;for(var p=0;p<h.value.length;p++){if(a(s,h.value[p])){r=p}}if(typeof r==="undefined"){h.value.push(s)}else{h.value.splice(r,1)}h.updateValue()}else{h.value=[s];h.updateValue()}};h.setValue=function(r){var p=new Date(r[0]);h.setYearMonth(p.getFullYear(),p.getMonth());h.addValue(+p)};h.updateValue=function(){h.wrapper.find(".picker-calendar-day-selected").removeClass("picker-calendar-day-selected");var r,p;for(r=0;r<h.value.length;r++){var s=new Date(h.value[r]);h.wrapper.find('.picker-calendar-day[data-date="'+s.getFullYear()+"-"+s.getMonth()+"-"+s.getDate()+'"]').addClass("picker-calendar-day-selected")}if(h.params.onChange){h.params.onChange(h,h.value.map(n),h.value.map(function(t){return +new Date(typeof t===typeof"a"?t.split(/\D/).filter(function(u){return !!u}).join("-"):t)}))}if(h.input&&h.input.length>0){if(h.params.formatValue){p=h.params.formatValue(h,h.value)}else{p=[];for(r=0;r<h.value.length;r++){p.push(n(h.value[r]))}p=p.join(", ")}b(h.input).val(p);b(h.input).trigger("change")}};h.initCalendarEvents=function(){var r;var C=true;var F,w,J,H,t,s,u,G,I,z,p,D,y,B,K;function v(M){if(w||F){return}F=true;var L=b.getTouchPosition(M);J=s=L.x;H=s=L.y;u=(new Date()).getTime();y=0;C=true;K=undefined;I=z=h.monthsTranslate}function x(M){if(!F){return}var L=b.getTouchPosition(M);t=L.x;s=L.y;if(typeof K==="undefined"){K=!!(K||Math.abs(s-H)>Math.abs(t-J))}if(h.isH&&K){F=false;return}M.preventDefault();if(h.animating){F=false;return}C=false;if(!w){w=true;p=h.wrapper[0].offsetWidth;D=h.wrapper[0].offsetHeight;h.wrapper.transition(0)}M.preventDefault();B=h.isH?t-J:s-H;y=B/(h.isH?p:D);z=(h.monthsTranslate*l+y)*100;h.wrapper.transform("translate3d("+(h.isH?z:0)+"%, "+(h.isH?0:z)+"%, 0)")}function A(L){if(!F||!w){F=w=false;return}F=w=false;G=new Date().getTime();if(G-u<300){if(Math.abs(B)<10){h.resetMonth()}else{if(B>=10){if(e){h.nextMonth()}else{h.prevMonth()}}else{if(e){h.prevMonth()}else{h.nextMonth()}}}}else{if(y<=-0.5){if(e){h.prevMonth()}else{h.nextMonth()}}else{if(y>=0.5){if(e){h.nextMonth()}else{h.prevMonth()}}else{h.resetMonth()}}}setTimeout(function(){C=true},100)}function E(P){if(!C){return}var L=b(P.target).parents(".picker-calendar-day");if(L.length===0&&b(P.target).hasClass("picker-calendar-day")){L=b(P.target)}if(L.length===0){return}if(L.hasClass("picker-calendar-day-disabled")){return}if(L.hasClass("picker-calendar-day-next")){h.nextMonth()}if(L.hasClass("picker-calendar-day-prev")){h.prevMonth()}var M=L.attr("data-year");var N=L.attr("data-month");var O=L.attr("data-day");if(h.params.onDayClick){h.params.onDayClick(h,L[0],M,N,O)}h.addValue(new Date(M,N,O).getTime());if(h.params.closeOnSelect&&!h.params.multiple){h.close()}}h.container.find(".picker-calendar-prev-month").on("click",h.prevMonth);h.container.find(".picker-calendar-next-month").on("click",h.nextMonth);h.container.find(".picker-calendar-prev-year").on("click",h.prevYear);h.container.find(".picker-calendar-next-year").on("click",h.nextYear);h.wrapper.on("click",E);if(h.params.touchMove){h.wrapper.on(b.touchEvents.start,v);h.wrapper.on(b.touchEvents.move,x);h.wrapper.on(b.touchEvents.end,A)}h.container[0].f7DestroyCalendarEvents=function(){h.container.find(".picker-calendar-prev-month").off("click",h.prevMonth);h.container.find(".picker-calendar-next-month").off("click",h.nextMonth);h.container.find(".picker-calendar-prev-year").off("click",h.prevYear);h.container.find(".picker-calendar-next-year").off("click",h.nextYear);h.wrapper.off("click",E);if(h.params.touchMove){h.wrapper.off(b.touchEvents.start,v);h.wrapper.off(b.touchEvents.move,x);h.wrapper.off(b.touchEvents.end,A)}}};h.destroyCalendarEvents=function(p){if("f7DestroyCalendarEvents" in h.container[0]){h.container[0].f7DestroyCalendarEvents()}};h.daysInMonth=function(p){var r=new Date(p);return new Date(r.getFullYear(),r.getMonth()+1,0).getDate()};h.monthHTML=function(M,w){M=new Date(M);var D=M.getFullYear(),N=M.getMonth(),J=M.getDate();if(w==="next"){if(N===11){M=new Date(D+1,0)}else{M=new Date(D,N+1,1)}}if(w==="prev"){if(N===0){M=new Date(D-1,11)}else{M=new Date(D,N-1,1)}}if(w==="next"||w==="prev"){N=M.getMonth();D=M.getFullYear()}var E=h.daysInMonth(new Date(M.getFullYear(),M.getMonth()).getTime()-10*24*60*60*1000),p=h.daysInMonth(M),H=new Date(M.getFullYear(),M.getMonth()).getDay();if(H===0){H=7}var G,r=[],K,I,z=6,B=7,O="",F=0+(h.params.firstDay-1),L=new Date().setHours(0,0,0,0),y=h.params.minDate?new Date(h.params.minDate).getTime():null,A=h.params.maxDate?new Date(h.params.maxDate).getTime():null;if(h.value&&h.value.length){for(K=0;K<h.value.length;K++){r.push(new Date(h.value[K]).setHours(0,0,0,0))}}for(K=1;K<=z;K++){var P="";var x=K;for(I=1;I<=B;I++){var u=I;F++;var t=F-H;var C="";if(t<0){t=E+t+1;C+=" picker-calendar-day-prev";G=new Date(N-1<0?D-1:D,N-1<0?11:N-1,t).getTime()}else{t=t+1;if(t>p){t=t-p;C+=" picker-calendar-day-next";G=new Date(N+1>11?D+1:D,N+1>11?0:N+1,t).getTime()}else{G=new Date(D,N,t).getTime()}}if(G===L){C+=" picker-calendar-day-today"}if(r.indexOf(G)>=0){C+=" picker-calendar-day-selected"}if(h.params.weekendDays.indexOf(u-1)>=0){C+=" picker-calendar-day-weekend"}if((y&&G<y)||(A&&G>A)){C+=" picker-calendar-day-disabled"}G=new Date(G);var v=G.getFullYear();var s=G.getMonth();P+='<div data-year="'+v+'" data-month="'+s+'" data-day="'+t+'" class="picker-calendar-day'+(C)+'" data-date="'+(v+"-"+s+"-"+t)+'"><span>'+t+"</span></div>"}O+='<div class="picker-calendar-row">'+P+"</div>"}O='<div class="picker-calendar-month" data-year="'+D+'" data-month="'+N+'">'+O+"</div>";return O};h.animating=false;h.updateCurrentMonthYear=function(p){if(typeof p==="undefined"){h.currentMonth=parseInt(h.months.eq(1).attr("data-month"),10);h.currentYear=parseInt(h.months.eq(1).attr("data-year"),10)}else{h.currentMonth=parseInt(h.months.eq(p==="next"?(h.months.length-1):0).attr("data-month"),10);h.currentYear=parseInt(h.months.eq(p==="next"?(h.months.length-1):0).attr("data-year"),10)}h.container.find(".current-month-value").text(h.params.monthNames[h.currentMonth]);h.container.find(".current-year-value").text(h.currentYear)};h.onMonthChangeStart=function(r){h.updateCurrentMonthYear(r);h.months.removeClass("picker-calendar-month-current picker-calendar-month-prev picker-calendar-month-next");var p=r==="next"?h.months.length-1:0;h.months.eq(p).addClass("picker-calendar-month-current");h.months.eq(r==="next"?p-1:p+1).addClass(r==="next"?"picker-calendar-month-prev":"picker-calendar-month-next");if(h.params.onMonthYearChangeStart){h.params.onMonthYearChangeStart(h,h.currentYear,h.currentMonth)}};h.onMonthChangeEnd=function(p,u){h.animating=false;var t,r,s;h.wrapper.find(".picker-calendar-month:not(.picker-calendar-month-prev):not(.picker-calendar-month-current):not(.picker-calendar-month-next)").remove();if(typeof p==="undefined"){p="next";u=true}if(!u){s=h.monthHTML(new Date(h.currentYear,h.currentMonth),p)}else{h.wrapper.find(".picker-calendar-month-next, .picker-calendar-month-prev").remove();r=h.monthHTML(new Date(h.currentYear,h.currentMonth),"prev");t=h.monthHTML(new Date(h.currentYear,h.currentMonth),"next")}if(p==="next"||u){h.wrapper.append(s||t)}if(p==="prev"||u){h.wrapper.prepend(s||r)}h.months=h.wrapper.find(".picker-calendar-month");h.setMonthsTranslate(h.monthsTranslate);if(h.params.onMonthAdd){h.params.onMonthAdd(h,p==="next"?h.months.eq(h.months.length-1)[0]:h.months.eq(0)[0])}if(h.params.onMonthYearChangeEnd){h.params.onMonthYearChangeEnd(h,h.currentYear,h.currentMonth)}};h.setMonthsTranslate=function(t){t=t||h.monthsTranslate||0;if(typeof h.monthsTranslate==="undefined"){h.monthsTranslate=t}h.months.removeClass("picker-calendar-month-current picker-calendar-month-prev picker-calendar-month-next");var r=-(t+1)*100*l;var s=-t*100*l;var p=-(t-1)*100*l;h.months.eq(0).transform("translate3d("+(h.isH?r:0)+"%, "+(h.isH?0:r)+"%, 0)").addClass("picker-calendar-month-prev");h.months.eq(1).transform("translate3d("+(h.isH?s:0)+"%, "+(h.isH?0:s)+"%, 0)").addClass("picker-calendar-month-current");h.months.eq(2).transform("translate3d("+(h.isH?p:0)+"%, "+(h.isH?0:p)+"%, 0)").addClass("picker-calendar-month-next")};h.nextMonth=function(v){if(typeof v==="undefined"||typeof v==="object"){v="";if(!h.params.animate){v=0}}var u=parseInt(h.months.eq(h.months.length-1).attr("data-month"),10);var w=parseInt(h.months.eq(h.months.length-1).attr("data-year"),10);var s=new Date(w,u);var r=s.getTime();var x=h.animating?false:true;if(h.params.maxDate){if(r>new Date(h.params.maxDate).getTime()){return h.resetMonth()}}h.monthsTranslate--;if(u===h.currentMonth){var y=-(h.monthsTranslate)*100*l;var t=b(h.monthHTML(r,"next")).transform("translate3d("+(h.isH?y:0)+"%, "+(h.isH?0:y)+"%, 0)").addClass("picker-calendar-month-next");h.wrapper.append(t[0]);h.months=h.wrapper.find(".picker-calendar-month");if(h.params.onMonthAdd){h.params.onMonthAdd(h,h.months.eq(h.months.length-1)[0])}}h.animating=true;h.onMonthChangeStart("next");var p=(h.monthsTranslate*100)*l;h.wrapper.transition(v).transform("translate3d("+(h.isH?p:0)+"%, "+(h.isH?0:p)+"%, 0)");if(x){h.wrapper.transitionEnd(function(){h.onMonthChangeEnd("next")})}if(!h.params.animate){h.onMonthChangeEnd("next")}};h.prevMonth=function(w){if(typeof w==="undefined"||typeof w==="object"){w="";if(!h.params.animate){w=0}}var s=parseInt(h.months.eq(0).attr("data-month"),10);var v=parseInt(h.months.eq(0).attr("data-year"),10);var p=new Date(v,s+1,-1);var x=p.getTime();var y=h.animating?false:true;if(h.params.minDate){if(x<new Date(h.params.minDate).getTime()){return h.resetMonth()}}h.monthsTranslate++;if(s===h.currentMonth){var t=-(h.monthsTranslate)*100*l;var u=b(h.monthHTML(x,"prev")).transform("translate3d("+(h.isH?t:0)+"%, "+(h.isH?0:t)+"%, 0)").addClass("picker-calendar-month-prev");h.wrapper.prepend(u[0]);h.months=h.wrapper.find(".picker-calendar-month");if(h.params.onMonthAdd){h.params.onMonthAdd(h,h.months.eq(0)[0])}}h.animating=true;h.onMonthChangeStart("prev");var r=(h.monthsTranslate*100)*l;h.wrapper.transition(w).transform("translate3d("+(h.isH?r:0)+"%, "+(h.isH?0:r)+"%, 0)");if(y){h.wrapper.transitionEnd(function(){h.onMonthChangeEnd("prev")})}if(!h.params.animate){h.onMonthChangeEnd("prev")}};h.resetMonth=function(r){if(typeof r==="undefined"){r=""}var p=(h.monthsTranslate*100)*l;h.wrapper.transition(r).transform("translate3d("+(h.isH?p:0)+"%, "+(h.isH?0:p)+"%, 0)")};h.setYearMonth=function(w,t,v){if(typeof w==="undefined"){w=h.currentYear}if(typeof t==="undefined"){t=h.currentMonth}if(typeof v==="undefined"||typeof v==="object"){v="";if(!h.params.animate){v=0}}var u;if(w<h.currentYear){u=new Date(w,t+1,-1).getTime()}else{u=new Date(w,t).getTime()}if(h.params.maxDate&&u>new Date(h.params.maxDate).getTime()){return false}if(h.params.minDate&&u<new Date(h.params.minDate).getTime()){return false}var p=new Date(h.currentYear,h.currentMonth).getTime();var s=u>p?"next":"prev";var y=h.monthHTML(new Date(w,t));h.monthsTranslate=h.monthsTranslate||0;var A=h.monthsTranslate;var z,r;var x=h.animating?false:true;if(u>p){h.monthsTranslate--;if(!h.animating){h.months.eq(h.months.length-1).remove()}h.wrapper.append(y);h.months=h.wrapper.find(".picker-calendar-month");z=-(A-1)*100*l;h.months.eq(h.months.length-1).transform("translate3d("+(h.isH?z:0)+"%, "+(h.isH?0:z)+"%, 0)").addClass("picker-calendar-month-next")}else{h.monthsTranslate++;if(!h.animating){h.months.eq(0).remove()}h.wrapper.prepend(y);h.months=h.wrapper.find(".picker-calendar-month");z=-(A+1)*100*l;h.months.eq(0).transform("translate3d("+(h.isH?z:0)+"%, "+(h.isH?0:z)+"%, 0)").addClass("picker-calendar-month-prev")}if(h.params.onMonthAdd){h.params.onMonthAdd(h,s==="next"?h.months.eq(h.months.length-1)[0]:h.months.eq(0)[0])}h.animating=true;h.onMonthChangeStart(s);r=(h.monthsTranslate*100)*l;h.wrapper.transition(v).transform("translate3d("+(h.isH?r:0)+"%, "+(h.isH?0:r)+"%, 0)");if(x){h.wrapper.transitionEnd(function(){h.onMonthChangeEnd(s,true)})}if(!h.params.animate){h.onMonthChangeEnd(s)}};h.nextYear=function(){h.setYearMonth(h.currentYear+1)};h.prevYear=function(){h.setYearMonth(h.currentYear-1)};h.layout=function(){var r="";var s="";var x;var u=h.value&&h.value.length?h.value[0]:new Date().setHours(0,0,0,0);var z=h.monthHTML(u,"prev");var w=h.monthHTML(u);var v=h.monthHTML(u,"next");var t='<div class="picker-calendar-months"><div class="picker-calendar-months-wrapper">'+(z+w+v)+"</div></div>";var y="";if(h.params.weekHeader){for(x=0;x<7;x++){var A=(x+h.params.firstDay>6)?(x-7+h.params.firstDay):(x+h.params.firstDay);var B=h.params.dayNamesShort[A];y+='<div class="picker-calendar-week-day '+((h.params.weekendDays.indexOf(A)>=0)?"picker-calendar-week-day-weekend":"")+'"> '+B+"</div>"}y='<div class="picker-calendar-week-days">'+y+"</div>"}s="weui-picker-calendar "+(h.params.cssClass||"");if(!h.inline){s="weui-picker-modal "+s}var p=h.params.toolbar?h.params.toolbarTemplate.replace(/{{closeText}}/g,h.params.toolbarCloseText):"";if(h.params.toolbar){p=h.params.toolbarTemplate.replace(/{{closeText}}/g,h.params.toolbarCloseText).replace(/{{monthPicker}}/g,(h.params.monthPicker?h.params.monthPickerTemplate:"")).replace(/{{yearPicker}}/g,(h.params.yearPicker?h.params.yearPickerTemplate:""))}r='<div class="'+(s)+'">'+p+'<div class="picker-modal-inner">'+y+t+"</div></div>";h.pickerHTML=r};function i(w){w.preventDefault();if(h.opened){return}h.open();if(h.params.scrollToInput&&!g()){var p=h.input.parents(".page-content");if(p.length===0){return}var y=parseInt(p.css("padding-top"),10),t=parseInt(p.css("padding-bottom"),10),u=p[0].offsetHeight-y-h.container.height(),r=p[0].scrollHeight-y-h.container.height(),v;var x=h.input.offset().top-y+h.input[0].offsetHeight;if(x>u){var s=p.scrollTop()+x-u;if(s+u>r){v=s+u-r+t;if(u===r){v=h.container.height()}p.css({"padding-bottom":(v)+"px"})}p.scrollTop(s,300)}}}function o(p){if(q()){return}if(h.input&&h.input.length>0){if(p.target!==h.input[0]&&b(p.target).parents(".weui-picker-modal").length===0){h.close()}}else{if(b(p.target).parents(".weui-picker-modal").length===0){h.close()}}}if(h.params.input){h.input=b(h.params.input);if(h.input.length>0){if(h.params.inputReadOnly){h.input.prop("readOnly",true)}if(!h.inline){h.input.on("click",i)}if(h.params.inputReadOnly){h.input.on("focus mousedown",function(p){p.preventDefault()})}}}if(!h.inline){b(document).on("click touchend",o)}function m(){h.opened=false;if(h.input&&h.input.length>0){h.input.parents(".page-content").css({"padding-bottom":""})}if(h.params.onClose){h.params.onClose(h)}h.destroyCalendarEvents()}h.opened=false;h.open=function(){var r=g()&&false;var p=false;if(!h.opened){if(!h.value){if(h.params.value){h.value=h.params.value;p=true}}h.layout();if(r){h.pickerHTML='<div class="popover popover-picker-calendar"><div class="popover-inner">'+h.pickerHTML+"</div></div>";h.popover=b.popover(h.pickerHTML,h.params.input,true);h.container=b(h.popover).find(".weui-picker-modal");b(h.popover).on("close",function(){m()})}else{if(h.inline){h.container=b(h.pickerHTML);h.container.addClass("picker-modal-inline");b(h.params.container).append(h.container)}else{h.container=b(b.openPicker(h.pickerHTML));b(h.container).on("close",function(){m()})}}h.container[0].f7Calendar=h;h.wrapper=h.container.find(".picker-calendar-months-wrapper");h.months=h.wrapper.find(".picker-calendar-month");h.updateCurrentMonthYear();h.monthsTranslate=0;h.setMonthsTranslate();h.initCalendarEvents();if(p){h.updateValue()}}h.opened=true;h.initialized=true;if(h.params.onMonthAdd){h.months.each(function(){h.params.onMonthAdd(h,this)})}if(h.params.onOpen){h.params.onOpen(h)}};h.close=function(){if(!h.opened||h.inline){return}h.animating=false;if(q()){b.closePicker(h.popover);return}else{b.closePicker(h.container);return}};h.destroy=function(){h.close();if(h.params.input&&h.input.length>0){h.input.off("click focus",i);h.input.data("calendar",null)}b("html").off("click",o)};if(h.inline){h.open()}return h};var c=function(g){return g<10?"0"+g:g};b.fn.calendar=function(h,g){h=h||{};return this.each(function(){var l=b(this);if(!l[0]){return}var k={};if(l[0].tagName.toUpperCase()==="INPUT"){k.input=l}else{k.container=l}var j=l.data("calendar");if(!j){if(typeof h===typeof"a"){}else{if(!h.value&&l.val()){h.value=[l.val()]}if(!h.value){var i=new Date();h.value=[i.getFullYear()+"/"+c(i.getMonth()+1)+"/"+c(i.getDate())]}j=l.data("calendar",new f(b.extend(k,h)))}}if(typeof h===typeof"a"){j[h].call(j,g)}})};d=b.fn.calendar.prototype.defaults={value:undefined,monthNames:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthNamesShort:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],dayNames:["周日","周一","周二","周三","周四","周五","周六"],dayNamesShort:["周日","周一","周二","周三","周四","周五","周六"],firstDay:1,weekendDays:[0,6],multiple:false,dateFormat:"yyyy/mm/dd",direction:"horizontal",minDate:null,maxDate:null,touchMove:true,animate:true,closeOnSelect:true,monthPicker:true,monthPickerTemplate:'<div class="picker-calendar-month-picker"><a href="javascript:;" class="link icon-only picker-calendar-prev-month"><i class="icon icon-prev"></i></a><div class="current-month-value"></div><a href="javascript:;" class="link icon-only picker-calendar-next-month"><i class="icon icon-next"></i></a></div>',yearPicker:true,yearPickerTemplate:'<div class="picker-calendar-year-picker"><a href="javascript:;" class="link icon-only picker-calendar-prev-year"><i class="icon icon-prev"></i></a><span class="current-year-value"></span><a href="javascript:;" class="link icon-only picker-calendar-next-year"><i class="icon icon-next"></i></a></div>',weekHeader:true,scrollToInput:true,inputReadOnly:true,convertToPopover:true,onlyInPopover:false,toolbar:true,toolbarCloseText:"Done",toolbarTemplate:'<div class="toolbar"><div class="toolbar-inner">{{yearPicker}}{{monthPicker}}</div></div>'}}($);+function(c){var d;var b=function(e){return e<10?"0"+e:e};var a=function(e,g){this.input=c(e);this.params=g||{};this.initMonthes=g.monthes;this.initYears=g.years;var f=c.extend({},g,this.getConfig());c(this.input).picker(f)};a.prototype={getDays:function(e){var g=[];for(var f=1;f<=(e||31);f++){g.push(f<10?"0"+f:f)}return g},getDaysByMonthAndYear:function(g,f){var e=new Date(f,parseInt(g)+1-1,1);var h=new Date(e-1);return this.getDays(h.getDate())},getConfig:function(){var h=new Date(),k=this.params,g=this,f;var i={rotateEffect:false,cssClass:"datetime-picker",value:[h.getFullYear(),b(h.getMonth()+1),b(h.getDate()),b(h.getHours()),(b(h.getMinutes()))],onChange:function(o,t,m){var r=o.cols;var u=g.getDaysByMonthAndYear(t[1],t[0]);var s=t[2];if(s>u.length){s=u.length}o.cols[4].setValue(s);var p=new Date(t[0]+"-"+t[1]+"-"+t[2]);var l=true;if(k.min){var n=new Date(typeof k.min==="function"?k.min():k.min);if(p<+n){o.setValue(f);l=false}}if(k.max){var q=new Date(typeof k.max==="function"?k.max():k.max);if(p>+q){o.setValue(f);l=false}}l&&(f=t);if(g.params.onChange){g.params.onChange.apply(this,arguments)}},formatValue:function(n,l,m){return g.params.format(n,l,m)},cols:[{values:this.initYears},{divider:true,content:k.yearSplit},{values:this.initMonthes},{divider:true,content:k.monthSplit},{values:(function(){var m=[];for(var l=1;l<=31;l++){m.push(b(l))}return m})()}]};if(k.dateSplit){i.cols.push({divider:true,content:k.dateSplit})}i.cols.push({divider:true,content:k.datetimeSplit});var j=g.params.times();if(j&&j.length){i.cols=i.cols.concat(j)}var e=this.input.val();if(e){i.value=k.parse(e)}if(this.params.value){this.input.val(this.params.value);i.value=k.parse(this.params.value)}return i}};c.fn.datetimePicker=function(e){e=c.extend({},d,e);return this.each(function(){if(!this){return}var f=c(this);var g=f.data("datetime");if(!g){f.data("datetime",new a(this,e))}return g})};d=c.fn.datetimePicker.prototype.defaults={input:undefined,min:undefined,max:undefined,yearSplit:"-",monthSplit:"-",dateSplit:"",datetimeSplit:" ",monthes:("01 02 03 04 05 06 07 08 09 10 11 12").split(" "),years:(function(){var e=[];for(var f=1930;f<=2080;f++){e.push(f)}return e})(),times:function(){return[{values:(function(){var e=[];for(var f=0;f<24;f++){e.push(b(f))}return e})()},{divider:true,content:":"},{values:(function(){var f=[];for(var e=0;e<60;e++){f.push(b(e))}return f})()}]},format:function(f,e){return f.cols.map(function(g){return g.value||g.content}).join("")},parse:function(f){var e=f.split(this.datetimeSplit);return e[0].split(/\D/).concat(e[1].split(/:|时|分|秒/)).filter(function(g){return !!g})}}}($);+function(a){a.openPopup=function(b,c){a.closePopup();b=a(b);b.show();b.width();b.addClass("weui-popup__container--visible");var d=b.find(".weui-popup__modal");d.width();d.transitionEnd(function(){d.trigger("open")})};a.closePopup=function(c,b){c=a(c||".weui-popup__container--visible");c.find(".weui-popup__modal").transitionEnd(function(){var d=a(this);d.trigger("close");c.hide();b&&c.remove()});c.removeClass("weui-popup__container--visible")};a(document).on("click",".close-popup, .weui-popup__overlay",function(){a.closePopup()}).on("click",".open-popup",function(){a(a(this).data("target")).popup()}).on("click",".weui-popup__container",function(b){if(a(b.target).hasClass("weui-popup__container")){a.closePopup()}});a.fn.popup=function(){return this.each(function(){a.openPopup(this)})}}($);+function(d){var e,c,g,a,j,h;var b=function(m){var l=d.getTouchPosition(m);a=l;j=h=0;e.addClass("touching")};var f=function(m){if(!a){return false}m.preventDefault();m.stopPropagation();var l=d.getTouchPosition(m);j=l.x-a.x;h=l.y-a.y;if(h>0){h=Math.sqrt(h)}e.css("transform","translate3d(0, "+h+"px, 0)")};var k=function(){e.removeClass("touching");e.attr("style","");if(h<0&&(Math.abs(h)>e.height()*0.38)){d.closeNotification()}if(Math.abs(j)<=1&&Math.abs(h)<=1){e.trigger("noti-click")}a=false};var i=function(l){l.on(d.touchEvents.start,b);l.on(d.touchEvents.move,f);l.on(d.touchEvents.end,k)};d.notification=d.noti=function(m){m=d.extend({},c,m);e=d(".weui-notification");if(!e[0]){e=d('<div class="weui-notification"></div>').appendTo(document.body);i(e)}e.off("noti-click");if(m.onClick){e.on("noti-click",function(){m.onClick(m.data)})}e.html(d.t7.compile(m.tpl)(m));e.show();e.addClass("weui-notification--in");e.data("params",m);var l=function(){if(g){clearTimeout(g);g=null}g=setTimeout(function(){if(e.hasClass("weui-notification--touching")){l()}else{d.closeNotification()}},m.time)};l()};d.closeNotification=function(){g&&clearTimeout(g);g=null;var l=d(".weui-notification").removeClass("weui-notification--in").transitionEnd(function(){d(this).remove()});if(l[0]){var m=d(".weui-notification").data("params");if(m&&m.onClose){m.onClose(m.data)}}};c=d.noti.prototype.defaults={title:undefined,text:undefined,media:undefined,time:4000,onClick:undefined,onClose:undefined,data:undefined,tpl:'<div class="weui-notification__inner">{{#if media}}<div class="weui-notification__media">{{media}}</div>{{/if}}<div class="weui-notification__content">{{#if title}}<div class="weui-notification__title">{{title}}</div>{{/if}}{{#if text}}<div class="weui-notification__text">{{text}}</div>{{/if}}</div><div class="weui-notification__handle-bar"></div></div>'}}($);+function(b){var a;b.toptip=function(f,e,d){if(!f){return}if(typeof e===typeof"a"){d=e;e=undefined}e=e||3000;var c=d?"bg-"+d:"bg-danger";var g=b(".weui-toptips").remove();g=b('<div class="weui-toptips"></div>').appendTo(document.body);g.html(f);g[0].className="weui-toptips "+c;clearTimeout(a);if(!g.hasClass("weui-toptips_visible")){g.show().width();g.addClass("weui-toptips_visible")}a=setTimeout(function(){b(".weui-toptips_visible").remove()},e)}}($);+function(d){var c=[];var e="swipeout-touching";var b=function(f){this.container=d(f);this.mover=this.container.find(">.weui-cell__bd");this.attachEvents();c.push(this)};b.prototype.touchStart=function(h){var g=d.getTouchPosition(h);this.container.addClass(e);this.start=g;this.startX=0;this.startTime=+new Date;var f=this.mover.css("transform").match(/-?[\d\.]+/g);if(f&&f.length){this.startX=parseInt(f[4])}this.diffX=this.diffY=0;this._closeOthers();this.limit=this.container.find(">.weui-cell__ft").width()||68};b.prototype.touchMove=function(h){if(!this.start){return true}var g=d.getTouchPosition(h);this.diffX=g.x-this.start.x;this.diffY=g.y-this.start.y;if(Math.abs(this.diffX)<Math.abs(this.diffY)){this.close();this.start=false;return true}h.preventDefault();h.stopPropagation();var f=this.diffX+this.startX;if(f>0){f=0}if(Math.abs(f)>this.limit){f=-(Math.pow(-(f+this.limit),0.7)+this.limit)}this.mover.css("transform","translate3d("+f+"px, 0, 0)")};b.prototype.touchEnd=function(){if(!this.start){return true}this.start=false;var f=this.diffX+this.startX;var g=new Date-this.startTime;if(this.diffX<-5&&g<200){this.open()}else{if(this.diffX>=0&&g<200){this.close()}else{if(f>0||-f<=this.limit/2){this.close()}else{this.open()}}}};b.prototype.close=function(){this.container.removeClass(e);this.mover.css("transform","translate3d(0, 0, 0)");this.container.trigger("swipeout-close")};b.prototype.open=function(){this.container.removeClass(e);this._closeOthers();this.mover.css("transform","translate3d("+(-this.limit)+"px, 0, 0)");this.container.trigger("swipeout-open")};b.prototype.attachEvents=function(){var f=this.mover;f.on(d.touchEvents.start,d.proxy(this.touchStart,this));f.on(d.touchEvents.move,d.proxy(this.touchMove,this));f.on(d.touchEvents.end,d.proxy(this.touchEnd,this))};b.prototype._closeOthers=function(){var f=this;c.forEach(function(g){if(g!==f){g.close()}})};var a=function(f){return new b(f)};d.fn.swipeout=function(f){return this.each(function(){var h=d(this);var g=h.data("swipeout")||a(this);h.data("swipeout",g);if(typeof f===typeof"a"){g[f]()}})};d(".weui-cell_swiped").swipeout()}($);(function(b){var a=b.fn.tab;b.fn.tab=function(d){d=b.extend({defaultIndex:0,activeClass:"weui-bar__item_on",onToggle:b.noop},d);const e=this.find(".weui-tabbar__item, .weui-navbar__item");const f=this.find(".weui-tab__content");this.toggle=function(g){const i=e.eq(g);i.addClass(d.activeClass).siblings().removeClass(d.activeClass);const h=f.eq(g);h.fadeIn().siblings().fadeOut();d.onToggle(g)};const c=this;this.on("click",".weui-tabbar__item, .weui-navbar__item",function(h){const g=b(this).index();c.toggle(g)});this.toggle(d.defaultIndex);return this};b.fn.tab.noConflict=function(){return a}})($);function share(){var a='<div class="weui-share" onclick="$(this).remove();">\n<div class="weui-share-box">\n点击右上角发送给指定朋友或分享到朋友圈 <i></i>\n</div>\n</div>';var a=$.t7.compile(a);$("body").append(a())}$(function(){var c=[];var b=[];c=$(".weixin");for(var a=0;a<c.length;a++){b[a]=c[a].src}$(".weixin").click(function(){var d=$(".weixin").index(this);wx.previewImage({current:b[d],urls:b})})});
