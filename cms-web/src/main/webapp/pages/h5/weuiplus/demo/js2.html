<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <link rel="stylesheet" href="../css/weui.css"/>
    <link rel="stylesheet" href="../css/weuix.css"/>

    <script src="../js/zepto.min.js"></script>
    <script src="../js/zepto.weui.js"></script>

</head>

<body ontouchstart>
<div class="container">
    <div class="page-hd">
        <h1 class="page-hd-title">
            ActionSheet
        </h1>
        <p class="page-hd-desc">操作表</p>
    </div>
    <div class="page-bd-15">
        <a href="javascript:;" id='show-actions' class="weui-btn weui-btn_primary">显示 ActionSheet</a>
        <a href="javascript:;" id='show-actions-bg' class="weui-btn weui-btn_primary">自定义背景色</a>

    </div>



    <br>
    <br>
    <div class="weui-footer weui-footer_fixed-bottom">
        <p class="weui-footer__links">
            <a href="../index.html" class="weui-footer__link">WeUI首页</a>
        </p>
        <p class="weui-footer__text">Copyright &copy; Yoby</p>
    </div>
</div>

<script>
    //有4个颜色配置
    $(document).on("click", "#show-actions", function() {
        $.actions({
            title: "选择操作",
            onClose: function() {
                console.log("close");
            },
            actions: [
                {
                    text: "发布",
                    className: "color-primary",
                    onClick: function() {
                        $.alert("发布成功");
                    }
                },
                {
                    text: "编辑",
                    className: "color-warning",
                    onClick: function() {
                        $.alert("你选择了“编辑”");
                    }
                },
                {
                    text: "删除",
                    className: 'color-danger',
                    onClick: function() {
                        $.alert("你选择了“删除”");
                    }
                }
            ]
        });
    });

    $(document).on("click", "#show-actions-bg", function() {
        $.actions({
            actions: [
                {
                    text: "发布",
                    className: "bg-primary",
                },
                {
                    text: "编辑",
                    className: "bg-warning",
                },
                {
                    text: "删除",
                    className: 'bg-danger',
                } ,{
                    text: "删除",
                    className: 'bg-success',
                }
            ]
        });
    });
</script>
</body>
</html>