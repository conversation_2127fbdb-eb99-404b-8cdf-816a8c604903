@layout("/h5/common/_pccontainer.html",{js:["/pages/h5/order/js/orderlist.js"]}){
@ include("/h5/car/carhead.html"){}
<br>
<div class="page__bd">
    <form action="/pc/daily/check/submit" method="post" id="dform">
        <a class="weui-cell weui-cell_access" href="/pc/orderDetail?guideOrderId=${pcOrderRsp.bizOrderGuide.id}">
            <div class="weui-cell__bd">
                <p>${pcOrderRsp.bizOrderGuide.orderNo}</p>
                <input id="orderNo" name="orderNo" class="weui-input" value="${pcOrderRsp.bizOrderGuide.orderNo}"  type="hidden"/>
                <input id="carId" name="carId" class="weui-input" value="${pcOrderRsp.bizOrderGuide.carId}"  type="hidden"/>
                <input id="orderGuideNo" name="orderGuideNo" value="${pcOrderRsp.bizOrderGuide.id}" type="hidden" />
            </div>
            <div class="weui-cell__ft">${pcOrderRsp.bizOrderCar.serviceTime,dateFormat="yyyy-MM-dd HH:mm"}
            </div>
        </a>

        <div class="weui-cell">
            <label class="weui-cell__bd">车辆名称</label>
            <span class="weui-cell__ft"> ${pcOrderRsp.bizOrderGuide.carName}</span>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__hd">
                <input class="weui-form-checkbox" id="allOk" checked value="1" name="allOk"  type="checkbox" required>
                <label for="allOk">
                    <i class="weui-icon-checkbox"></i>
                    <div class="weui-form-text" style="color: #000;"><p>所有检测项正常</p></div>
                </label>
            </div>
            @if(isNotEmpty(bizGuideDailyCheck)){
            <input id="id" name="id" class="weui-input" value="${bizGuideDailyCheck.id}"  type="hidden"/>
            <a class="weui-cell__bd weui-cell_access" href="/pc/daily/check/toedit?id=${bizGuideDailyCheck.id}">
                <div class="weui-cell__ft">修改</div>
            </a>
            @}
            @if(isEmpty(bizGuideDailyCheck)){
            <a class="weui-cell__bd weui-cell_access" href="/pc/daily/check/forward?guideOrderNo=${pcOrderRsp.bizOrderGuide.id}">
                <div class="weui-cell__ft">修改</div>
            </a>
            @}
        </div>
        <div class="weui-form__control-area">
            <div class="weui-cells__group weui-cells__group_form">
                <div class="weui-cells weui-cells_form">
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            @if(isNotEmpty(bizGuideDailyCheck)){
                            <textarea class="weui-textarea" id="remark" name="remark" value="${bizGuideDailyCheck.remark}" placeholder="备注" rows="3">${bizGuideDailyCheck.remark}</textarea>
                            @}
                            @if(isEmpty(bizGuideDailyCheck)){
                            <textarea class="weui-textarea" id="remark" name="remark"  placeholder="备注" rows="3"></textarea>
                            @}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="weui-form__opr-area" style="padding-bottom: 50px">
            <button class="weui-btn weui-btn_primary" type="button" id="showTooltips">保存</button>
        </div>
    </form>
</div>

<script>
    $(document).on("click", "#showTooltips", function() {
        var msg = "";
        if(!$("#carId").val() || $("#carId").val().length<2){
            msg = "车辆信息不能为空，请联系OP指派订单车辆！";
        }else{
            if(!$("#allOk").is(':checked')){
                msg = msg+"<br/>请填写所有选择项，或者进入详情页编辑！";
            }
        }

        if(msg==""){
            $("#dform").submit();
        }else{
            $.alert(msg);
        }
    });
</script>

@}
