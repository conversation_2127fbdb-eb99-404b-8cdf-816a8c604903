<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行摄天下 - 风景达人林景明</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
        }

        body {
            line-height: 1.8;
            color: #333;
            background: #f9fafb;
        }

        .header {
            background: linear-gradient(135deg, #2c5364, #203a43);
            color: white;
            padding: 6rem 2rem;
            text-align: center;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .section {
            margin: 4rem 0;
            background: white;
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .photo-card {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            transition: transform 0.3s;
        }

        .photo-card:hover {
            transform: translateY(-5px);
        }

        .photo-card img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 8px;
        }

        .caption {
            position: absolute;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            color: white;
            width: 100%;
            padding: 1rem;
            font-size: 0.9em;
        }

        h1 {
            font-size: 2.8rem;
            margin-bottom: 1rem;
        }

        h2 {
            color: #2c5364;
            border-left: 5px solid #fdc830;
            padding-left: 1rem;
            margin: 3rem 0;
        }

        .highlight {
            color: #fdc830;
            font-weight: bold;
        }

        blockquote {
            border-left: 4px solid #fdc830;
            margin: 2rem 0;
            padding: 1rem 2rem;
            background: #f8f9fa;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .header { padding: 4rem 1rem; }
            .section { padding: 2rem; }
        }
    </style>
</head>
<body>
<div class="header">
    <h1>林景明</h1>
    <p>用镜头对话自然的行者</p>
</div>

<div class="container">
    <!-- 人物简介 -->
    <div class="section">
        <img src="https://source.unsplash.com/800x600/?photographer,mountain"
             alt="林景明工作照" style="width:100%;border-radius:8px;margin-bottom:2rem">

        <h2>⛰️ 追光者自述</h2>
        <p>12年前，当我在<span class="highlight">四姑娘山</span>第一次用胶片机捕捉到云海漫过山脊的瞬间，便与风景摄影结下不解之缘。从金融分析师转型职业摄影师，累计行程38万公里，作品发表于《国家地理》等20余家国际刊物，获2022全球自然摄影大赛<span class="highlight">"年度生态摄影师"</span>称号。</p>

        <blockquote>
            "真正的风景不在取景框里，而在与自然共鸣的每一次呼吸间"
        </blockquote>
    </div>

    <!-- 代表作品 -->
    <div class="section">
        <h2>📸 经典作品</h2>
        <div class="photo-grid">
            <div class="photo-card">
                <img src="https://source.unsplash.com/600x900/?northernlights" alt="极光幻境">
                <div class="caption">2023年摄于挪威特罗姆瑟 - 极光与峡湾的量子纠缠</div>
            </div>
            <div class="photo-card">
                <img src="https://source.unsplash.com/600x900/?terrace,rice" alt="梯田晨韵">
                <div class="caption">元阳哈尼梯田 - 荣获IPA国际摄影奖人文类金奖</div>
            </div>
            <div class="photo-card">
                <img src="https://source.unsplash.com/600x900/?desert,sand" alt="大漠孤烟">
                <div class="caption">塔克拉玛干沙漠 - 国家地理2021年度精选</div>
            </div>
        </div>
    </div>

    <!-- 摄影哲学 -->
    <div class="section">
        <h2>🎞️ 创作哲学</h2>
        <p>在实践中独创<span class="highlight">"三度摄影法"</span>：</p>
        <ul style="margin:1.5rem 0;padding-left:2rem">
            <li><strong>温度</strong>：等待最佳环境湿度与设备温差，创造独特的雾化效果</li>
            <li><strong>角度</strong>：采用无人机与地面设备的多维联动拍摄</li>
            <li><strong>态度</strong>：每个拍摄地至少进行3次不同季节的深度回访</li>
        </ul>
        <img src="https://source.unsplash.com/800x600/?camera,tripod"
             alt="摄影器材" style="width:100%;border-radius:8px;margin-top:1rem">
    </div>

    <!-- 探险故事 -->
    <div class="section">
        <h2>🌍 秘境日记</h2>
        <h3>2023南极远征</h3>
        <p>历时28天的极地航行中，在<span class="highlight">零下42℃</span>的极端环境下，成功拍摄到帝企鹅群穿越冰裂缝的珍贵画面。为保护设备特制保温箱，每天需进行3小时设备除霜作业。</p>

        <div class="photo-grid">
            <div class="photo-card">
                <img src="https://source.unsplash.com/600x900/?penguin" alt="南极企鹅">
                <div class="caption">冰山上的帝企鹅家族</div>
            </div>
            <div class="photo-card">
                <img src="https://source.unsplash.com/600x900/?aurora,ice" alt="极光冰原">
                <div class="caption">南极光与冰晶的共舞</div>
            </div>
        </div>
    </div>

    <!-- 技术指南 -->
    <div class="section">
        <h2>🔭 行摄贴士</h2>
        <div style="columns:2;gap:3rem;margin:2rem 0">
            <div>
                <h3>黄金时刻掌控</h3>
                <p>利用Sun Surveyor App精确计算日出前<span class="highlight">"蓝色时刻"</span>，高原地区建议提前90分钟抵达机位</p>
            </div>
            <div>
                <h3>特殊天气应对</h3>
                <p>沙尘暴中拍摄需用<span class="highlight">医用止血绷带</span>缠绕设备缝隙，实测防尘效果优于专业防护罩</p>
            </div>
        </div>
    </div>
</div>
</body>
</html>
