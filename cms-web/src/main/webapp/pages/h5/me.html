
<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover">
    <title>服务列表</title>
    <link rel="stylesheet" href="/pages/h5ges/h5/weui-2.1.2/style/weui.css"/>
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
    <script src="https://res.wx.qq.com/open/libs/weuijs/1.2.1/weui.min.js"></script>

    <script src="/assets/common/libs/jquery/jquery-3.2.1.min.js"></script>
</head>
<body>
<div class="page">
    <div class="weui-form">
        <div class="weui-form__text-area">
            <img src="http://www.xiziwang.net/uploads/allimg/161228/737_161228102112_1.jpg" style="width: 310px;height: 170px"/>
        </div>
        <div class="weui-form__control-area">
            <div class="weui-cells__group weui-cells__group_form">
                <div class="weui-cells weui-cells_form">
                    <div class="weui-cell">
                        <div class="weui-cell__hd"><label class="weui-label">编号</label></div>
                        <div class="weui-cell__bd">
                            <input id="js_input" class="weui-input" placeholder="填写本人微信号" value="G000991"/>
                        </div>
                    </div>
                    <div class="weui-cell">
                        <div class="weui-cell__hd"><label class="weui-label">姓名</label></div>
                        <div class="weui-cell__bd">
                            <input id="js_input" class="weui-input" placeholder="填写本人微信号的昵称" value="王文义"/>
                        </div>
                    </div>
                    <div class="weui-cell">
                        <div class="weui-cell__hd"><label class="weui-label">联系电话</label></div>
                        <div class="weui-cell__bd">
                            <input id="js_input" class="weui-input" placeholder="填写绑定的电话号码" type="number" pattern="[0-9]*" value="1778982989"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="js_toast" style="display: none;">
        <div class="weui-mask_transparent"></div>
        <div class="weui-toast">
            <i class="weui-icon-success-no-circle weui-icon_toast"></i>
            <p class="weui-toast__content">已完成</p>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        var $toast = $('#js_toast');
        var $input = $('#js_input');
        $input.on('input', function () {
            if ($input.val()) {
                $('#showTooltips').removeClass('weui-btn_disabled');
            } else {
                $('#showTooltips').addClass('weui-btn_disabled');
            }
        });
        $('#showTooltips').on('click', function () {
            if ($(this).hasClass('weui-btn_disabled')) return;

            // toptips的fixed, 如果有`animation`, `position: fixed`不生效
            $('.page.cell').removeClass('slideIn');

            $toast.fadeIn(100);
            setTimeout(function () {
                $toast.fadeOut(100);
            }, 2000);
        });
    });
</script>
<div class="weui-tabbar tab-bottom" style="position: fixed;bottom: 0px;width: 100%">
    <a href="javascript:;" class="weui-tabbar__item weui-bar__item_on ">
                    <span style="display: inline-block;position: relative;">
                        <img src="/pages/h5ges/h5/weui-2.1.2/images/icon_tabbar.png" alt="" class="weui-tabbar__icon">
                        <span class="weui-badge" style="position: absolute;top: -2px;right: -13px;">8</span>
                    </span>

        <p class="weui-tabbar__label">首页</p>
    </a>
    <a href="javascript:;" class="weui-tabbar__item">
        <img src="/pages/h5ges/h5/weui-2.1.2/images/icon_tabbar.png" alt="" class="weui-tabbar__icon">
        <p class="weui-tabbar__label">服务订单</p>
    </a>
    <a href="javascript:;" class="weui-tabbar__item">
        <img src="/pages/h5ges/h5/weui-2.1.2/images/icon_tabbar.png" alt="" class="weui-tabbar__icon">
        <p class="weui-tabbar__label">我</p>
    </a>
</div>
</div>
</body>
<script type="text/javascript">
    $(function () {
        $('.weui-tabbar__item').on('click', function () {
            $(this).addClass('weui-bar__item_on').siblings('.weui-bar__item_on').removeClass('weui-bar__item_on');
        });
    });
</script>
</html>
