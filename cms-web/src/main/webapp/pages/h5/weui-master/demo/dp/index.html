<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <title>DPlayer视频播放器演示</title>
    <link rel="stylesheet" href="../../css/weui.css"/>
    <link rel="stylesheet" href="../../css/weuix.css"/>
    <script src="../../js/zepto.min.js"></script>
    <script>
        $(function(){
            $(".weui-c-like .icon").click(function(){
                if($(this).hasClass('on')) {
                    $(this).removeClass('on')
                }else{
                    $(this).addClass('on')
                }
            })

        });

    </script>
</head>
<body>
<div class="weui-content">
    <div class="weui-c-inner">
        <div class="weui-c-content">
            <h2 class="weui-c-title">Dplayer演示</h2>
            <div class="weui-c-meta">
                <span class="weui-c-nickname"><a href="http://dplayer.js.org/#/zh-Hans/">dplayer文档</a></span>
                <em class="weui-c-nickname">2018-10-10 10:10</em>
            </div>
            <div class="weui-c-article">
                <p>DPlayer是一个支持弹幕的 HTML5 视频播放器。支持 Bilibili 视频和 danmaku，支持HLS，FLV，MPEG DASH，WebTorrent(需要相应插件,默认支持mp4)以及其他视频格式，支持截屏、热键、切换清晰度以及字幕等</p>
        <link rel="stylesheet" href="DPlayer.min.css">
<div id="dplayer"></div>
<script src="DPlayer.min.js"></script>
		<script type="text/javascript">
	const dp = new DPlayer({
    container: document.getElementById('dplayer'),
    screenshot: true,
    autoplay:true,
    video: {
        url: './1.mp4',
        pic: '../../images/1.jpg',
        thumbnails: '../../images/1.jpg'
    }
});
		</script>
            </div>
        </div>
        <div class="weui-c-tools">
            <a href="javascript:;">阅读原文</a>
            <div class="weui-c-readnum">阅读<span id="readnum">10000+</span></div>
            <div class="weui-c-like">
                <i class="icon"></i>
                <span id="likenum">1000</span>
            </div>
        </div>
    </div>

</div>

</body>
</html>