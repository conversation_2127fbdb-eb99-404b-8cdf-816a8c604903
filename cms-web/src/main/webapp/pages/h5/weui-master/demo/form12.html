<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <link rel="stylesheet" href="../css/weui.css"/>
    <link rel="stylesheet" href="../css/weuix.css"/>

    <script src="../js/zepto.min.js"></script>

    <script src="../js/zepto.weui.js"></script>
    <script src="../js/lrz.min.js"></script>
    <script>

        $(function(){

            var tmpl = '<li class="weui-uploader__file" style="background-image:url(#url#)"></li>';
          var      $uploaderInput = $("#uploaderInput"); //上传按钮+
         var       $uploaderFiles = $("#uploaderFiles");    //图片列表
            var $galleryImg = $(".weui-gallery__img");//相册图片地址
            var $gallery = $(".weui-gallery");
            $uploaderInput.on("change", function(e){
                var src, url = window.URL || window.webkitURL || window.mozURL, files = e.target.files;
                for (var i = 0, len = files.length; i < len; ++i) {
                    var file = files[i];

                    if (url) {
                        src = url.createObjectURL(file);
                    } else {
                        src = e.target.result;
                    }

                    $uploaderFiles.append($(tmpl.replace('#url#', src)));
                }
            });
            $uploaderFiles.on("click", "li", function(){
                $galleryImg.attr("style", this.getAttribute("style"));
                console.log(this)
                $gallery.fadeIn(100);
            });
            $gallery.on("click", function(){
                $gallery.fadeOut(100);
            });

        });

    </script>
</head>

<body ontouchstart>
<div class="page-hd">
    <h1 class="page-hd-title">
        图片上传和预览压缩
    </h1>
    <p class="page-hd-desc">需要加载lrz.min.js压缩插件</p>
</div>

<div class="page-bd-15">
<div class="weui-uploader">
    <div class="weui-uploader__hd">
        <p class="weui-uploader__title">图片预览</p>
        <div class="weui-uploader__info">0/2</div>
    </div>
    <div class="weui-uploader__bd">
        <ul class="weui-uploader__files" id="uploaderFiles">
            <li class="weui-uploader__file" style="background-image:url(../images/1.jpg)"></li>
            <li class="weui-uploader__file" style="background-image:url(../images/2.jpg)"></li>
            <li class="weui-uploader__file" style="background-image:url(../images/3.jpg)"></li>
            <li class="weui-uploader__file weui-uploader__file_status" style="background-image:url(../images/1.jpg)">
                <div class="weui-uploader__file-content">
                    <i class="weui-icon-warn"></i>
                </div>
            </li>
            <li class="weui-uploader__file weui-uploader__file_status" style="background-image:url(../images/2.jpg)">
                <div class="weui-uploader__file-content">50%</div>
            </li>
        </ul>
        <div class="weui-uploader__input-box">
            <input id="uploaderInput" class="weui-uploader__input" accept="image/*" multiple="" type="file">
        </div>
    </div>
</div>

<div class="weui-gallery" style="display: none">
    <span class="weui-gallery__img"></span>
    <div class="weui-gallery__opr">
    </div>
</div>


    <div class="weui-uploader">
        <div class="weui-uploader__hd">
            <p class="weui-uploader__title">单图压缩上传,使用lrz压缩</p>
            <div class="weui-uploader__info">0/2</div>
        </div>
        <div class="weui-uploader__bd">
            <ul class="weui-uploader__files">
            </ul>
            <div class="weui-uploader__input-box">
                <input class="weui-uploader__input" accept="image/*" multiple="" type="file" onchange="uploadimg(this)">
            </div>
        </div>
        <script>
            function removeimg(obj){
                $.confirm('您确定要删除吗?', '确认删除?', function() {$(obj).remove();});
                return false;
            }
            function uploadimg(obj) {
                lrz(obj.files[0],{width:750,fieldName:"file"}).then(function(data) {
                    $.post("../php/upload.php",{imgbase64: data.base64},function(rs){
                        $(obj).parent().prev().html('<li onclick="removeimg(this)" class="weui-uploader__file" style="background-image:url(/php/'+rs.src+')"><input value="'+rs.src+'"  type="hidden"  name="file" /></li>');
                    },'json');
                 
                }).then(function(data) {

                }).catch(function(err) {
                    console.log(err);
                });
            }
        </script>
    </div>

    <div class="weui-uploader">
        <div class="weui-uploader__hd">
            <p class="weui-uploader__title">多图压缩上传,使用lrz压缩</p>
            <div class="weui-uploader__info">0/2</div>
        </div>
        <div class="weui-uploader__bd">
            <ul class="weui-uploader__files">
            </ul>
            <div class="weui-uploader__input-box">
                <input class="weui-uploader__input" accept="image/*" multiple="" type="file" onchange="uploadimgs(this)">
            </div>
        </div>
        <script>
            function removeimgs(obj){
                $.confirm('您确定要删除吗?', '确认删除?', function() {$(obj).remove();});
                return false;
            }
            function uploadimgs(obj) {
                var files = obj.files;
                var len = files.length;
                for (var i=0; i < len; i++) {
                    lrz(files[i], {width: 750, fieldName: "file"}).then(function (data) {
                        $.post("../php/upload.php", {imgbase64: data.base64}, function (rs) {
                            $(obj).parent().prev().append('<li onclick="removeimgs(this)" class="weui-uploader__file " style="background-image:url(/php/' + rs.src + ')"><input value="'+rs.src+'"  type="hidden"  name="files" /></li>');
                        }, 'json');

                    }).then(function (data) {

                    }).catch(function (err) {
                        console.log(err);
                    });
                }
            }
        </script>
    </div>


    <div class="weui-uploader">
        <div class="weui-uploader__hd">
            <p class="weui-uploader__title">单图上传,非压缩,通过POST上传</p>
            <div class="weui-uploader__info">0/2</div>
        </div>
        <div class="weui-uploader__bd">
            <ul class="weui-uploader__files">
            </ul>
            <div class="weui-uploader__input-box">
                <input class="weui-uploader__input" accept="image/*" multiple="" type="file"  onchange="previewImage(this)">
            </div>
        </div>
 <script>
     function previewImage(file) {
         var MAXWIDTH = 1000;
         var MAXHEIGHT = 1200;
         if (file.files && file.files[0]) {
             var reader = new FileReader();
             reader.onload = function (evt) {

                 $.post("../php/upload.php",{imgbase64: evt.target.result},function(rs){
                     $(file).parent().prev().html('<li  onclick="removeimg(this)" class="weui-uploader__file" style="background-image:url('+evt.target.result+')"><input value="'+rs.src+'"  type="hidden"  name="files" /></li>');
                 },'json');
             };
             reader.readAsDataURL(file.files[0]);
             console.log(file.files[0]);
         }
     }
 </script>
    </div>


    <div class="weui-uploader">
        <div class="weui-uploader__hd">
            <p class="weui-uploader__title">多图上传,非压缩,使用POST上传</p>
            <div class="weui-uploader__info">0/2</div>
        </div>
        <div class="weui-uploader__bd">
            <ul class="weui-uploader__files">

            </ul>
            <div class="weui-uploader__input-box">
                <input class="weui-uploader__input" accept="image/*" multiple="" type="file" onchange="previewImages(this)">
            </div>
        </div>
        <script>
            function previewImages(file) {
                var MAXWIDTH = 1000;
                var MAXHEIGHT = 1200;
                for (var i = 0; i < file.files.length; i++) {

                    if (file.files && file.files[i]) {
                        var reader = new FileReader();
                        reader.onload = function (evt) {
                            $.post("../php/upload.php",{imgbase64: evt.target.result},function(rs){
                                $(file).parent().prev().append('<li  onclick="removeimg(this)" class="weui-uploader__file" style="background-image:url('+evt.target.result+')"><input value="'+rs.src+'"  type="hidden"  name="files" /></li>');
                            },'json');
                        };
                        reader.readAsDataURL(file.files[i]);
                        console.log(file.files[i]);
                    }

                }
            }
        </script>
    </div>
    
</div>
<br>
<br>
<div class="weui-footer weui-footer_fixed-bottom">
    <p class="weui-footer__links">
        <a href="../index.html" class="weui-footer__link">WeUI首页</a>
    </p>
    <p class="weui-footer__text">Copyright &copy; Yoby</p>
</div>
</body>
</html>