<html>
<head>

    <title>Bytesize Icons - SVG Symbol</title>

    <style>

        body {
            color: #3f414e;
            font-family: "Avenir Next", Avenir, Segoe UI, "Helvetica Neue", helvetica, sans-serif;
            text-align: center;
            padding: 3em 0;
        }

        .i {
            stroke: currentColor;
            stroke-width: 2px;
            stroke-linecap: round;
            stroke-linejoin: round;
            fill: none;
            width: 1em;
            height: 1em;
            margin: 0.4em;
        }

        svg, svg symbol {
            overflow: visible;
        }

        div { margin: 1em }

        .i-large div { font-size: 4em; margin: 0 }
        .i-medium div { font-size: 2em; margin: 0 }
        .i-small div { font-size: 1.5em; margin: 0 }

        .i-large,
        .i-medium,
        .i-small { max-width: 50em; margin: 0 auto 2em }

        h1 {
            font-weight: normal;
            line-height: 1;
            font-size: 2em;
        }

        h3 {
            font-size: 1em;
            margin: 0 0 2em;
            font-weight: 600;
        }

        hr {
            border: 0 solid transparent;
            border-bottom: 5px solid #ddd;
            height: 0;
            max-width: 48em;
            margin: 4em auto;
        }

    </style>

</head>


<body>

    <!-- Include the SVG -->
 <svg xmlns="http://www.w3.org/2000/svg"><symbol id="i-search" viewBox="0 0 32 32"><circle cx="14" cy="14" r="12"/><path d="M23 23l7 7"/></symbol><symbol id="i-close" viewBox="0 0 32 32"><path d="M2 30L30 2m0 28L2 2"/></symbol><symbol id="i-plus" viewBox="0 0 32 32"><path d="M16 2v28M2 16h28"/></symbol><symbol id="i-minus" viewBox="0 0 32 32"><path d="M2 16h28"/></symbol><symbol id="i-play" viewBox="0 0 32 32"><path d="M10 2v28l14-14z"/></symbol><symbol id="i-pause" viewBox="0 0 32 32"><path d="M23 2v28M9 2v28"/></symbol><symbol id="i-backwards" viewBox="0 0 32 32"><path d="M16 2L2 16l14 14V16l14 14V2L16 16z"/></symbol><symbol id="i-forwards" viewBox="0 0 32 32"><path d="M16 2l14 14-14 14V16L2 30V2l14 14z"/></symbol><symbol id="i-move" viewBox="0 0 32 32"><path d="M3 16h26M16 3v26M12 7l4-4 4 4m-8 18l4 4 4-4m5-13l4 4-4 4M7 12l-4 4 4 4"/></symbol><symbol id="i-zoom-in" viewBox="0 0 32 32"><circle cx="14" cy="14" r="12"/><path d="M23 23l7 7"/><path d="M14 10v8m-4-4h8"/></symbol><symbol id="i-zoom-out" viewBox="0 0 32 32"><circle cx="14" cy="14" r="12"/><path d="M23 23l7 7"/><path d="M10 14h8"/></symbol><symbol id="i-zoom-reset" viewBox="0 0 32 32"><circle cx="14" cy="14" r="12"/><path d="M23 23l7 7"/><path d="M9 12V9h3m4 0h3v3M9 16v3h3m7-3v3h-3"/></symbol><symbol id="i-fullscreen" viewBox="0 0 32 32"><path d="M4 12V4h8m8 0h8v8M4 20v8h8m16-8v8h-8"/></symbol><symbol id="i-fullscreen-exit" viewBox="0 0 32 32"><path d="M4 12h8V4m8 0v8h8M4 20h8v8m16-8h-8v8"/></symbol><symbol id="i-star" viewBox="0 0 32 32"><path d="M16 2l4 10h10l-8 7 3 11-9-7-9 7 3-11-8-7h10z"/></symbol><symbol id="i-checkmark" viewBox="0 0 32 32"><path d="M2 20l10 8L30 4"/></symbol><symbol id="i-chevron-top" viewBox="0 0 32 32"><path d="M30 20L16 8 2 20"/></symbol><symbol id="i-chevron-right" viewBox="0 0 32 32"><path d="M12 30l12-14L12 2"/></symbol><symbol id="i-chevron-bottom" viewBox="0 0 32 32"><path d="M30 12L16 24 2 12"/></symbol><symbol id="i-chevron-left" viewBox="0 0 32 32"><path d="M20 30L8 16 20 2"/></symbol><symbol id="i-arrow-top" viewBox="0 0 32 32"><path d="M6 10l10-8 10 8M16 2v28"/></symbol><symbol id="i-arrow-right" viewBox="0 0 32 32"><path d="M22 6l8 10-8 10m8-10H2"/></symbol><symbol id="i-arrow-bottom" viewBox="0 0 32 32"><path d="M6 22l10 8 10-8m-10 8V2"/></symbol><symbol id="i-arrow-left" viewBox="0 0 32 32"><path d="M10 6L2 16l8 10M2 16h28"/></symbol><symbol id="i-caret-top" viewBox="0 0 32 32"><path d="M30 22L16 6 2 22z"/></symbol><symbol id="i-caret-right" viewBox="0 0 32 32"><path d="M10 30l16-14L10 2z"/></symbol><symbol id="i-caret-bottom" viewBox="0 0 32 32"><path d="M30 10L16 26 2 10z"/></symbol><symbol id="i-caret-left" viewBox="0 0 32 32"><path d="M22 30L6 16 22 2z"/></symbol><symbol id="i-start" viewBox="0 0 32 32"><path d="M8 2v14L22 2v28L8 16v14"/></symbol><symbol id="i-end" viewBox="0 0 32 32"><path d="M24 2v14L10 2v28l14-14v14"/></symbol><symbol id="i-eject" viewBox="0 0 32 32"><path d="M30 18L16 5 2 18zM2 25h28"/></symbol><symbol id="i-mute" viewBox="0 0 32 32"><path d="M20 16c0-8-5-14-5-14l-7 8H2v12h6l7 8s5-6 5-14z"/></symbol><symbol id="i-volume" viewBox="0 0 32 32"><path d="M20 16c0-8-5-14-5-14l-7 8H2v12h6l7 8s5-6 5-14zm1-14s4 4 4 14-4 14-4 14m6-26s3 4 3 12-3 12-3 12"/></symbol><symbol id="i-ban" viewBox="0 0 32 32"><circle cx="16" cy="16" r="14"/><path d="M6 6l20 20"/></symbol><symbol id="i-flag" viewBox="0 0 32 32"><path d="M6 2v28M6 6h20l-6 6 6 6H6"/></symbol><symbol id="i-options" viewBox="0 0 32 32"><path d="M28 6H4m24 10H4m24 10H4M24 3v6M8 13v6m12 4v6"/></symbol><symbol id="i-settings" viewBox="0 0 32 32"><path d="M13 2v4l-2 1-3-3-4 4 3 3-1 2H2v6h4l1 2-3 3 4 4 3-3 2 1v4h6v-4l2-1 3 3 4-4-3-3 1-2h4v-6h-4l-1-2 3-3-4-4-3 3-2-1V2z"/><circle cx="16" cy="16" r="4"/></symbol><symbol id="i-heart" viewBox="0 0 32 32"><path d="M4 16C1 12 2 6 7 4s8 2 9 4c1-2 5-6 10-4s5 8 2 12-12 12-12 12-9-8-12-12z"/></symbol><symbol id="i-clock" viewBox="0 0 32 32"><circle cx="16" cy="16" r="14"/><path d="M16 8v8l4 4"/></symbol><symbol id="i-menu" viewBox="0 0 32 32"><path d="M4 8h24M4 16h24M4 24h24"/></symbol><symbol id="i-msg" viewBox="0 0 32 32"><path d="M2 4h28v18H16l-8 7v-7H2z"/></symbol><symbol id="i-photo" viewBox="0 0 32 32"><path d="M20 24l-8-8L2 26V2h28v22m-14-4l6-6 8 8v8H2v-6"/><circle cx="10" cy="9" r="3"/></symbol><symbol id="i-camera" viewBox="0 0 32 32"><path d="M2 8h7l3-4h8l3 4h7v18H2z"/><circle cx="16" cy="16" r="5"/></symbol><symbol id="i-video" viewBox="0 0 32 32"><path d="M22 13l8-5v16l-8-5zM2 8v16h20V8z"/></symbol><symbol id="i-music" viewBox="0 0 32 32"><path d="M11 25V6l13-3v20M11 13l13-3"/><ellipse cx="7" cy="25" rx="4" ry="5"/><ellipse cx="20" cy="23" rx="4" ry="5"/></symbol><symbol id="i-mail" viewBox="0 0 32 32"><path d="M2 26h28V6H2zM2 6l14 10L30 6"/></symbol><symbol id="i-home" viewBox="0 0 32 32"><path d="M12 20v10H4V12L16 2l12 10v18h-8V20z"/></symbol><symbol id="i-user" viewBox="0 0 32 32"><path d="M22 11c0 5-3 9-6 9s-6-4-6-9 2-8 6-8 6 3 6 8zM4 30h24c0-9-6-10-12-10S4 21 4 30z"/></symbol><symbol id="i-signin" viewBox="0 0 32 32"><path d="M3 16h20m-8-8l8 8-8 8m6-20h8v24h-8"/></symbol><symbol id="i-signout" viewBox="0 0 32 32"><path d="M28 16H8m12-8l8 8-8 8m-9 4H3V4h8"/></symbol><symbol id="i-trash" viewBox="0 0 32 32"><path d="M28 6H6l2 24h16l2-24H4m12 6v12m5-12l-1 12m-9-12l1 12m0-18l1-4h6l1 4"/></symbol><symbol id="i-paperclip" viewBox="0 0 32 32"><path d="M10 9v15c0 4 3 6 6 6s6-2 6-6V6c0-3-2-4-4-4s-4 1-4 4v17c0 1 1 2 2 2s2-1 2-2V9"/></symbol><symbol id="i-file" viewBox="0 0 32 32"><path d="M6 2v28h20V10l-8-8zm12 0v8h8"/></symbol><symbol id="i-folder" viewBox="0 0 32 32"><path d="M2 26h28V7H14l-4-3H2zm28-14H2"/></symbol><symbol id="i-folder-open" viewBox="0 0 32 32"><path d="M4 28h24l2-16H14l-4-4H2zm24-16V4H4v4"/></symbol><symbol id="i-work" viewBox="0 0 32 32"><path d="M30 8H2v18h28zM20 8s0-4-4-4-4 4-4 4M8 26V8m16 18V8"/></symbol><symbol id="i-portfolio" viewBox="0 0 32 32"><path d="M29 17v11H3V17M2 8h28v8s-6 4-14 4-14-4-14-4V8zm14 14v-4m4-10s0-4-4-4-4 4-4 4"/></symbol><symbol id="i-eye" viewBox="0 0 32 32"><circle cx="17" cy="15" r="1"/><circle cx="16" cy="16" r="6"/><path d="M2 16S7 6 16 6s14 10 14 10-5 10-14 10S2 16 2 16z"/></symbol><symbol id="i-bookmark" viewBox="0 0 32 32"><path d="M6 2h20v28L16 20 6 30z"/></symbol><symbol id="i-tag" viewBox="0 0 32 32"><circle cx="24" cy="8" r="2"/><path d="M2 18L18 2h12v12L14 30z"/></symbol><symbol id="i-lightning" viewBox="0 0 32 32"><path d="M18 13l8-11L8 13l6 6-8 11 18-11z"/></symbol><symbol id="i-activity" viewBox="0 0 32 32"><path d="M4 16h7l3 13 4-26 3 13h7"/></symbol><symbol id="i-location" viewBox="0 0 32 32"><circle cx="16" cy="11" r="4"/><path d="M24 15c-3 7-8 15-8 15s-5-8-8-15 2-13 8-13 11 6 8 13z"/></symbol><symbol id="i-export" viewBox="0 0 32 32"><path d="M28 22v8H4v-8M16 4v20M8 12l8-8 8 8"/></symbol><symbol id="i-import" viewBox="0 0 32 32"><path d="M28 22v8H4v-8M16 4v20m-8-8l8 8 8-8"/></symbol><symbol id="i-inbox" viewBox="0 0 32 32"><path d="M2 15v10h28V15l-4-8H6zm0 0h8s1 5 6 5 6-5 6-5h8"/></symbol><symbol id="i-archive" viewBox="0 0 32 32"><path d="M4 10v18h24V10M2 4v6h28V4zm10 11h8"/></symbol><symbol id="i-reply" viewBox="0 0 32 32"><path d="M10 6l-7 8 7 8m-7-8h15c8 0 12 4 12 12"/></symbol><symbol id="i-edit" viewBox="0 0 32 32"><path d="M30 7l-5-5L5 22l-2 7 7-2zm-9-1l5 5zM5 22l5 5z"/></symbol><symbol id="i-compose" viewBox="0 0 32 32"><path d="M27 15v15H2V5h15m13 1l-4-4L9 19l-2 6 6-2zm-8 0l4 4zM9 19l4 4z"/></symbol><symbol id="i-upload" viewBox="0 0 32 32"><path d="M9 22c-9 1-8-10 0-9C6 2 23 2 22 10c10-3 10 13 1 12m-12-4l5-4 5 4m-5-4v15"/></symbol><symbol id="i-download" viewBox="0 0 32 32"><path d="M9 22c-9 1-8-10 0-9C6 2 23 2 22 10c10-3 10 13 1 12m-12 4l5 4 5-4m-5-10v14"/></symbol><symbol id="i-send" viewBox="0 0 32 32"><path d="M2 16L30 2 16 30l-4-10zM30 2L12 20"/></symbol><symbol id="i-link" viewBox="0 0 32 32"><path d="M18 8s6-6 9-3 2 7-3 11-8 5-10 1m0 7s-6 6-9 3-2-7 3-11 8-5 10-1"/></symbol><symbol id="i-code" viewBox="0 0 32 32"><path d="M10 9l-7 8 7 8M22 9l7 8-7 8M18 7l-4 20"/></symbol><symbol id="i-lock" viewBox="0 0 32 32"><path d="M5 15v15h22V15zm4 0C9 9 9 5 16 5s7 4 7 10m-7 5v3"/><circle cx="16" cy="24" r="1"/></symbol><symbol id="i-unlock" viewBox="0 0 32 32"><path d="M5 15v15h22V15zm4 0C9 7 9 3 16 3s7 5 7 6m-7 11v3"/><circle cx="16" cy="24" r="1"/></symbol><symbol id="i-bell" viewBox="0 0 32 32"><path d="M8 17c0-5 1-11 8-11s8 6 8 11 3 8 3 8H5s3-3 3-8zm12 8s0 4-4 4-4-4-4-4m4-22v3"/></symbol><symbol id="i-book" viewBox="0 0 32 32"><path d="M16 7S9 1 2 6v22c7-5 14 0 14 0s7-5 14 0V6c-7-5-14 1-14 1zm0 0v21"/></symbol><symbol id="i-calendar" viewBox="0 0 32 32"><path d="M2 6v24h28V6zm0 9h28M7 3v6m6-6v6m6-6v6m6-6v6"/></symbol><symbol id="i-print" viewBox="0 0 32 32"><path d="M7 25H2V9h28v16h-5M7 19v11h18V19zM25 9V2H7v7m15 5h3"/></symbol><symbol id="i-alert" viewBox="0 0 32 32"><path d="M16 3l14 26H2zm0 8v8m0 4v2"/></symbol><symbol id="i-info" viewBox="0 0 32 32"><path d="M16 14v9m0-15v2"/><circle cx="16" cy="16" r="14"/></symbol><symbol id="i-creditcard" viewBox="0 0 32 32"><path d="M2 7v18h28V7zm3 11h4m-4 3h6"/><path d="M2 11v2h28v-2z" fill="currentColor"/></symbol><symbol id="i-cart" viewBox="0 0 32 32"><path d="M6 6h24l-3 13H9m18 4H10L5 2H2"/><circle cx="25" cy="27" r="2"/><circle cx="12" cy="27" r="2"/></symbol><symbol id="i-bag" viewBox="0 0 32 32"><path d="M5 9v20h22V9zm5 0s0-6 6-6 6 6 6 6"/></symbol><symbol id="i-gift" viewBox="0 0 32 32"><path d="M4 14v16h24V14M2 9v5h28V9zm14 0s-2-9-8-6 8 6 8 6 2-9 8-6-8 6-8 6m0 0v21"/></symbol><symbol id="i-external" viewBox="0 0 32 32"><path d="M14 9H3v20h20V18M18 4h10v10m0-10L14 18"/></symbol><symbol id="i-reload" viewBox="0 0 32 32"><path d="M29 16c0 6-5 13-13 13S3 22 3 16 8 3 16 3c5 0 9 3 11 6m-7 1l7-1 1-7"/></symbol><symbol id="i-clipboard" viewBox="0 0 32 32"><path d="M12 2v4h8V2h-8zm-1 2H6v26h20V4h-5"/></symbol><symbol id="i-filter" viewBox="0 0 32 32"><path d="M2 5s4-2 14-2 14 2 14 2L19 18v9l-6 3V18L2 5z"/></symbol><symbol id="i-feed" viewBox="0 0 32 32"><circle cx="6" cy="26" r="2" fill="currentColor"/><path d="M4 15c7 0 13 6 13 13M4 6c13 0 22 9 22 22"/></symbol><symbol id="i-moon" viewBox="0 0 32 32"><path d="M14 2C9 2 3 7 3 15s6 14 14 14 13-6 13-11C19 25 7 13 14 2z"/></symbol><symbol id="i-microphone" viewBox="0 0 32 32"><path d="M16 2c-4 0-4 4-4 4v10s0 4 4 4 4-4 4-4V6s0-4-4-4zM8 17s0 7 8 7 8-7 8-7M13 29h6m-3-5v5"/></symbol><symbol id="i-telephone" viewBox="0 0 32 32"><path d="M3 12c0-7 7-7 13-7s13 0 13 7c0 8-7-1-7-1H10s-7 9-7 1zm8 2s-5 5-5 14h20c0-9-5-14-5-14H11z"/><circle cx="16" cy="21" r="4"/></symbol><symbol id="i-desktop" viewBox="0 0 32 32"><path d="M10 29s0-5 6-5 6 5 6 5H10zM2 6v17h28V6H2z"/></symbol><symbol id="i-mobile" viewBox="0 0 32 32"><path d="M21 2H11c-1 0-2 1-2 2v24c0 1 1 2 2 2h10c1 0 2-1 2-2V4c0-1-1-2-2-2zM9 5h14M9 27h14"/></symbol><symbol id="i-ellipsis-horizontal" viewBox="0 0 32 32"><circle cx="7" cy="16" r="2"/><circle cx="16" cy="16" r="2"/><circle cx="25" cy="16" r="2"/></symbol><symbol id="i-ellipsis-vertical" viewBox="0 0 32 32"><circle cx="16" cy="7" r="2"/><circle cx="16" cy="16" r="2"/><circle cx="16" cy="25" r="2"/></symbol><symbol id="i-twitter" viewBox="0 0 64 64"><path stroke-width="0" fill="currentColor" d="M60 16l-6 1 4-5-7 2c-9-10-23 1-19 10C16 24 8 12 8 12s-6 9 4 16l-6-2c0 6 4 10 11 12h-7c4 8 11 8 11 8s-6 5-17 5c33 16 53-14 50-30z"/></symbol><symbol id="i-github" viewBox="0 0 64 64"><path stroke-width="0" fill="currentColor" d="M32 0C14 0 0 14 0 32c0 21 19 30 22 30 2 0 2-1 2-2v-5c-7 2-10-2-11-5 0 0 0-1-2-3-1-1-5-3-1-3 3 0 5 4 5 4 3 4 7 3 9 2 0-2 2-4 2-4-8-1-14-4-14-15 0-4 1-7 3-9 0 0-2-4 0-9 0 0 5 0 9 4 3-2 13-2 16 0 4-4 9-4 9-4 2 7 0 9 0 9 2 2 3 5 3 9 0 11-7 14-14 15 1 1 2 3 2 6v8c0 1 0 2 2 2 3 0 22-9 22-30C64 14 50 0 32 0z"/></symbol></svg>     
    <div class="header">
        <h1>Bytesize Icons</h1>
        <h3>A tiny style-controlled SVG iconset</h3>
    </div>

    <hr />

    <div class="i-large">

        <h4>SVG Symbol - 64px</h4>

        <div>
        <svg style="color:red" class="i"><use xlink:href="#i-search" /></svg>
        <svg class="i"><use xlink:href="#i-close" /></svg>
        <svg class="i"><use xlink:href="#i-plus" /></svg>
        <svg class="i"><use xlink:href="#i-minus" /></svg>
        <svg class="i"><use xlink:href="#i-play" /></svg>
        <svg class="i"><use xlink:href="#i-pause" /></svg>
        <svg class="i"><use xlink:href="#i-backwards" /></svg>
        <svg class="i"><use xlink:href="#i-forwards" /></svg>
        <svg class="i"><use xlink:href="#i-move" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-in" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-out" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-reset" /></svg>
        <svg class="i"><use xlink:href="#i-fullscreen" /></svg>
        <svg class="i"><use xlink:href="#i-fullscreen-exit" /></svg>
        <svg class="i"><use xlink:href="#i-star" /></svg>
        <svg class="i"><use xlink:href="#i-checkmark" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-top" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-right" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-left" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-top" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-right" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-left" /></svg>
        <svg class="i"><use xlink:href="#i-caret-top" /></svg>
        <svg class="i"><use xlink:href="#i-caret-right" /></svg>
        <svg class="i"><use xlink:href="#i-caret-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-caret-left" /></svg>
        <svg class="i"><use xlink:href="#i-start" /></svg>
        <svg class="i"><use xlink:href="#i-end" /></svg>
        <svg class="i"><use xlink:href="#i-eject" /></svg>
        <svg class="i"><use xlink:href="#i-mute" /></svg>
        <svg class="i"><use xlink:href="#i-volume" /></svg>
        <svg class="i"><use xlink:href="#i-ban" /></svg>
        <svg class="i"><use xlink:href="#i-flag" /></svg>
        <svg class="i"><use xlink:href="#i-options" /></svg>
        <svg class="i"><use xlink:href="#i-settings" /></svg>
        <svg class="i"><use xlink:href="#i-heart" /></svg>
        <svg class="i"><use xlink:href="#i-clock" /></svg>
        <svg class="i"><use xlink:href="#i-menu" /></svg>
        <svg class="i"><use xlink:href="#i-msg" /></svg>
        <svg class="i"><use xlink:href="#i-photo" /></svg>
        <svg class="i"><use xlink:href="#i-camera" /></svg>
        <svg class="i"><use xlink:href="#i-video" /></svg>
        <svg class="i"><use xlink:href="#i-music" /></svg>
        <svg class="i"><use xlink:href="#i-mail" /></svg>
        <svg class="i"><use xlink:href="#i-home" /></svg>
        <svg class="i"><use xlink:href="#i-user" /></svg>
        <svg class="i"><use xlink:href="#i-signin" /></svg>
        <svg class="i"><use xlink:href="#i-signout" /></svg>
        <svg class="i"><use xlink:href="#i-trash" /></svg>
        <svg class="i"><use xlink:href="#i-paperclip" /></svg>
        <svg class="i"><use xlink:href="#i-file" /></svg>
        <svg class="i"><use xlink:href="#i-folder" /></svg>
        <svg class="i"><use xlink:href="#i-folder-open" /></svg>
        <svg class="i"><use xlink:href="#i-work" /></svg>
        <svg class="i"><use xlink:href="#i-portfolio" /></svg>
        <svg class="i"><use xlink:href="#i-eye" /></svg>
        <svg class="i"><use xlink:href="#i-bookmark" /></svg>
        <svg class="i"><use xlink:href="#i-tag" /></svg>
        <svg class="i"><use xlink:href="#i-lightning" /></svg>
        <svg class="i"><use xlink:href="#i-location" /></svg>
        <svg class="i"><use xlink:href="#i-activity" /></svg>
        <svg class="i"><use xlink:href="#i-export" /></svg>
        <svg class="i"><use xlink:href="#i-import" /></svg>
        <svg class="i"><use xlink:href="#i-inbox" /></svg>
        <svg class="i"><use xlink:href="#i-archive" /></svg>
        <svg class="i"><use xlink:href="#i-reply" /></svg>
        <svg class="i"><use xlink:href="#i-edit" /></svg>
        <svg class="i"><use xlink:href="#i-compose" /></svg>
        <svg class="i"><use xlink:href="#i-upload" /></svg>
        <svg class="i"><use xlink:href="#i-download" /></svg>
        <svg class="i"><use xlink:href="#i-send" /></svg>
        <svg class="i"><use xlink:href="#i-link" /></svg>
        <svg class="i"><use xlink:href="#i-code" /></svg>
        <svg class="i"><use xlink:href="#i-lock" /></svg>
        <svg class="i"><use xlink:href="#i-unlock" /></svg>
        <svg class="i"><use xlink:href="#i-bell" /></svg>
        <svg class="i"><use xlink:href="#i-book" /></svg>
        <svg class="i"><use xlink:href="#i-calendar" /></svg>
        <svg class="i"><use xlink:href="#i-print" /></svg>
        <svg class="i"><use xlink:href="#i-alert" /></svg>
        <svg class="i"><use xlink:href="#i-info" /></svg>
        <svg class="i"><use xlink:href="#i-creditcard" /></svg>
        <svg class="i"><use xlink:href="#i-cart" /></svg>
        <svg class="i"><use xlink:href="#i-bag" /></svg>
        <svg class="i"><use xlink:href="#i-gift" /></svg>
        <svg class="i"><use xlink:href="#i-external" /></svg>
        <svg class="i"><use xlink:href="#i-reload" /></svg>
        <svg class="i"><use xlink:href="#i-clipboard" /></svg>
        <svg class="i"><use xlink:href="#i-filter" /></svg>
        <svg class="i"><use xlink:href="#i-feed" /></svg>
        <svg class="i"><use xlink:href="#i-moon" /></svg>
        <svg class="i"><use xlink:href="#i-microphone" /></svg>
        <svg class="i"><use xlink:href="#i-telephone" /></svg>
        <svg class="i"><use xlink:href="#i-desktop" /></svg>
        <svg class="i"><use xlink:href="#i-mobile" /></svg>
        <svg class="i"><use xlink:href="#i-ellipsis-horizontal" /></svg>
        <svg class="i"><use xlink:href="#i-ellipsis-vertical" /></svg>
        <svg class="i"><use xlink:href="#i-twitter" /></svg>
        <svg class="i"><use xlink:href="#i-github" /></svg>
        </div>

    </div>

    <hr />

    <div class="i-medium">

        <h4>SVG Symbol - 32px</h4>

        <div>
        <svg class="i"><use xlink:href="#i-search" /></svg>
        <svg class="i"><use xlink:href="#i-close" /></svg>
        <svg class="i"><use xlink:href="#i-plus" /></svg>
        <svg class="i"><use xlink:href="#i-minus" /></svg>
        <svg class="i"><use xlink:href="#i-play" /></svg>
        <svg class="i"><use xlink:href="#i-pause" /></svg>
        <svg class="i"><use xlink:href="#i-backwards" /></svg>
        <svg class="i"><use xlink:href="#i-forwards" /></svg>
        <svg class="i"><use xlink:href="#i-move" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-in" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-out" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-reset" /></svg>
        <svg class="i"><use xlink:href="#i-fullscreen" /></svg>
        <svg class="i"><use xlink:href="#i-fullscreen-exit" /></svg>
        <svg class="i"><use xlink:href="#i-star" /></svg>
        <svg class="i"><use xlink:href="#i-checkmark" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-top" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-right" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-left" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-top" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-right" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-left" /></svg>
        <svg class="i"><use xlink:href="#i-caret-top" /></svg>
        <svg class="i"><use xlink:href="#i-caret-right" /></svg>
        <svg class="i"><use xlink:href="#i-caret-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-caret-left" /></svg>
        <svg class="i"><use xlink:href="#i-start" /></svg>
        <svg class="i"><use xlink:href="#i-end" /></svg>
        <svg class="i"><use xlink:href="#i-eject" /></svg>
        <svg class="i"><use xlink:href="#i-mute" /></svg>
        <svg class="i"><use xlink:href="#i-volume" /></svg>
        <svg class="i"><use xlink:href="#i-ban" /></svg>
        <svg class="i"><use xlink:href="#i-flag" /></svg>
        <svg class="i"><use xlink:href="#i-options" /></svg>
        <svg class="i"><use xlink:href="#i-settings" /></svg>
        <svg class="i"><use xlink:href="#i-heart" /></svg>
        <svg class="i"><use xlink:href="#i-clock" /></svg>
        <svg class="i"><use xlink:href="#i-menu" /></svg>
        <svg class="i"><use xlink:href="#i-msg" /></svg>
        <svg class="i"><use xlink:href="#i-photo" /></svg>
        <svg class="i"><use xlink:href="#i-camera" /></svg>
        <svg class="i"><use xlink:href="#i-video" /></svg>
        <svg class="i"><use xlink:href="#i-music" /></svg>
        <svg class="i"><use xlink:href="#i-mail" /></svg>
        <svg class="i"><use xlink:href="#i-home" /></svg>
        <svg class="i"><use xlink:href="#i-user" /></svg>
        <svg class="i"><use xlink:href="#i-signin" /></svg>
        <svg class="i"><use xlink:href="#i-signout" /></svg>
        <svg class="i"><use xlink:href="#i-trash" /></svg>
        <svg class="i"><use xlink:href="#i-paperclip" /></svg>
        <svg class="i"><use xlink:href="#i-file" /></svg>
        <svg class="i"><use xlink:href="#i-folder" /></svg>
        <svg class="i"><use xlink:href="#i-folder-open" /></svg>
        <svg class="i"><use xlink:href="#i-work" /></svg>
        <svg class="i"><use xlink:href="#i-portfolio" /></svg>
        <svg class="i"><use xlink:href="#i-eye" /></svg>
        <svg class="i"><use xlink:href="#i-bookmark" /></svg>
        <svg class="i"><use xlink:href="#i-tag" /></svg>
        <svg class="i"><use xlink:href="#i-lightning" /></svg>
        <svg class="i"><use xlink:href="#i-location" /></svg>
        <svg class="i"><use xlink:href="#i-activity" /></svg>
        <svg class="i"><use xlink:href="#i-export" /></svg>
        <svg class="i"><use xlink:href="#i-import" /></svg>
        <svg class="i"><use xlink:href="#i-inbox" /></svg>
        <svg class="i"><use xlink:href="#i-archive" /></svg>
        <svg class="i"><use xlink:href="#i-reply" /></svg>
        <svg class="i"><use xlink:href="#i-edit" /></svg>
        <svg class="i"><use xlink:href="#i-compose" /></svg>
        <svg class="i"><use xlink:href="#i-upload" /></svg>
        <svg class="i"><use xlink:href="#i-download" /></svg>
        <svg class="i"><use xlink:href="#i-send" /></svg>
        <svg class="i"><use xlink:href="#i-link" /></svg>
        <svg class="i"><use xlink:href="#i-code" /></svg>
        <svg class="i"><use xlink:href="#i-lock" /></svg>
        <svg class="i"><use xlink:href="#i-unlock" /></svg>
        <svg class="i"><use xlink:href="#i-bell" /></svg>
        <svg class="i"><use xlink:href="#i-book" /></svg>
        <svg class="i"><use xlink:href="#i-calendar" /></svg>
        <svg class="i"><use xlink:href="#i-print" /></svg>
        <svg class="i"><use xlink:href="#i-alert" /></svg>
        <svg class="i"><use xlink:href="#i-info" /></svg>
        <svg class="i"><use xlink:href="#i-creditcard" /></svg>
        <svg class="i"><use xlink:href="#i-cart" /></svg>
        <svg class="i"><use xlink:href="#i-bag" /></svg>
        <svg class="i"><use xlink:href="#i-gift" /></svg>
        <svg class="i"><use xlink:href="#i-external" /></svg>
        <svg class="i"><use xlink:href="#i-reload" /></svg>
        <svg class="i"><use xlink:href="#i-clipboard" /></svg>
        <svg class="i"><use xlink:href="#i-filter" /></svg>
        <svg class="i"><use xlink:href="#i-feed" /></svg>
        <svg class="i"><use xlink:href="#i-moon" /></svg>
        <svg class="i"><use xlink:href="#i-microphone" /></svg>
        <svg class="i"><use xlink:href="#i-telephone" /></svg>
        <svg class="i"><use xlink:href="#i-desktop" /></svg>
        <svg class="i"><use xlink:href="#i-mobile" /></svg>
        <svg class="i"><use xlink:href="#i-ellipsis-horizontal" /></svg>
        <svg class="i"><use xlink:href="#i-ellipsis-vertical" /></svg>
        <svg class="i"><use xlink:href="#i-twitter" /></svg>
        <svg class="i"><use xlink:href="#i-github" /></svg>
        </div>

    </div>

    <hr />

    <div class="i-small">

        <h4>SVG Symbol - 24px</h4>

        <div>
        <svg class="i"><use xlink:href="#i-search" /></svg>
        <svg class="i"><use xlink:href="#i-close" /></svg>
        <svg class="i"><use xlink:href="#i-plus" /></svg>
        <svg class="i"><use xlink:href="#i-minus" /></svg>
        <svg class="i"><use xlink:href="#i-play" /></svg>
        <svg class="i"><use xlink:href="#i-pause" /></svg>
        <svg class="i"><use xlink:href="#i-backwards" /></svg>
        <svg class="i"><use xlink:href="#i-forwards" /></svg>
        <svg class="i"><use xlink:href="#i-move" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-in" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-out" /></svg>
        <svg class="i"><use xlink:href="#i-zoom-reset" /></svg>
        <svg class="i"><use xlink:href="#i-fullscreen" /></svg>
        <svg class="i"><use xlink:href="#i-fullscreen-exit" /></svg>
        <svg class="i"><use xlink:href="#i-star" /></svg>
        <svg class="i"><use xlink:href="#i-checkmark" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-top" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-right" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-chevron-left" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-top" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-right" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-arrow-left" /></svg>
        <svg class="i"><use xlink:href="#i-caret-top" /></svg>
        <svg class="i"><use xlink:href="#i-caret-right" /></svg>
        <svg class="i"><use xlink:href="#i-caret-bottom" /></svg>
        <svg class="i"><use xlink:href="#i-caret-left" /></svg>
        <svg class="i"><use xlink:href="#i-start" /></svg>
        <svg class="i"><use xlink:href="#i-end" /></svg>
        <svg class="i"><use xlink:href="#i-eject" /></svg>
        <svg class="i"><use xlink:href="#i-mute" /></svg>
        <svg class="i"><use xlink:href="#i-volume" /></svg>
        <svg class="i"><use xlink:href="#i-ban" /></svg>
        <svg class="i"><use xlink:href="#i-flag" /></svg>
        <svg class="i"><use xlink:href="#i-options" /></svg>
        <svg class="i"><use xlink:href="#i-settings" /></svg>
        <svg class="i"><use xlink:href="#i-heart" /></svg>
        <svg class="i"><use xlink:href="#i-clock" /></svg>
        <svg class="i"><use xlink:href="#i-menu" /></svg>
        <svg class="i"><use xlink:href="#i-msg" /></svg>
        <svg class="i"><use xlink:href="#i-photo" /></svg>
        <svg class="i"><use xlink:href="#i-camera" /></svg>
        <svg class="i"><use xlink:href="#i-video" /></svg>
        <svg class="i"><use xlink:href="#i-music" /></svg>
        <svg class="i"><use xlink:href="#i-mail" /></svg>
        <svg class="i"><use xlink:href="#i-home" /></svg>
        <svg class="i"><use xlink:href="#i-user" /></svg>
        <svg class="i"><use xlink:href="#i-signin" /></svg>
        <svg class="i"><use xlink:href="#i-signout" /></svg>
        <svg class="i"><use xlink:href="#i-trash" /></svg>
        <svg class="i"><use xlink:href="#i-paperclip" /></svg>
        <svg class="i"><use xlink:href="#i-file" /></svg>
        <svg class="i"><use xlink:href="#i-folder" /></svg>
        <svg class="i"><use xlink:href="#i-folder-open" /></svg>
        <svg class="i"><use xlink:href="#i-work" /></svg>
        <svg class="i"><use xlink:href="#i-portfolio" /></svg>
        <svg class="i"><use xlink:href="#i-eye" /></svg>
        <svg class="i"><use xlink:href="#i-bookmark" /></svg>
        <svg class="i"><use xlink:href="#i-tag" /></svg>
        <svg class="i"><use xlink:href="#i-lightning" /></svg>
        <svg class="i"><use xlink:href="#i-location" /></svg>
        <svg class="i"><use xlink:href="#i-activity" /></svg>
        <svg class="i"><use xlink:href="#i-export" /></svg>
        <svg class="i"><use xlink:href="#i-import" /></svg>
        <svg class="i"><use xlink:href="#i-inbox" /></svg>
        <svg class="i"><use xlink:href="#i-archive" /></svg>
        <svg class="i"><use xlink:href="#i-reply" /></svg>
        <svg class="i"><use xlink:href="#i-edit" /></svg>
        <svg class="i"><use xlink:href="#i-compose" /></svg>
        <svg class="i"><use xlink:href="#i-upload" /></svg>
        <svg class="i"><use xlink:href="#i-download" /></svg>
        <svg class="i"><use xlink:href="#i-send" /></svg>
        <svg class="i"><use xlink:href="#i-link" /></svg>
        <svg class="i"><use xlink:href="#i-code" /></svg>
        <svg class="i"><use xlink:href="#i-lock" /></svg>
        <svg class="i"><use xlink:href="#i-unlock" /></svg>
        <svg class="i"><use xlink:href="#i-bell" /></svg>
        <svg class="i"><use xlink:href="#i-book" /></svg>
        <svg class="i"><use xlink:href="#i-calendar" /></svg>
        <svg class="i"><use xlink:href="#i-print" /></svg>
        <svg class="i"><use xlink:href="#i-alert" /></svg>
        <svg class="i"><use xlink:href="#i-info" /></svg>
        <svg class="i"><use xlink:href="#i-creditcard" /></svg>
        <svg class="i"><use xlink:href="#i-cart" /></svg>
        <svg class="i"><use xlink:href="#i-bag" /></svg>
        <svg class="i"><use xlink:href="#i-gift" /></svg>
        <svg class="i"><use xlink:href="#i-external" /></svg>
        <svg class="i"><use xlink:href="#i-reload" /></svg>
        <svg class="i"><use xlink:href="#i-clipboard" /></svg>
        <svg class="i"><use xlink:href="#i-filter" /></svg>
        <svg class="i"><use xlink:href="#i-feed" /></svg>
        <svg class="i"><use xlink:href="#i-moon" /></svg>
        <svg class="i"><use xlink:href="#i-microphone" /></svg>
        <svg class="i"><use xlink:href="#i-telephone" /></svg>
        <svg class="i"><use xlink:href="#i-desktop" /></svg>
        <svg class="i"><use xlink:href="#i-mobile" /></svg>
        <svg class="i"><use xlink:href="#i-ellipsis-horizontal" /></svg>
        <svg class="i"><use xlink:href="#i-ellipsis-vertical" /></svg>
        <svg class="i"><use xlink:href="#i-twitter" /></svg>
        <svg class="i"><use xlink:href="#i-github" /></svg>
        </div>

    </div>

</body>
</html>