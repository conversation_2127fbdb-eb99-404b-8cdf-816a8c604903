<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0,viewport-fit=cover">
    <title>${title!""}</title>
    <link rel="stylesheet" href="${ctxPath}/pages/h5/weui-master/css/weui.css"/>
    <link rel="stylesheet" href="${ctxPath}/pages/h5/weui-master/css/weuix.css"/>

    <script src="${ctxPath}/pages/h5/weui-master/js/zepto.min.js"></script>
    <script src="${ctxPath}/pages/h5/weui-master/js/zepto.weui.js"></script>
    <script src="${ctxPath}/pages/h5/js/h5common.js"></script>
    <script src="${ctxPath}/pages/h5/weui-master/js/iscroll-lite.js"></script>
    <script src="${ctxPath}/pages/h5/weui-master/js/fn.js"></script>
    <script src="${ctxPath}/pages/h5/weui-master/js/php.js"></script>
    <script type="text/javascript" src="${ctxPath}/assets/common/layui/layui.js"></script>
    <script type="text/javascript" src="${ctxPath}/pages/h5/common/common.js"></script>
    <script type="text/javascript">
        $(function () {
            //todo 修改服务订单 defaultIndex
            var indexNum = 0;
            var locationUrl = window.location.pathname;


            if (locationUrl.indexOf("order") >= 0) {
                indexNum = 0;
            } else if (locationUrl.indexOf("daily") >= 0) {
                indexNum = 1;
            } else {
                indexNum = 2;
            }

            $('#buttom_weui').tab({
                defaultIndex: indexNum,
                activeClass: 'weui-bar__item_on',
                onToggle: function (index) {
                    // if (index == 0) {
                    //     window.location.href = "/pc/orders";
                    // } else if (index == 1) {
                    //     window.location.href = "/pc/orders";
                    // }
                }
            })

            var dlist = $("[required]")
            dlist.each(function(){
                var $required = $("<span style=\"color:red;width:3px;display: inline-block;float: left\">* </span>");    //创建元素
                $(this).parent().parent().append($required);    //追加到文档中，注意parent()方法的使用
            })
        })
        layui.config({
            base: '${ctxPath}/assets/common/module/'
        }).extend({
            formSelects: 'formSelects/formSelects-v4',
            treetable: 'treetable-lay/treetable',
            dropdown: 'dropdown/dropdown',
            notice: 'notice/notice',
            step: 'step-lay/step',
            dtree: 'dtree/dtree',
            citypicker: 'city-picker/city-picker',
            tableSelect: 'tableSelect/tableSelect',
            selectPlus: '../../expand/module/selectPlus/selectPlus',
            ax: '../../expand/module/ax/ax',
            ztree: '../../expand/module/ztree/ztree-object'
        }).use(
            ['admin'],
            function () {
                var $ = layui.jquery;
                var admin = layui.admin;

                // 移除loading动画
                setTimeout(function () {
                    admin.removeLoading();
                }, window == top ? 600 : 100);

                //注册session超时的操作
                $
                    .ajaxSetup({
                        contentType: "application/x-www-form-urlencoded;charset=utf-8",
                        complete: function (XMLHttpRequest, textStatus) {

                            //通过XMLHttpRequest取得响应头，sessionstatus，
                            var sessionstatus = XMLHttpRequest
                                .getResponseHeader("sessionstatus");
                            if (sessionstatus === "timeout") {

                                //如果超时就处理 ，指定要跳转的页面
                                window.location = "${ctxPath}/global/sessionError";
                            }
                        }
                    });

            });


    </script>
</head>
<body ontouchstart>
${layoutContent}
<div class="weui-tab" id="buttom_weui" style="height:auto;">
    <div class="weui-tabbar tab-bottom">
        <a href="/pc/orders?orderStatus=2005" class="weui-tabbar__item">
                    <span style="display: inline-block;position: relative;">
                        <i class="icon icon-27 f27 weui-tabbar__icon"></i>
                    </span>
            <p class="weui-tabbar__label">服务订单</p>
        </a>
        <a href="/pc/daily/services" class="weui-tabbar__item">
            <i class="icon icon-28 f27 weui-tabbar__icon"></i>
            <p class="weui-tabbar__label">日报</p>
        </a>
        <a href="/pc/guide/calendar" class="weui-tabbar__item ">
            <i class="icon icon-30 f27 weui-tabbar__icon"></i>
            <p class="weui-tabbar__label f-green">我</p>
        </a>
    </div>
</div>
</body>
</html>
