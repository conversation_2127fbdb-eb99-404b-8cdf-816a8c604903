@layout("/h5/common/_pccontainer.html",{js:["/pages/h5/order/js/orderlist.js"]}){

@ include("/h5/car/carhead.html"){}
<div class="page">
    <form method="post" action="/pc/daily/leave/edit" id="leaveForm">
        <div class="weui-form">
            <div class="weui-form__control-area">
                <div class="weui-cells__group weui-cells__group_form">
                    <div class="weui-cells weui-cells_form">
                        <div class="weui-cell">
                            <div class="weui-cell__hd"><label class="weui-label">车辆</label></div>
                            <div class="weui-cell__bd">
                                <input id="id" name="id"  type="hidden"/>
                                <input id="carName" name="carName"  class="weui-input" placeholder="车辆"  value="${bizGuideDaily.carName}" disabled/>
                            </div>
                        </div>
                        <div class="weui-cell">
                            <div class="weui-cell__hd"><label class="weui-label">天气</label></div>
                            <div class="weui-cell__bd">
                                <input id="weather" name="weather" class="weui-input" placeholder="天气"/>
                            </div>
                        </div>
                        <div class="weui-cell">
                            <div class="weui-cell__hd"><label class="weui-label">表读数</label></div>
                            <div class="weui-cell__bd">
                                <input id="startKm" name="startKm" class="weui-input" pattern="[0-9]*" placeholder="表读数KM"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="weui-gallery" id="gallery">
                <span class="weui-gallery__img" id="galleryImg"></span>
                <div class="weui-gallery__opr">
                    <a href="javascript:" class="weui-gallery__del">
                        <i class="weui-icon-delete weui-icon_gallery-delete"></i>
                    </a>
                </div>
            </div>

            <div class="weui-cells weui-cells_form">
                <div class="weui-cell">
                    <div class="weui-cell__bd">
                        <div class="weui-uploader">
                            <div class="weui-uploader__hd">
                                <p class="weui-uploader__title">车内部以及车辆外部照片</p>
                            </div>
                            <div class="weui-uploader__bd">
                                <ul class="weui-uploader__files" id="uploaderFiles">
                                    @if (isNotEmpty(leavePicsShow)) {
                                    @var leavePicsShowArray = strutil.split(leavePicsShow,",");
                                    @for (leavePicUrl in leavePicsShowArray) {
                                    <li class="weui-uploader__file" style="background-image:url(${leavePicUrl})"></li>
                                    @}
                                    @}
                                </ul>
                                <div class="weui-uploader__input-box">
                                    <input id="uploaderInput" class="weui-uploader__input" type="file" accept="image/*" multiple/>
                                    <input id="leavePics" name="leavePics" type="hidden"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="weui-form__opr-area">
                <button class="weui-btn weui-btn_primary" type="submit" id="showTooltips">保存</button>
            </div>
        </div>
    </form>
</div>
</body>
<script type="text/javascript">
    $(function () {
        layui.use(['ax'], function () {
            var $ = layui.jquery;
            var $ax = layui.ax;
            var form = layui.form;

            var ajax = new $ax("${ctxPath}/pc/daily/leave/detail?id=${bizGuideDaily.id}");
            var result = ajax.start();
            setform($("#leaveForm"),result.data);
        });
        initImg($("#leavePics"));

        @var dcarStr = "";
        @var carnames = "";
        @if (isNotEmpty(carList)) {
        @for (car in carList) {
            @dcarStr = dcarStr + "'" + car.carName + "':'" + car.id + "',";
            @carnames = carnames + "'" + car.carName + "',";
        @}
        @}
        var cars = {
            ${dcarStr}
        }
        $("#carName").picker({
            cols: [
                {
                    textAlign: 'center',
                    values: [${carnames}]
                }
            ],
            onChange: function (p, v, dv) {
                var carId = cars[v]
                $("#carId").val(carId)
            },
            onClose: function (p, v, d) {
            }
        });


        $("#weather").picker({
            cols: [
                {
                    textAlign: 'center',
                    values: ['晴天', '大风', '雨天', '雪']
                }
            ],
            onChange: function (p, v, dv) {
            },
            onClose: function (p, v, d) {
            }
        });
    });
</script>

@}
