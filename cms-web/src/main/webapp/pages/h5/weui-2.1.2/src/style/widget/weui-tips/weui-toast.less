/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../base/fn";

.weui-toast {
    position: fixed;
    z-index: 5000;
    width: 120px;
    height: 120px;
    top: 40%;
    left: 50%;
    transform:translate(-50%,-50%);
    background: rgba(17,17,17,0.7);
    text-align: center;
    border-radius: 5px;
    color: #FFFFFF;

    display:flex;
    flex-direction:column;
    align-items:center;
    justify-content:center;
}
.weui-icon_toast {
    display: block;
    &.weui-icon-success-no-circle{
      color: #FFFFFF;
      width: 55px;
      height: 55px;
    }
    &.weui-loading{
      margin:8px 0;
      width:38px;
      height:38px;
      vertical-align: baseline;
    }
}

.weui-toast__content {
    font-size:14px;
}
