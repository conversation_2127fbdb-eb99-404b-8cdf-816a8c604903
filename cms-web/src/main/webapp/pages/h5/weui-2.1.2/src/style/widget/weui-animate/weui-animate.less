/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../base/fn";

@keyframes slideUp {
    from {
        transform: translate3d(0, 100%, 0);
    }

    to {
        transform: translate3d(0, 0, 0);
    }
}

.weui-animate-slide-up {
    animation: slideUp ease .3s forwards;
}

@keyframes slideDown {
    from {
        transform: translate3d(0, 0, 0);
    }

    to {
        transform: translate3d(0, 100%, 0);
    }
}

.weui-animate-slide-down {
    animation: slideDown ease .3s forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.weui-animate-fade-in {
    animation: fadeIn ease .3s forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.weui-animate-fade-out {
    animation: fadeOut ease .3s forwards;
}