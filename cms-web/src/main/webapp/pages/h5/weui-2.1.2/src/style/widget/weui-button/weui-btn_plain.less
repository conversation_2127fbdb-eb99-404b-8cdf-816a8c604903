/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../base/fn";

.weui-btn_plain-primary {
    color: @weuiBtnPlainPrimaryColor;
    border: 1px solid @weuiBtnPlainPrimaryBorderColor;
    &:not(.weui-btn_plain-disabled):active {
        color:@weuiBtnPlainPrimaryActiveColor;
        border-color: @weuiBtnPlainPrimaryActiveBorderColor;
		background-color:rgba(0,0,0,.1);
    }
    &:after {
        border-width: 0;
    }
}

.weui-btn_plain-default {
    color: @weuiBtnPlainDefaultColor;
    border: 1px solid @weuiBtnPlainDefaultBorderColor;
    &:not(.weui-btn_plain-disabled):active {
        color:@weuiBtnPlainDefaultActiveColor;
        border-color: @weuiBtnPlainDefaultActiveBorderColor;
		background-color:rgba(0,0,0,.1);
    }
    &:after {
        border-width: 0;
    }
}
.weui-btn_plain-disabled{
    color:rgba(0,0,0,.2);
    border-color:rgba(0,0,0,.2);
}
