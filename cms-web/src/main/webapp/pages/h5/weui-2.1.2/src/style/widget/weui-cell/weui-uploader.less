/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../base/fn";

.weui-uploader{}
.weui-uploader__hd{
    display: flex;
    padding-bottom: @weuiCellGapV;
    align-items: center;
}
.weui-uploader__title{
    flex: 1;
}
.weui-uploader__info{
    color: @weuiTextColorTips;
}

.weui-uploader__bd{
    margin-bottom: @weuiCellGapH - (@weuiCellGapV + @weuiUploaderFileSpacing);
    margin-right: -@weuiUploaderFileSpacing;
    overflow: hidden;
}
.weui-uploader__files{
    list-style: none;
}
.weui-uploader__file{
    float: left;
    margin-right: @weuiUploaderFileSpacing;
    margin-bottom: @weuiUploaderFileSpacing;
    width: @weuiUploaderSize;
    height: @weuiUploaderSize;
    background: no-repeat center center;
    background-size: cover;
}
.weui-uploader__file_status{
    position: relative;
    &:before{
        content: " ";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-color: rgba(0, 0, 0, .5);
    }
    .weui-uploader__file-content{
        display: block;
    }
}
.weui-uploader__file-content{
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #FFFFFF;
    .weui-icon-warn{
        display: inline-block;
    }
}
.weui-uploader__input-box{
    float:left;
    position: relative;
    margin-right: @weuiUploaderFileSpacing;
    margin-bottom: @weuiUploaderFileSpacing;
    width: @weuiUploaderSize;
    height: @weuiUploaderSize;
    box-sizing:border-box;
    background-color:@weuiBgColorDefault;
    &:before, &:after{
        content: " ";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: @weuiUploaderBorderColor;
    }
    &:before{
        width: @weuiUploaderBorderWidth + 1;
        height: @weuiUploaderSize / 3;
    }
    &:after{
        width: @weuiUploaderSize / 3;
        height: @weuiUploaderBorderWidth + 1;
    }
    &:active{
        border-color: @weuiUploaderActiveBorderColor;
        &:before, &:after{
            background-color: @weuiUploaderActiveBorderColor;
        }
    }
}
.weui-uploader__input{
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    .setTapColor();
}
