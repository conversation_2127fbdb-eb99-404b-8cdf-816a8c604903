/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../base/fn";

.weui-cell_swiped {
    display: block;
    padding: 0;
    > .weui-cell__bd {
        position: relative;
        z-index: 1;
        background-color: #FFFFFF;
    }
    > .weui-cell__ft {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        display: flex;
        color: #FFFFFF;
    }
}
.weui-swiped-btn {
    display: block;
    padding: @weuiCellGapV 1em;
    line-height: @weuiCellLineHeight;
    color: inherit;
}
.weui-swiped-btn_default {
    background-color: @weuiBgColorDefault;
}
.weui-swiped-btn_warn {
    background-color: @weuiColorWarn;
}
