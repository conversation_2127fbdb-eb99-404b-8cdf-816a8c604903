/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../base/fn";
@import "weui-btn_global";
@import "weui-btn_default";
@import "weui-btn_primary";
@import "weui-btn_warn";
@import "weui-btn_disabled";
@import "weui-btn_loading";
@import "weui-btn_plain";
@import "weui-btn_cell";

button, input {
    &.weui-btn {
        border-width: 0;
        outline: 0;
        -webkit-appearance: none;
        &:focus {
            outline: 0;
        }
    }
    &.weui-btn_inline,&.weui-btn_mini {
        width: auto;
    }
    &.weui-btn_plain-primary,&.weui-btn_plain-default{
        border-width: 1px;
        background-color: transparent;
    }
}

.weui-btn_mini {
    display: inline-block;
    width:auto;
    padding: 0 unit(12px/@weuiBtnMiniFontSize,em);
    line-height: unit(@weuiBtnMiniHeight/@weuiBtnMiniFontSize);
    font-size: @weuiBtnMiniFontSize;
}


/*gap between btn*/
.weui-btn:not(.weui-btn_mini) + .weui-btn:not(.weui-btn_mini) {
    margin-top: @weuiBtnDefaultGap;
}

.weui-btn.weui-btn_inline + .weui-btn.weui-btn_inline {
    margin-top: auto;
    margin-left: @weuiBtnDefaultGap;
}

.weui-btn-area {
    margin: 48px @weuiBtnDefaultGap 8px; 

}
.weui-btn-area_inline {
    display: flex;
    .weui-btn {
        margin-top: auto;
        margin-right: @weuiBtnDefaultGap;
        width: 100%;
        flex: 1;
        &:last-child {
            margin-right: 0;
        }
    }
}

.weui-btn_reset{
  background:transparent;
  border:0;
  padding:0;
  outline:0;
}
.weui-btn_icon{
  font-size:0;
  &:active{
    [class*="weui-icon-"]{
      color:rgba(0,0,0,.5);
    }
  }
}
