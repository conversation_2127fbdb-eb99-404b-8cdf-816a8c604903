/*
* <PERSON><PERSON> is pleased to support the open source community by making WeUI available.
* 
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* 
* Licensed under the MIT License (the "License"); you may not use this file except in compliance
* with the License. You may obtain a copy of the License at
* 
*       http://opensource.org/licenses/MIT
* 
* Unless required by applicable law or agreed to in writing, software distributed under the License is
* distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
* either express or implied. See the License for the specific language governing permissions and
* limitations under the License.
*/

@import "../../../base/fn";

.weui-cell_vcode {
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0;
}
.weui-vcode-img{
    margin-left: 5px;
    height: @weuiCellHeight;
    vertical-align: middle;
}

.weui-vcode-btn {
    display: inline-block;
    height: @weuiCellHeight;
    margin-left: 5px;
    padding: 0 0.6em 0 0.7em;
    line-height: @weuiCellHeight;
    vertical-align: middle;
    font-size: @weuiCellFontSize;
    color: @weuiDialogLinkColor;
    position:relative;
    &:before{
      .setLeftLine(@weuiLineColorLight);
    }
    button&{
        background-color: transparent;
        border: 0;
        outline: 0;
    }
    &:active {
        color: desaturate(@weuiDialogLinkColor, 30%);
    }
}
