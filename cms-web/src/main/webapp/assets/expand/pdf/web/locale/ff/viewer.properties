# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Hello Æennungo
previous_label=ÆennuÉo
next.title=Hello faango
next_label=Yeeso

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Hello
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=e nder {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} of {{pagesCount}})

zoom_out.title=Lonngo WoÉÉa
zoom_out_label=Lonngo WoÉÉa
zoom_in.title=Lonngo Ara
zoom_in_label=Lonngo Ara
zoom.title=Lonngo
presentation_mode.title=Faytu to  Presentation Mode
presentation_mode_label=Presentation Mode
open_file.title=Uddit Fiilde
open_file_label=Uddit
print.title=Winndito
print_label=Winndito
download.title=Aawto
download_label=Aawto
bookmark.title=Jiytol gonangol (natto walla uddit e henorde)
bookmark_label=Jiytol Gonangol

# Secondary toolbar and context menu
tools.title=KuutorÉe
tools_label=KuutorÉe
first_page.title=Yah to hello adanngo
first_page.label=Yah to hello adanngo
first_page_label=Yah to hello adanngo
last_page.title=Yah to hello wattindiingo
last_page.label=Yah to hello wattindiingo
last_page_label=Yah to hello wattindiingo
page_rotate_cw.title=Yiiltu Faya Ãaamo
page_rotate_cw.label=Yiiltu Faya Ãaamo
page_rotate_cw_label=Yiiltu Faya Ãaamo
page_rotate_ccw.title=Yiiltu Faya Nano
page_rotate_ccw.label=Yiiltu Faya Nano
page_rotate_ccw_label=Yiiltu Faya Nano

cursor_text_select_tool.title=Gollin kaÉirgel cuÉirgel binndi
cursor_text_select_tool_label=KaÉirgel cuÉirgel binndi
cursor_hand_tool.title=Hurmin kuutorgal junngo
cursor_hand_tool_label=KaÉirgel junngo

# Document properties dialog box
document_properties.title=KeeroraaÉi Winndanndeâ¦
document_properties_label=KeeroraaÉi Winndanndeâ¦
document_properties_file_name=Innde fiilde:
document_properties_file_size=Æetol fiilde:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bite)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bite)
document_properties_title=Tiitoonde:
document_properties_author=BinnduÉo:
document_properties_subject=ToÉÉere:
document_properties_keywords=Kelmekele jiytirÉe:
document_properties_creation_date=Ãalnde Sosaa:
document_properties_modification_date=Ãalnde Waylaa:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=CosÉo:
document_properties_producer=PaggiiÉo PDF:
document_properties_version=Yamre PDF:
document_properties_page_count=Limoore Kelle:
document_properties_page_size=Æeto Hello:
document_properties_page_size_unit_inches=nder
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=dariingo
document_properties_page_size_orientation_landscape=wertiingo
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Æataake
document_properties_page_size_name_legal=Laawol
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
document_properties_close=Uddu

print_progress_message=Nana heboo winnditaade fiilanndeâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Haaytu

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Toggilo Palal Sawndo
toggle_sidebar_notification.title=Palal sawndo (dokimaa oo ina waÉi taarngo/cinnde)
toggle_sidebar_label=Toggilo Palal Sawndo
document_outline.title=Hollu Æ³iyal Fiilannde (dobdobo ngam wertude/taggude teme fof)
document_outline_label=ToÉÉe Fiilannde
attachments.title=Hollu ÆisanÉe
attachments_label=ÆisanÉe
thumbs.title=Hollu DooÉe
thumbs_label=DooÉe
findbar.title=Yiylo e fiilannde
findbar_label=Yiytu

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Hello {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=DooÉre Hello {{page}}

# Find panel button title and messages
find_input.title=Yiytu
find_input.placeholder=Yiylo nder dokimaa
find_previous.title=Yiylo cilol Éennugol konngol ngol
find_previous_label=ÆennuÉo
find_next.title=Yiylo cilol garowol konngol ngol
find_next_label=Yeeso
find_highlight=Jalbin fof
find_match_case_label=JaaÉnu darnde
find_reached_top=HeÉii fuÉÉorde fiilannde, jokku faya les
find_reached_bottom=HeÉii hoore fiilannde, jokku faya les
find_not_found=Konngi njiyataa

# Error panel labels
error_more_info=Æeydu Humpito
error_less_info=Ustu Humpito
error_close=Uddu
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Æatakuure: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Fiilde: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Gorol: {{line}}
rendering_error=Juumre waÉii tuma nde yoÅkittoo hello.

# Predefined zoom values
page_scale_width=Njaajeendi Hello
page_scale_fit=KeÆ´eendi Hello
page_scale_auto=Loongorde Jaajol
page_scale_actual=Æetol Jaati
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Juumre
loading_error=Juumre waÉii tuma nde loowata PDF oo.
invalid_file_error=Fiilde PDF moÆ´Æ´aani walla jiibii.
missing_file_error=Fiilde PDF ena Åakki.
unexpected_response_error=Jaabtol sarworde tijjinooka.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Siiftannde]
password_label=Naatu finnde ngam uddite ndee fiilde PDF.
password_invalid=Finnde moÆ´Æ´aani. TiiÉno eto kadi.
password_ok=OK
password_cancel=Haaytu

printing_not_supported=Reentino: Winnditagol tammbitaaka no feewi e ndee wanngorde.
printing_not_ready=Reentino: PDF oo loowaaki haa timmi ngam winnditagol.
web_fonts_disabled=Ponte geese ko daaÆ´aaÉe: horiima huutoraade ponte PDF coomtoraaÉe.
document_colors_not_allowed=PiilanÉe PDF njamiraaka yoo kuutoro goobuuji mum'en keeriiÉi: 'Yamir kello yoo kuutoro goobuuki keeriiÉi' koko daaÆ´aa e wanngorde ndee.
