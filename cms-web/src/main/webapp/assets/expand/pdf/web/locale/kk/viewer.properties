# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ÐÐ»Ð´ÑÒ£ÒÑ Ð¿Ð°ÑÐ°Ò
previous_label=ÐÐ»Ð´ÑÒ£ÒÑÑÑ
next.title=ÐÐµÐ»ÐµÑÑ Ð¿Ð°ÑÐ°Ò
next_label=ÐÐµÐ»ÐµÑÑ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ÐÐ°ÑÐ°Ò
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} ÑÑÑÐ½ÐµÐ½
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=(Ð¿Ð°ÑÐ°Ò {{pageNumber}}, {{pagesCount}} ÑÑÑÐ½ÐµÐ½)

zoom_out.title=ÐÑÑÑÑÐµÐ¹ÑÑ
zoom_out_label=ÐÑÑÑÑÐµÐ¹ÑÑ
zoom_in.title=Ò®Ð»ÐºÐµÐ¹ÑÑ
zoom_in_label=Ò®Ð»ÐºÐµÐ¹ÑÑ
zoom.title=ÐÐ°ÑÑÑÐ°Ð±
presentation_mode.title=ÐÑÐµÐ·ÐµÐ½ÑÐ°ÑÐ¸Ñ ÑÐµÐ¶Ð¸Ð¼ÑÐ½Ðµ Ð°ÑÑÑÑ
presentation_mode_label=ÐÑÐµÐ·ÐµÐ½ÑÐ°ÑÐ¸Ñ ÑÐµÐ¶Ð¸Ð¼Ñ
open_file.title=Ð¤Ð°Ð¹Ð»Ð´Ñ Ð°ÑÑ
open_file_label=ÐÑÑ
print.title=ÐÐ°ÑÐ¿Ð°ÒÐ° ÑÑÒÐ°ÑÑ
print_label=ÐÐ°ÑÐ¿Ð°ÒÐ° ÑÑÒÐ°ÑÑ
download.title=ÐÒ¯ÐºÑÐµÐ¿ Ð°Ð»Ñ
download_label=ÐÒ¯ÐºÑÐµÐ¿ Ð°Ð»Ñ
bookmark.title=ÐÒÑÐ¼Ð´Ð°ÒÑ ÐºÓ©ÑÑÐ½ÑÑ (ÐºÓ©ÑÑÑÑ Ð½Ðµ Ð¶Ð°Ò£Ð° ÑÐµÑÐµÐ·ÐµÐ´Ðµ Ð°ÑÑ)
bookmark_label=ÐÒÑÐ¼Ð´Ð°ÒÑ ÐºÓ©ÑÑÐ½ÑÑ

# Secondary toolbar and context menu
tools.title=ÒÒ±ÑÐ°Ð»Ð´Ð°Ñ
tools_label=ÒÒ±ÑÐ°Ð»Ð´Ð°Ñ
first_page.title=ÐÐ»ÒÐ°ÑÒÑ Ð¿Ð°ÑÐ°ÒÒÐ° Ó©ÑÑ
first_page.label=ÐÐ»ÒÐ°ÑÒÑ Ð¿Ð°ÑÐ°ÒÒÐ° Ó©ÑÑ
first_page_label=ÐÐ»ÒÐ°ÑÒÑ Ð¿Ð°ÑÐ°ÒÒÐ° Ó©ÑÑ
last_page.title=Ð¡Ð¾Ò£ÒÑ Ð¿Ð°ÑÐ°ÒÒÐ° Ó©ÑÑ
last_page.label=Ð¡Ð¾Ò£ÒÑ Ð¿Ð°ÑÐ°ÒÒÐ° Ó©ÑÑ
last_page_label=Ð¡Ð¾Ò£ÒÑ Ð¿Ð°ÑÐ°ÒÒÐ° Ó©ÑÑ
page_rotate_cw.title=Ð¡Ð°ÒÐ°Ñ ÑÑÐ»Ñ Ð±Ð°ÒÑÑÑÐ¼ÐµÐ½ Ð°Ð¹Ð½Ð°Ð»Ð´ÑÑÑ
page_rotate_cw.label=Ð¡Ð°ÒÐ°Ñ ÑÑÐ»Ñ Ð±Ð°ÒÑÑÑÐ¼ÐµÐ½ Ð±Ò±ÑÑ
page_rotate_cw_label=Ð¡Ð°ÒÐ°Ñ ÑÑÐ»Ñ Ð±Ð°ÒÑÑÑÐ¼ÐµÐ½ Ð±Ò±ÑÑ
page_rotate_ccw.title=Ð¡Ð°ÒÐ°Ñ ÑÑÐ»Ñ Ð±Ð°ÒÑÑÑÐ½Ð° ÒÐ°ÑÑÑ Ð±Ò±ÑÑ
page_rotate_ccw.label=Ð¡Ð°ÒÐ°Ñ ÑÑÐ»Ñ Ð±Ð°ÒÑÑÑÐ½Ð° ÒÐ°ÑÑÑ Ð±Ò±ÑÑ
page_rotate_ccw_label=Ð¡Ð°ÒÐ°Ñ ÑÑÐ»Ñ Ð±Ð°ÒÑÑÑÐ½Ð° ÒÐ°ÑÑÑ Ð±Ò±ÑÑ

cursor_text_select_tool.title=ÐÓÑÑÐ½Ð´Ñ ÑÐ°Ò£Ð´Ð°Ñ ÒÒ±ÑÐ°Ð»ÑÐ½ ÑÑÐºÐµ ÒÐ¾ÑÑ
cursor_text_select_tool_label=ÐÓÑÑÐ½Ð´Ñ ÑÐ°Ò£Ð´Ð°Ñ ÒÒ±ÑÐ°Ð»Ñ
cursor_hand_tool.title=ÒÐ¾Ð» ÒÒ±ÑÐ°Ð»ÑÐ½ ÑÑÐºÐµ ÒÐ¾ÑÑ
cursor_hand_tool_label=ÒÐ¾Ð» ÒÒ±ÑÐ°Ð»Ñ

scroll_vertical.title=ÐÐµÑÑÐ¸ÐºÐ°Ð»Ð´Ñ Ð°Ð¹Ð½Ð°Ð»Ð´ÑÑÑÐ´Ñ ÒÐ¾Ð»Ð´Ð°Ð½Ñ
scroll_vertical_label=ÐÐµÑÑÐ¸ÐºÐ°Ð»Ð´Ñ Ð°Ð¹Ð½Ð°Ð»Ð´ÑÑÑ
scroll_horizontal.title=ÐÐ¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»Ð´Ñ Ð°Ð¹Ð½Ð°Ð»Ð´ÑÑÑÐ´Ñ ÒÐ¾Ð»Ð´Ð°Ð½Ñ
scroll_horizontal_label=ÐÐ¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»Ð´Ñ Ð°Ð¹Ð½Ð°Ð»Ð´ÑÑÑ
scroll_wrapped.title=ÐÐ°ÑÑÑÐ°Ð±ÑÐ°Ð»Ð°ÑÑÐ½ Ð°Ð¹Ð½Ð°Ð»Ð´ÑÑÑÐ´Ñ ÒÐ¾Ð»Ð´Ð°Ð½Ñ
scroll_wrapped_label=ÐÐ°ÑÑÑÐ°Ð±ÑÐ°Ð»Ð°ÑÑÐ½ Ð°Ð¹Ð½Ð°Ð»Ð´ÑÑÑ

spread_none.title=ÐÐ°Ð·ÑÒ Ð±ÐµÑÑÐµÑ ÑÐµÐ¶Ð¸Ð¼ÑÐ½ ÒÐ¾Ð»Ð´Ð°Ð½Ð±Ð°Ñ
spread_none_label=ÐÐ°Ð·ÑÒ Ð±ÐµÑÑÐµÑ ÑÐµÐ¶Ð¸Ð¼ÑÑÐ·
spread_odd.title=ÐÐ°Ð·ÑÒ Ð±ÐµÑÑÐµÑ ÑÐ°Ò Ð½Ó©Ð¼ÑÑÐ»Ñ Ð±ÐµÑÑÐµÑÐ´ÐµÐ½ Ð±Ð°ÑÑÐ°Ð»Ð°Ð´Ñ
spread_odd_label=Ð¢Ð°Ò Ð½Ó©Ð¼ÑÑÐ»Ñ Ð±ÐµÑÑÐµÑ ÑÐ¾Ð» Ð¶Ð°ÒÑÐ°Ð½
spread_even.title=ÐÐ°Ð·ÑÒ Ð±ÐµÑÑÐµÑ Ð¶Ò±Ð¿ Ð½Ó©Ð¼ÑÑÐ»Ñ Ð±ÐµÑÑÐµÑÐ´ÐµÐ½ Ð±Ð°ÑÑÐ°Ð»Ð°Ð´Ñ
spread_even_label=ÐÒ±Ð¿ Ð½Ó©Ð¼ÑÑÐ»Ñ Ð±ÐµÑÑÐµÑ ÑÐ¾Ð» Ð¶Ð°ÒÑÐ°Ð½

# Document properties dialog box
document_properties.title=ÒÒ±Ð¶Ð°Ñ ÒÐ°ÑÐ¸ÐµÑÑÐµÑÑâ¦
document_properties_label=ÒÒ±Ð¶Ð°Ñ ÒÐ°ÑÐ¸ÐµÑÑÐµÑÑâ¦
document_properties_file_name=Ð¤Ð°Ð¹Ð» Ð°ÑÑ:
document_properties_file_size=Ð¤Ð°Ð¹Ð» Ó©Ð»ÑÐµÐ¼Ñ:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
document_properties_title=Ð¢Ð°ÒÑÑÑÐ±Ñ:
document_properties_author=ÐÐ²ÑÐ¾ÑÑ:
document_properties_subject=Ð¢Ð°ÒÑÑÑÐ±Ñ:
document_properties_keywords=ÐÑÐ»Ñ ÑÓ©Ð·Ð´ÐµÑ:
document_properties_creation_date=ÐÐ°ÑÐ°Ð»ÒÐ°Ð½ ÐºÒ¯Ð½Ñ:
document_properties_modification_date=Ð¢Ò¯Ð·ÐµÑÑ ÐºÒ¯Ð½Ñ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ÐÐ°ÑÐ°ÒÐ°Ð½:
document_properties_producer=PDF Ó©Ð½Ð´ÑÑÐ³ÐµÐ½:
document_properties_version=PDF Ð½Ò±ÑÒÐ°ÑÑ:
document_properties_page_count=ÐÐµÑÑÐµÑ ÑÐ°Ð½Ñ:
document_properties_page_size=ÐÐµÑ Ó©Ð»ÑÐµÐ¼Ñ:
document_properties_page_size_unit_inches=Ð´ÑÐ¹Ð¼
document_properties_page_size_unit_millimeters=Ð¼Ð¼
document_properties_page_size_orientation_portrait=ÑÑÐº
document_properties_page_size_orientation_landscape=Ð¶Ð°ÑÑÒ
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ÐÑÐ»Ð´Ð°Ð¼ Web ÐºÓ©ÑÑÐ½ÑÑÑ:
document_properties_linearized_yes=ÐÓ
document_properties_linearized_no=ÐÐ¾Ò
document_properties_close=ÐÐ°Ð±Ñ

print_progress_message=ÒÒ±Ð¶Ð°ÑÑÑ Ð±Ð°ÑÐ¿Ð°ÒÐ° ÑÑÒÐ°ÑÑ Ò¯ÑÑÐ½ Ð´Ð°Ð¹ÑÐ½Ð´Ð°Ñâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÐÐ°Ñ ÑÐ°ÑÑÑ

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ÐÒ¯Ð¹ÑÑ Ð¿Ð°Ð½ÐµÐ»ÑÐ½ ÐºÓ©ÑÑÐµÑÑ/Ð¶Ð°ÑÑÑÑ
toggle_sidebar_notification.title=ÐÒ¯Ð¹ÑÑ Ð¿Ð°Ð½ÐµÐ»ÑÐ½ ÐºÓ©ÑÑÐµÑÑ/Ð¶Ð°ÑÑÑÑ (ÒÒ±Ð¶Ð°ÑÑÐ° ÒÒ±ÑÑÐ»ÑÐ¼Ñ/ÑÐ°Ð»ÑÐ½ÑÐ¼Ð´Ð°Ñ Ð±Ð°Ñ)
toggle_sidebar_label=ÐÒ¯Ð¹ÑÑ Ð¿Ð°Ð½ÐµÐ»ÑÐ½ ÐºÓ©ÑÑÐµÑÑ/Ð¶Ð°ÑÑÑÑ
document_outline.title=ÒÒ±Ð¶Ð°Ñ ÒÒ±ÑÑÐ»ÑÐ¼ÑÐ½ ÐºÓ©ÑÑÐµÑÑ (Ð±Ð°ÑÐ»ÑÒ Ð½ÓÑÑÐµÐ»ÐµÑÐ´Ñ Ð¶Ð°Ð·ÑÒ ÒÑÐ»Ñ/Ð¶Ð¸Ð½Ð°Ñ Ò¯ÑÑÐ½ ÒÐ¾Ñ ÑÐµÑÑÑ ÐºÐµÑÐµÐº)
document_outline_label=ÒÒ±Ð¶Ð°Ñ ÒÒ±ÑÐ°Ð¼Ð°ÑÑ
attachments.title=Ð¡Ð°Ð»ÑÐ½ÑÐ¼Ð´Ð°ÑÐ´Ñ ÐºÓ©ÑÑÐµÑÑ
attachments_label=Ð¡Ð°Ð»ÑÐ½ÑÐ¼Ð´Ð°Ñ
thumbs.title=ÐÑÑÑ ÐºÓ©ÑÑÐ½ÑÑÑÐµÑÐ´Ñ ÐºÓ©ÑÑÐµÑÑ
thumbs_label=ÐÑÑÑ ÐºÓ©ÑÑÐ½ÑÑÑÐµÑ
findbar.title=ÒÒ±Ð¶Ð°ÑÑÐ°Ð½ ÑÐ°Ð±Ñ
findbar_label=Ð¢Ð°Ð±Ñ

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}} Ð¿Ð°ÑÐ°ÒÑ
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} Ð¿Ð°ÑÐ°ÒÑ Ò¯ÑÑÐ½ ÐºÑÑÑ ÐºÓ©ÑÑÐ½ÑÑÑ

# Find panel button title and messages
find_input.title=Ð¢Ð°Ð±Ñ
find_input.placeholder=ÒÒ±Ð¶Ð°ÑÑÐ°Ð½ ÑÐ°Ð±Ñâ¦
find_previous.title=ÐÑÑ ÑÓ©Ð·Ð´ÐµÑÐ´ÑÒ£ Ð¼ÓÑÑÐ½Ð½ÐµÐ½ Ð°Ð»Ð´ÑÒ£ÒÑ ÐºÐµÐ·Ð´ÐµÑÑÑÐ½ ÑÐ°Ð±Ñ
find_previous_label=ÐÐ»Ð´ÑÒ£ÒÑÑÑ
find_next.title=ÐÑÑ ÑÓ©Ð·Ð´ÐµÑÐ´ÑÒ£ Ð¼ÓÑÑÐ½Ð½ÐµÐ½ ÐºÐµÐ»ÐµÑÑ ÐºÐµÐ·Ð´ÐµÑÑÑÐ½ ÑÐ°Ð±Ñ
find_next_label=ÐÐµÐ»ÐµÑÑ
find_highlight=ÐÐ°ÑÐ»ÑÒÑÐ½ ÑÒ¯ÑÐ¿ÐµÐ½ ÐµÑÐµÐºÑÐµÐ»ÐµÑ
find_match_case_label=Ð ÐµÐ³Ð¸ÑÑÑÐ´Ñ ÐµÑÐºÐµÑÑ
find_entire_word_label=Ð¡Ó©Ð·Ð´ÐµÑ ÑÐ¾Ð»ÑÒÑÐ¼ÐµÐ½
find_reached_top=ÒÒ±Ð¶Ð°ÑÑÑÒ£ Ð±Ð°ÑÑÐ½Ð° Ð¶ÐµÑÑÑÐº, ÑÐ¾Ò£ÑÐ½Ð°Ð½ Ð±Ð°ÑÑÐ°Ð¿ Ð¶Ð°Ð»ÒÐ°ÑÑÑÑÐ°Ð¼ÑÐ·
find_reached_bottom=ÒÒ±Ð¶Ð°ÑÑÑÒ£ ÑÐ¾Ò£ÑÐ½Ð° Ð¶ÐµÑÑÑÐº, Ð±Ð°ÑÑÐ½Ð°Ð½ Ð±Ð°ÑÑÐ°Ð¿ Ð¶Ð°Ð»ÒÐ°ÑÑÑÑÐ°Ð¼ÑÐ·
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} / {{total}} ÑÓÐ¹ÐºÐµÑÑÑÐº
find_match_count[two]={{current}} / {{total}} ÑÓÐ¹ÐºÐµÑÑÑÐº
find_match_count[few]={{current}} / {{total}} ÑÓÐ¹ÐºÐµÑÑÑÐº
find_match_count[many]={{current}} / {{total}} ÑÓÐ¹ÐºÐµÑÑÑÐº
find_match_count[other]={{current}} / {{total}} ÑÓÐ¹ÐºÐµÑÑÑÐº
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} ÑÓÐ¹ÐºÐµÑÑÑÐºÑÐµÐ½ ÐºÓ©Ð¿
find_match_count_limit[one]={{limit}} ÑÓÐ¹ÐºÐµÑÑÑÐºÑÐµÐ½ ÐºÓ©Ð¿
find_match_count_limit[two]={{limit}} ÑÓÐ¹ÐºÐµÑÑÑÐºÑÐµÐ½ ÐºÓ©Ð¿
find_match_count_limit[few]={{limit}} ÑÓÐ¹ÐºÐµÑÑÑÐºÑÐµÐ½ ÐºÓ©Ð¿
find_match_count_limit[many]={{limit}} ÑÓÐ¹ÐºÐµÑÑÑÐºÑÐµÐ½ ÐºÓ©Ð¿
find_match_count_limit[other]={{limit}} ÑÓÐ¹ÐºÐµÑÑÑÐºÑÐµÐ½ ÐºÓ©Ð¿
find_not_found=Ð¡Ó©Ð·(Ð´ÐµÑ) ÑÐ°Ð±ÑÐ»Ð¼Ð°Ð´Ñ

# Error panel labels
error_more_info=ÐÓ©Ð±ÑÑÐµÐº Ð°ÒÐ¿Ð°ÑÐ°Ñ
error_less_info=ÐÐ·ÑÑÐ°Ò Ð°ÒÐ¿Ð°ÑÐ°Ñ
error_close=ÐÐ°Ð±Ñ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (Ð¶Ð¸Ð½Ð°Ò: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ð¥Ð°Ð±Ð°ÑÐ»Ð°Ð¼Ð°: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ð¡ÑÐµÐº: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Ð¤Ð°Ð¹Ð»: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ÐÐ¾Ð»: {{line}}
rendering_error=ÐÐ°ÑÐ°ÒÑÑ Ó©Ò£Ð´ÐµÑ ÐºÐµÐ·ÑÐ½Ð´Ðµ ÒÐ°ÑÐµ ÐºÐµÑÑÑ.

# Predefined zoom values
page_scale_width=ÐÐ°ÑÐ°Ò ÐµÐ½Ñ
page_scale_fit=ÐÐ°ÑÐ°ÒÑÑ ÑÑÐ¹Ð´ÑÑÑ
page_scale_auto=ÐÐ²ÑÐ¾Ð¼Ð°ÑÑÑÐ°Ð±ÑÐ°Ñ
page_scale_actual=ÐÐ°ÒÑÑ Ó©Ð»ÑÐµÐ¼Ñ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ÒÐ°ÑÐµ
loading_error=PDF Ð¶Ò¯ÐºÑÐµÑ ÐºÐµÐ·ÑÐ½Ð´Ðµ ÒÐ°ÑÐµ ÐºÐµÑÑÑ.
invalid_file_error=ÐÐ°ÒÑÐ¼Ð´Ð°Ð»ÒÐ°Ð½ Ð½ÐµÐ¼ÐµÑÐµ ÒÐ°ÑÐµ PDF ÑÐ°Ð¹Ð».
missing_file_error=PDF ÑÐ°Ð¹Ð»Ñ Ð¶Ð¾Ò.
unexpected_response_error=Ð¡ÐµÑÐ²ÐµÑÐ´ÑÒ£ ÐºÒ¯ÑÐ¿ÐµÐ³ÐµÐ½ Ð¶Ð°ÑÐ°Ð±Ñ.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Ð°Ò£Ð´Ð°ÑÐ¿Ð°ÑÑ]
password_label=ÐÒ±Ð» PDF ÑÐ°Ð¹Ð»ÑÐ½ Ð°ÑÑ Ò¯ÑÑÐ½ Ð¿Ð°ÑÐ¾Ð»ÑÐ´Ñ ÐµÐ½Ð³ÑÐ·ÑÒ£ÑÐ·.
password_invalid=ÐÐ°ÑÐ¾Ð»Ñ Ð´Ò±ÑÑÑ ÐµÐ¼ÐµÑ. ÒÐ°Ð¹ÑÐ°Ð»Ð°Ð¿ ÐºÓ©ÑÑÒ£ÑÐ·.
password_ok=ÐÐ
password_cancel=ÐÐ°Ñ ÑÐ°ÑÑÑ

printing_not_supported=ÐÑÐºÐµÑÑÑ: ÐÐ°ÑÐ¿Ð°ÒÐ° ÑÑÒÐ°ÑÑÐ´Ñ Ð±Ò±Ð» Ð±ÑÐ°ÑÐ·ÐµÑ ÑÐ¾Ð»ÑÒÑÐ¼ÐµÐ½ ÒÐ¾Ð»Ð´Ð°Ð¼Ð°Ð¹Ð´Ñ.
printing_not_ready=ÐÑÐºÐµÑÑÑ: ÐÐ°ÑÐ¿Ð°ÒÐ° ÑÑÒÐ°ÑÑ Ò¯ÑÑÐ½, Ð±Ò±Ð» PDF ÑÐ¾Ð»ÑÒÑÐ¼ÐµÐ½ Ð¶Ò¯ÐºÑÐµÐ»ÑÐ¿ Ð°Ð»ÑÐ½Ð±Ð°Ð´Ñ.
web_fonts_disabled=ÐÐµÐ± ÒÐ°ÑÑÐ¿ÑÐµÑÑ ÑÓ©Ð½Ð´ÑÑÑÐ»Ð³ÐµÐ½: ÒÒ±ÑÐ°Ð¼ÑÐ½Ð° ÐµÐ½Ð³ÑÐ·ÑÐ»Ð³ÐµÐ½ PDF ÒÐ°ÑÑÐ¿ÑÐµÑÑÐ½ ÒÐ¾Ð»Ð´Ð°Ð½Ñ Ð¼Ò¯Ð¼ÐºÑÐ½ ÐµÐ¼ÐµÑ.
document_colors_not_allowed=PDF ÒÒ±Ð¶Ð°ÑÑÐ°ÑÑÐ½Ð° Ó©Ð·Ð´ÑÐº ÑÒ¯ÑÑÐµÑÐ´Ñ ÒÐ¾Ð»Ð´Ð°Ð½Ñ ÑÒ±ÒÑÐ°Ñ ÐµÑÑÐ»Ð¼ÐµÐ³ÐµÐ½: Ð±Ò±Ð» Ð±ÑÐ°ÑÐ·ÐµÑÐ´Ðµ 'ÐÐµÐ±-ÑÐ°Ð¹ÑÑÐ°ÑÒÐ° Ó©Ð·Ð´ÐµÑÑÐ½ÑÒ£ ÑÒ¯ÑÑÐµÑÑÐ½ ÒÐ¾Ð»Ð´Ð°Ð½ÑÒÐ° ÑÒ±ÒÑÐ°Ñ Ð±ÐµÑÑ' Ð¼Ò¯Ð¼ÐºÑÐ½Ð´ÑÐ³Ñ ÑÓ©Ð½Ð´ÑÑÑÐ»Ñ ÑÒ±Ñ.
