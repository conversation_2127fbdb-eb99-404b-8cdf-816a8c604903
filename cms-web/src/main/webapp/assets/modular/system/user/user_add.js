/**
 * 用户详情对话框
 */
var UserInfoDlg = {
    data: {
        deptId: "",
        deptName: ""
    }
};

layui.use(['layer', 'form', 'admin', 'laydate', 'ax'], function () {
    var $ = layui.jquery;
    var $ax = layui.ax;
    var form = layui.form;
    var admin = layui.admin;
    var laydate = layui.laydate;
    var layer = layui.layer;

    // 让当前iframe弹层高度适应
    admin.iframeAuto();

    // 点击部门时
    $('#deptName').click(function () {
        var formName = encodeURIComponent("parent.UserInfoDlg.data.deptName");
        var formId = encodeURIComponent("parent.UserInfoDlg.data.deptId");
        var treeUrl = encodeURIComponent("/dept/tree");

        layer.open({
            type: 2,
            title: '部门选择',
            area: ['300px', '400px'],
            content: Feng.ctxPath + '/system/commonTree?formName=' + formName + "&formId=" + formId + "&treeUrl=" + treeUrl,
            end: function () {
                console.log(UserInfoDlg.data);
                $("#deptId").val(UserInfoDlg.data.deptId);
                $("#deptName").val(UserInfoDlg.data.deptName);
            }
        });
    });
    // 点击上级领导时
    new SearchDropdown({
        input: document.getElementById('leaderName'),
        dropdown: document.getElementById('dropdownMenu'),
        keywordName:'name',
        url: Feng.ctxPath +'/mgr/list?page=1&limit=10', // 替换为你的API接口
        minLength: 2,
        timeout: 300,
        parseArray:function(response){
          return JSON.parse(response).data
        },
        renderDropdownItem:function(item){
               return '<span>'+item.name+'<span>';
        },
        selectCallback:function(item){
             $("#leaderId").val(item.userId);
        }
    });
    // 添加表单验证方法
    form.verify({
        psw: [/^[\S]{6,12}$/, '密码必须6到12位，且不能出现空格'],
        repsw: function (value) {
            if (value !== $('#userForm input[name=password]').val()) {
                return '两次密码输入不一致';
            }
        }
    });

    // 渲染时间选择框
    laydate.render({
        elem: '#birthday'
    });




    // 获取 plat_vals 值并转为数组（假设已从后端获取）
    var authPlats = "";
    if ($("#authPlats").val()) {
        authPlats = $("#authPlats").val().split(','); // 如 "微信,支付宝" → ["微信","支付宝"]
    }

    // 请求接口获取平台列表
    $.ajax({
        url: '/bizPlatform/authList',
        type: 'GET',
        success: function (res) {
            if (res.code === 200) {
                var html = [];
                res.data.forEach(function (item) {
                    // 检查当前平台是否在 authPlats 中
                    var isChecked = authPlats.includes(item.platName) ? 'checked' : '';
                    html.push(
                        '<input type="checkbox" name="authPlat" lay-filter="authPlatFl"  lay-skin="primary" ' + isChecked +
                        ' value="' + item.platName + '" title="' + item.platName + '">'
                    );
                });
                // 渲染到页面
                $('#authPlatBox').html(html.join(''));
                // 更新 Layui 样式
                form.render('checkbox');
            }
        }
    });

    // 监听复选框选中事件（若需要实时更新 plat_vals）
    form.on('checkbox(authPlatFl)', function (data) {
        var checked = [];
        $('input[name="authPlat"]:checked').each(function () {
            checked.push($(this).val());
        });
        $('input[name="authPlats"]').val(checked.join(','));
        Feng.success($('input[name="authPlats"]').val())
    });




    // 获取 plat_vals 值并转为数组（假设已从后端获取）
    var coopPlats = "";
    if ($("#coopPlats").val()) {
        coopPlats = $("#coopPlats").val().split(','); // 如 "微信,支付宝" → ["微信","支付宝"]
    }

    // 请求接口获取平台列表
    $.ajax({
        url: '/bizPlatform/list?page=0&limit=200&cooperation=1',
        type: 'GET',
        success: function (res) {
            if (res.code === 200) {
                var html = [];
                res.data.forEach(function (item) {
                    // 检查当前平台是否在 platVals 中
                    var isChecked = coopPlats.includes(item.platName) ? 'checked' : '';
                    html.push(
                        '<input type="checkbox" name="coopPlat" lay-filter="coopPlatFl"  lay-skin="primary" ' + isChecked +
                        ' value="' + item.platName + '" title="' + item.platName + '">'
                    );
                });
                // 渲染到页面
                $('#platBox').html(html.join(''));
                // 更新 Layui 样式
                form.render('checkbox');
            }
        }
    });

    // 监听复选框选中事件（若需要实时更新 plat_vals）
    form.on('checkbox(coopPlatFl)', function (data) {
        var checked = [];
        $('input[name="coopPlat"]:checked').each(function () {
            checked.push($(this).val());
        });
        $('input[name="coopPlats"]').val(checked.join(','));
        Feng.success($('input[name="coopPlats"]').val())
    });


    // 表单提交事件
    form.on('submit(btnSubmit)', function (data) {
        var ajax = new $ax(Feng.ctxPath + "/mgr/add", function (data) {
            Feng.success("添加成功！");

            //传给上个页面，刷新table用
            admin.putTempData('formOk', true);

            //关掉对话框
            admin.closeThisDialog();
        }, function (data) {
            Feng.error("添加失败！" + data.responseJSON.message)
        });
        ajax.set(data.field);
        ajax.start();
    });
});