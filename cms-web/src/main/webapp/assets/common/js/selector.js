var selector = {};
/**
 *
 * @param reqUrl 请求URL var destReqUrl = Feng.ctxPath +"/bizDest/querySearch"
 * @param selectorId  SELECT ID 'serviceCityId'
 * @param idPName 被解析json obj 的熟悉ID
 * @param namePName 被解析json obj 的熟悉NAME
 * @param hiddenPname 隐藏提交的属性name
 */
selector.init = function (reqUrl, selectorId, idPName, namePName, hiddenPname) {
    var $ = layui.$;
    var $ax = layui.ax;
    var form = layui.form;
    $("#" + selectorId).empty();
    var ajax = new $ax(reqUrl, function (rpsData) {
        $("#" + selectorId).append('<option value="">请选择</option>');
        for (var i = 0; i < rpsData.data.length; i++) {
            var idVal = eval('rpsData.data[i].' + idPName);
            var nameVal = eval('rpsData.data[i].' + namePName);
            $("#" + selectorId).append('<option value="' + idVal + '">' + nameVal + '</option>');
        }

        form.on('select(' + selectorId + ')', function (data) {
            var name = data.elem[data.elem.selectedIndex].text
            $("#" + hiddenPname).val(name);
        });
        form.render("select");
    }, function (data) {
        Feng.error("加载失败!" + data.message + "!");
    });
    ajax.start();
}


selector.init = function (reqUrl, selectorId, idPName, namePName, hiddenPname,ckcal) {
    var $ = layui.$;
    var $ax = layui.ax;
    var form = layui.form;
    $("#" + selectorId).empty();
    var ajax = new $ax(reqUrl, function (rpsData) {
        $("#" + selectorId).append('<option value="">请选择</option>');
        for (var i = 0; i < rpsData.data.length; i++) {
            var idVal = eval('rpsData.data[i].' + idPName);
            var nameVal = eval('rpsData.data[i].' + namePName);
            $("#" + selectorId).append('<option value="' + idVal + '">' + nameVal + '</option>');
        }

        form.on('select(' + selectorId + ')', function (data) {
            var name = data.elem[data.elem.selectedIndex].text
            $("#" + hiddenPname).val(name);
            if(ckcal){
                ckcal(data);
            }
        });
        form.render("select");
    }, function (data) {
        Feng.error("加载失败!" + data.message + "!");
    });
    ajax.start();
}

/**
 * 动态options，回显
 * @param selectorId
 * @param idPName
 * @param namePName
 * @param optionsData
 * @param selectValue
 */
selector.initSelected = function (reqUrl, selectorId, idPName, namePName, hiddenPname, selectValue) {
    var $ = layui.$;
    var $ax = layui.ax;
    var form = layui.form;
    $("#" + selectorId).empty();
    var ajax = new $ax(reqUrl, function (rpsData) {
        $("#" + selectorId).append('<option value="">请选择</option>');

        for (var i = 0; i < rpsData.data.length; i++) {

            var idVal = eval('rpsData.data[i].' + idPName);
            var nameVal = eval('rpsData.data[i].' + namePName);
            var selected = (selectValue == idVal) ? 'selected' : ''; // 判断是否选中
            $("#" + selectorId).append('<option value="' + idVal + '" ' + selected + '>' + nameVal + '</option>');
        }

        form.on('select(' + selectorId + ')', function (data) {
            var name = data.elem[data.elem.selectedIndex].text
            $("#" + hiddenPname).val(name);
        });
        form.render("select");
    }, function (data) {
        Feng.error("加载失败!" + data.message + "!");
    });
    ajax.start();

}

/**
 * 静态options，回显
 * @param selectorId
 * @param idPName
 * @param namePName
 * @param optionsData
 * @param selectValue
 * @param defaultPlaceholder
 */
selector.initStaticSelected = function (selectorId, idPName, namePName, optionsData, selectValue, defaultPlaceholder) {
    var $ = layui.$;
    var form = layui.form;
    $("#" + selectorId).empty();

    if (null == defaultPlaceholder){
        $("#" + selectorId).append('<option value="">请选择</option>');
    }else {
        $("#" + selectorId).append('<option value="">'+defaultPlaceholder+'</option>');
    }

    for (var i = 0; i < optionsData.length; i++) {

        var idVal = eval('optionsData[i].' + idPName);
        var nameVal = eval('optionsData[i].' + namePName);
        var selected = (selectValue == idVal) ? 'selected' : ''; // 判断是否选中
        $("#" + selectorId).append('<option value="' + idVal + '" ' + selected + '>' + nameVal + '</option>');
    }

    form.on('select(' + selectorId + ')', function (data) {
        var name = data.elem[data.elem.selectedIndex].text
        $("#" + hiddenPname).val(name);
    });

    form.render("select");
}

selector.initStaticSelectedRequired = function (selectorId, idPName, namePName, optionsData, selectValue, defaultPlaceholder) {
    var $ = layui.$;
    var form = layui.form;
    $("#" + selectorId).empty();


    if (null == defaultPlaceholder){
        $("#" + selectorId).append('<option value="">请选择</option>');
    }else {
        $("#" + selectorId).append('<option value="">'+defaultPlaceholder+'</option>');
    }


    for (var i = 0; i < optionsData.length; i++) {

        var idVal = eval('optionsData[i].' + idPName);
        var nameVal = eval('optionsData[i].' + namePName);
        var selected = (selectValue == idVal) ? 'selected' : ''; // 判断是否选中
        $("#" + selectorId).append('<option value="' + idVal + '" ' + selected + '>' + nameVal + '</option>');
    }


    $("#" + selectorId).attr('lay-verify', 'required');
    $("#" + selectorId).attr('required');

    form.on('select(' + selectorId + ')', function (data) {
        var name = data.elem[data.elem.selectedIndex].text
        $("#" + hiddenPname).val(name);
    });

    form.render("select");
}