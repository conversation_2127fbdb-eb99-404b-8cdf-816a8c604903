<html>
<head>
    <title></title>
    <style type="text/css">
        body {
            margin-left: 45px;
            margin-right: 45px;
            font-family: Arial Unicode MS;
            font-size: x-small;
        }

        table {
            margin: auto;
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #444444;
        }

        th, td {
            border: 1px solid #444444;
            font-size: x-small;
            margin-left: 5px;
        }

        .mcContent {
            line-height: 180%;
            padding: 20px;
        }

        .logo {
            text-align: center;
        }

        .title {
            text-align: center;
            font-weight: bold;
            font-size: 20px;
            width: 70%;
            display: inline-block;
        }

        .sub_title {
            text-align: right;
            font-weight: lighter;
            font-size: 14px;
            width: 350px;
            display: inline;
        }

        .notes {
            font-weight: normal;
            margin-left: 5px;
            margin-right: 5px;
            line-height: 18px;
        }

        .text_content {
            margin-left: 5px;
            margin-right: 5px;
            line-height: 18px;
        }

        .sum_insured_first_row {
            width: 20%;
        }

        .sum_insured_span {
            font-size: 10px;
        }

        .special_agreements_div {
            page-break-before: always;
            font-size: 14px;
            margin-top: 20px;
        }

        .special_agreements_div .special_agreements {
            font-size: 18px;
            font-weight: bold;
        }

        .title_right {
            width: 100%;
            margin: 0 auto;
        }

        .title_right p {
            text-align: left;
            margin: 0;
            margin-left: 50%;
            padding: 0;
        }

        @page {
            size:a4;
        @
        bottom-center {
            content: "page " counter(
                    page
            ) " of  " counter(
                    pages
            );
        }

        .signature {
            margin: 0 auto;
            clear: both;
            font-size: 16px;
            font-weight: bold;
        }

        .signature_table {
            /* 	font-size: 16px; */
            font-weight: bold;
        }

    </style>
</head>
<body>
<div>
    <div>
        <div class="title">
            日报
        </div>
        <div class="sub_title">
            <p>${dailyMap.companyName!''}</p>
        </div>
    </div>
    <div class="insurance_info">
        <table class="insurance_info_table" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td width="40%" colspan="2">令和${dailyMap.daiyDate!''}</td>
                <td width="5%">司机</td>
                <td width="3%">正</td>
                <td width="25%">${dailyMap.guideName!''}</td>
                <td width="2%">副</td>
                <td width="25%">${dailyMap.secondGuideName!''}</td>
            </tr>
        </table>
        <table class="insurance_info_table" style="border-top-style: hidden" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td width="5%">所属</td>
                <td width="35%">${dailyMap.companyName!''}</td>
                <td width="5%">团体名</td>
                <td width="30%">樱华国际【${dailyMap.companyName!''}】</td>
                <td width="10%">乘车人数</td>
                <td width="15%">${dailyMap.passagerNum!''}人</td>
            </tr>
        </table>
        <table class="insurance_info_table" style="border-top-style: hidden" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td width="7%">车牌号</td>
                <td width="20%">${carLicense!''}</td>
                <td width="5%">天气</td>
                <td width="5%">${dailyMap.weather!''}</td>
                <td width="13%">出发时间<br/>（开始点呼）</td>
                <td width="20%">${(callMap.startCheckTime)!''}</td>
                <td width="12%">下班时间<br/>（终止点呼）</td>
                <td width="20%">${(callMap.endCheckTime)!''}</td>
            </tr>
        </table>
    </div>
    <div style="padding-top: 20px">
        <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td width="5%">NO</td>
                <td width="40%">出发地&amp;到达地</td>
                <td width="7%">到达时间</td>
                <td width="8%">出发时间</td>
                <td width="5%" >休息时长</td>
                <td width="5%">乘务时长</td>
                <td width="20%">出发<br/>到达</td>
                <td width="5%">实车</td>
                <td width="5%">回送</td>
            </tr>

            <#if routeList?? && routeList?size gt 0>
                <#list routeList as route>
                <#if route_index == 0>
            <tr>
                <td>${route_index+1}</td>
                <td>${route.startLocation!''}</td>
                <td>${route.endTime!''}</td>
                <td>${route.startTime!''}</td>
                <td>${'0'}</td>
                <td>${'0'}</td>
                <td>${route.startKm!''}KM</td>
                <td>${'0'}KM</td>
                <td>${'0'}KM</td>
            </tr>
                </#if>
        <tr>
            <td>${route_index+2}</td>
            <td>${route.endLocation!''}</td>
            <td>${route.endTime!''}</td>
            <td></td>
            <td>${route.sleepMin!'0'}</td>
            <td>${route.drivingMinites!'0'}</td>
            <td>${route.endKm!''}KM</td>
            <td>${route.serviceKm!''}KM</td>
            <td>${route.noCarrayKm!''}KM</td>
        </tr>
                </#list>
            </#if>
            <tr>
                <td>合计</td>
                <td>${''}</td>
                <td>${''}</td>
                <td>${''}</td>
                <td>${sumMap.sleepMin!'0'}</td>
                <td>${sumMap.drivingMinites!'0'}</td>
                <td></td>
                <td>${sumMap.serviceKm!''}KM</td>
                <td>${sumMap.noCarrayKm!''}KM</td>
            </tr>
            <tr>
                <td colspan="10" style="height: 20px"></td>
            </tr>
        </table>

        <table cellpadding="0" cellspacing="0" style="border-top-style: hidden" width="100%">
            <tr>
                <td width="8%">行前<br/>公里数</td>
                <td width="32%">${dailyMap.startKm!''}km</td>
                <td width="10%">行程结束<br/>公里数</td>
                <td width="5%">${dailyMap.endKm!''}km</td>
                <td width="8%">实际行程</td>
                <td width="6%">${dailyMap.realKm!''}km</td>
                <td width="10%">空驶距离</td>
                <td width="6%">${dailyMap.noCarrayKm!''}km</td>
                <td width="10%">总里程</td>
                <td width="5%">${dailyMap.allKm!''}km</td>
            </tr>
            <tr style="height: 20px;">
                <td colspan="10"></td>
            </tr>
        </table>

        <table cellpadding="0" cellspacing="0" style="border-top-style: hidden" width="100%">
            <tr>
                <td width="20%">出行管理者 辅助者</td>
                <td width="50%">报告 注意事项</td>
                <td width="30%">给油量</td>
            </tr>
            <tr>
                <td>${callMap.endCheckerName!''}</td>
                <td>${callMap.remark!''}</td>
                <td>${dailyMap.oilCost!''}L</td>
            </tr>
        </table>
    </div>
</div>
<div style="clear: both"></div>
<div>
    <div>
        <div class="title">
            日常检查
        </div>
        <div class="sub_title">
            整备运营辅助【<div style="width: 50px; display: inline-block;"> ${callMap.startCheckerName!''}</div>】
        </div>
    </div>
    <div>
        <div class="insurance_info" style="width: 50%;display: inline-block">
            <table class="insurance_info_table" cellpadding="0" cellspacing="0" width="50%">
                <tr>
                    <td width="5%">顺序</td>
                    <td width="25%">检查位置</td>
                    <td width="15%">检查项目</td>
                    <td width="5%">是否良好</td>
                </tr>
                <tr>
                    <td width="5%" rowspan="7">驾驶座检查</td>
                    <td width="25%">刹车</td>
                    <td width="15%">踩踏看效果</td>
                    <td width="5%">${checkMap.shache!''}</td>
                </tr>
                <tr>
                    <td width="25%">倒车制动档位</td>
                    <td width="15%">测试拉动情况</td>
                    <td width="5%">${checkMap.dangwei!''}</td>
                </tr>
                <tr>
                    <td width="25%">引擎</td>
                    <td width="15%">低速，加速状况</td>
                    <td width="5%">${checkMap.yinqing!''}</td>
                </tr>
                <tr>
                    <td width="25%">挡风玻璃清洗</td>
                    <td width="15%">是否喷射正常</td>
                    <td width="5%">${checkMap.boliQingxi!''}</td>
                </tr>
                <tr>
                    <td width="25%">雨刮器</td>
                    <td width="15%">是否可刮净</td>
                    <td width="5%">${checkMap.yugua!''}</td>
                </tr>
                <tr>
                    <td width="25%">车里内饰</td>
                    <td width="15%">清洁状况</td>
                    <td width="5%">${checkMap.neishi!''}</td>
                </tr>
                <tr>
                    <td width="25%">门锁，安全带</td>
                    <td width="15%">有无损伤</td>
                    <td width="5%">${checkMap.anquandai!''}</td>
                </tr>


                <tr>
                    <td width="5%" rowspan="6">机舱引擎</td>
                    <td width="25%">挡风玻璃清洗</td>
                    <td width="15%">液量</td>
                    <td width="5%">${checkMap.boliYeliang!''}</td>
                </tr>
                <tr>
                    <td width="25%">刹车油箱</td>
                    <td width="15%">液量</td>
                    <td width="5%">${checkMap.shacheYouxiang!''}</td>
                </tr>
                <tr>
                    <td width="25%">电池</td>
                    <td width="15%">液量</td>
                    <td width="5%">${checkMap.dianchi!''}</td>
                </tr>
                <tr>
                    <td width="25%">润滑油</td>
                    <td width="15%">水量</td>
                    <td width="5%">${checkMap.runhua!''}</td>
                </tr>
                <tr>
                    <td width="25%">机油</td>
                    <td width="15%">油量</td>
                    <td width="5%">${checkMap.jiyou!''}</td>
                </tr>
                <tr>
                    <td width="25%">风扇皮带</td>
                    <td width="15%">张力情况</td>
                    <td width="5%">${checkMap.fenshanPidai!''}</td>
                </tr>

<!--                <tr>-->
<!--                    <td width="25%" colspan="2">便携式酒精检测器号码</td>-->
<!--                    <td width="15%">${dailyMap.jianceNo!''}</td>-->
<!--                    <td width="5%">返还</td>-->
<!--                </tr>-->
            </table>
        </div>
        <div style="display: inline-block;width: 49%;vertical-align:top">
            <table class="insurance_info_table" cellpadding="0" cellspacing="0" width="50%">
                <tr>
                    <td width="5%">顺序</td>
                    <td width="25%">检查位置</td>
                    <td width="15%">检查项目</td>
                    <td width="5%">是否良好</td>
                </tr>
                <tr>
                    <td width="5%" rowspan="8">车辆周围检测</td>
                    <td width="25%" rowspan="4">轮胎</td>
                    <td width="15%">胎压</td>
                    <td width="5%">${checkMap.luotaiTaiya!''}</td>
                </tr>
                <tr>
                    <td width="15%">表面龟裂或者损伤</td>
                    <td width="5%">${checkMap.luotaiGuilie!''}</td>
                </tr>
                <tr>
                    <td width="15%">异常磨损</td>
                    <td width="5%">${checkMap.luotaiYichang!''}</td>
                </tr>
                <tr>
                    <td width="15%">轮胎纹路深度</td>
                    <td width="5%">${checkMap.luotaiShengdu!''}</td>
                </tr>
                <tr>
                    <td width="25%" rowspan="3">车灯</td>
                    <td width="15%">照明</td>
                    <td width="5%">${checkMap.chedeng!''}</td>
                </tr>
                <tr>
                    <td width="15%">闪烁</td>
                    <td width="5%">${checkMap.shanshuo!''}</td>
                </tr>
                <tr>
                    <td width="15%">污垢</td>
                    <td width="5%">${checkMap.wugou!''}</td>
                </tr>

                <tr>
                    <td width="25%">跌轮安装</td>
                    <td width="15%">安装情况</td>
                    <td width="5%">${checkMap.dieluoAnzhuang!''}</td>
                </tr>

                <tr>
                    <td width="5%" rowspan="2">其他</td>
                    <td width="25%">应急信号</td>
                    <td width="15%">配备</td>
                    <td width="5%">${checkMap.yingji!''}</td>
                </tr>

                <tr>
                    <td width="25%">车辆证明、保险</td>
                    <td width="15%">配备</td>
                    <td width="5%">${checkMap.baoxian!''}</td>
                </tr>

                <tr>
                    <td width="25%">备注</td>
                    <td width="15%" colspan="3">${checkMap.remark!''}</td>
                </tr>
            </table>
        </div>
    </div>
</div>
</body>
</html>
