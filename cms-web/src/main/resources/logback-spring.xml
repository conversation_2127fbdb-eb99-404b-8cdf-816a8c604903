<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOG_HOME" value="./logs/thcms"/>

    <!-- 定义控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date [%thread] %X{X-B3-TraceId:-} %X{X-B3-SpanId:-} %X{X-Span-Export:-} %-5level %logger{50}:%L - %msg%n
            </pattern>
        </encoder>
    </appender>

    <!-- 定义文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>${LOG_HOME}/info.log</file>
        <encoder>
            <pattern>%date [%thread] %X{X-B3-TraceId:-} %X{X-B3-SpanId:-} %X{X-Span-Export:-} %-5level %logger{50}:%L - %msg%n
            </pattern>        </encoder>
    </appender>
    <logger name="com.th.cms">
        <level value="INFO"/>
    </logger>
    <logger name="org.mybatis.spring">
        <level value="INFO"/>
    </logger>
    <logger name="Mybatis2Sql">
        <level value="DEBUG"/>
    </logger>
    <!-- 配置根日志级别，控制全局的日志级别 -->
    <root level="INFO">
        <!-- 附加输出目标（appender）的引用，可以有多个 -->
        <appender-ref ref="CONSOLE" />
    </root>
</configuration>
