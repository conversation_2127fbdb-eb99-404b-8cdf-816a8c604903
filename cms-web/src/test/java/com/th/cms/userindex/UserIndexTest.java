package com.th.cms.userindex;

import com.google.common.collect.Lists;
import com.th.cms.GunsApplication;
import com.th.cms.modular.userindex.dataobject.*;
import com.th.cms.modular.userindex.dto.BusinessRankDTO;
import com.th.cms.modular.userindex.mapper.UserIndexMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = GunsApplication.class)
@WebAppConfiguration
public class UserIndexTest {
    @Autowired
    private UserIndexMapper userIndexMapper;

    @Test
    public void test() {
        AuthenticationStatusPerPersonRequestDO dto = new AuthenticationStatusPerPersonRequestDO();
        dto.setStartDate(new Date());
        dto.setEndDate(new Date());
        dto.setIsAll(false);
        dto.setDeptIds(Lists.newArrayList());

        List<AuthenticationStatusPerPersonDO> authenticationStatusPerPersonDOS = userIndexMapper.authenticationStatusPerPerson(dto);
    }
    @Test
    public void test1() {
        CompletePerPersonRequestDO dto = new CompletePerPersonRequestDO();
        dto.setStartDate(new Date());
        dto.setEndDate(new Date());
        dto.setIsAll(false);
        dto.setDeptIds(Lists.newArrayList());
        dto.setQueryType(1);

        List<CompletePerPersonDO> authenticationStatusPerPersonDOS = userIndexMapper.completePerPerson(dto);
    }
    @Test
    public void test2() {
        BusinessRankRequestDO rankRequestDTO = new BusinessRankRequestDO();
        rankRequestDTO.setStartDate(new Date());
        rankRequestDTO.setEndDate(new Date());
        List<BusinessRankDTO> authenticationStatusPerPersonDOS = userIndexMapper.rankList(rankRequestDTO,null);
    }
    @Test
    public void test3() {


      Long count = userIndexMapper.queryTodayAudit(1L);
         count = userIndexMapper.queryWaitAudit(1L);
    }
}
