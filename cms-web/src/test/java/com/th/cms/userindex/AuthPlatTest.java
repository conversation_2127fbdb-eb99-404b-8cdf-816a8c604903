package com.th.cms.userindex;

import com.th.cms.GunsApplication;
import com.th.cms.modular.influcer.cooperationPlatform.dto.BasePlatformRequestDTO;
import com.th.cms.modular.influcer.cooperationPlatform.service.AuthPlatformService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = GunsApplication.class)
@WebAppConfiguration
public class AuthPlatTest {
    @Autowired
    private AuthPlatformService authPlatformService;

    @Test
    public void test() {
        authPlatformService.basePlatformList(new BasePlatformRequestDTO(),null);
    }

}
