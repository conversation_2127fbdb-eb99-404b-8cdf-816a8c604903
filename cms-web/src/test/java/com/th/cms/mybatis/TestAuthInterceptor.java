package com.th.cms.mybatis;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.th.cms.GunsApplication;
import com.th.cms.core.auth.AuthContext;
import com.th.cms.core.auth.AuthMybatisMapperHelper;
import com.th.cms.core.common.page.LayuiPageInfo;
import com.th.cms.core.shiro.ShiroKit;
import com.th.cms.modular.influcer.bizInflucer.dao.BizInflucerMapper;
import com.th.cms.modular.influcer.bizInflucer.model.BizInflucer;
import com.th.cms.modular.influcer.bizInflucer.model.reqparam.BizInflucerListParam;
import com.th.cms.modular.influcer.bizInflucer.service.BizInflucerService;
import com.th.cms.modular.settle.settleProjects.entity.SettleProjects;
import com.th.cms.modular.settle.settleProjects.mapper.SettleProjectsMapper;
import com.th.cms.modular.settle.settleProjects.model.reqparam.SettleProjectsListParam;
import com.th.cms.modular.settle.settleProjects.service.SettleProjectsService;
import com.th.cms.modular.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = GunsApplication.class)
@WebAppConfiguration
public class TestAuthInterceptor {
    @Autowired
    private SettleProjectsMapper myMapper;
    @Autowired
    private UserService userService;

    private void login() {
        Subject currentUser = ShiroKit.getSubject();
        AuthenticationToken token = new UsernamePasswordToken("lizong", "111111".toCharArray());
        currentUser.login(token);
    }

    /**
     * 用queryWrapper分页测试
     */

    @Test
    public void test() {
        AuthContext.AuthInfo authInfo = new AuthContext.AuthInfo();
        authInfo.setUseRole(true);
        authInfo.setRoleIds(Lists.newArrayList(1904882446309621762L));
        authInfo.setUseDept(false);
        authInfo.setDeptIds(Lists.newArrayList(1898938728194326529L));
        authInfo.setCurrentUserId(1L);
        authInfo.setUseSubordinate(false);
        AuthContext.set(authInfo);
        QueryWrapper<SettleProjects> objectQueryWrapper = new QueryWrapper<>();
//        objectQueryWrapper.eq("company_name","haha");
        IPage<SettleProjects> settleProjectsIPage = myMapper.selectPage(new Page<>(1, 10), objectQueryWrapper);
        log.info("data is {}", JSONObject.toJSONString(settleProjectsIPage));
    }

    /**
     * 不分页测试
     */
    @Test
    public void test1() {
        AuthContext.AuthInfo authInfo = new AuthContext.AuthInfo();
        authInfo.setUseRole(true);
        authInfo.setRoleIds(Lists.newArrayList(1904882446309621762L));
        authInfo.setUseDept(false);
        authInfo.setDeptIds(Lists.newArrayList(1898938728194326529L));
        authInfo.setCurrentUserId(1L);
        authInfo.setUseSubordinate(false);
        AuthContext.set(authInfo);
        QueryWrapper<SettleProjects> objectQueryWrapper = new QueryWrapper<>();
//        objectQueryWrapper.eq("company_name","haha");
        List<SettleProjects> settleProjectsIPage = myMapper.selectList(objectQueryWrapper);
        log.info("data is {}", JSONObject.toJSONString(settleProjectsIPage));
    }

    @Autowired
    private BizInflucerMapper bizInflucerMapper;

    /**
     * 自定义sql测试
     */
    @Test
    public void test2() {
        AuthContext.AuthInfo authInfo = new AuthContext.AuthInfo();
        authInfo.setUseRole(true);
        authInfo.setRoleIds(Lists.newArrayList(1904882446309621762L));
        authInfo.setUseDept(false);
        authInfo.setDeptIds(Lists.newArrayList(1898938728194326529L));
        authInfo.setCurrentUserId(1L);
        authInfo.setUseSubordinate(false);
//        AuthContext.set(authInfo);
        BizInflucerListParam param = new BizInflucerListParam();
        param.setKeywords("1");
        IPage<BizInflucer> bizInflucerIPage = bizInflucerMapper.queryBizInFlucerByPage(new Page<>(1, 1), param);
        log.info("data is {}", JSONObject.toJSONString(bizInflucerIPage));

    }

    @Autowired
    private BizInflucerService bizInflucerService;


    @Autowired
    private SettleProjectsService projectsService;
    @Autowired
    private SettleProjectsMapper settleProjectsMapper;
    /**
     * 测试注解，sql中包含预期字段
     */
    @Test
    public void test4() {
        login();
        SettleProjectsListParam param = new SettleProjectsListParam();
        projectsService.testMybatisAop(param);
    }

    /**
     * 查询角色
     */
    @Test
    public void testRole() {
        login();
        SettleProjectsListParam param = new SettleProjectsListParam();
        IPage<SettleProjects> settleProjectsIPage = settleProjectsMapper.testRole(new Page<>(1, 10), param);
        log.info("data is {}", JSONObject.toJSONString(settleProjectsIPage));

    }

    /**
     * 查询部门
     */
    @Test
    public void testDept() {
        login();
        SettleProjectsListParam param = new SettleProjectsListParam();
        IPage<SettleProjects> settleProjectsIPage = settleProjectsMapper.testDept(new Page<>(1, 10), param);
        log.info("data is {}", JSONObject.toJSONString(settleProjectsIPage));

    }
    /**
     * 查询下属数据
     */
    @Test
    public void testSubordinate() {
        login();
        SettleProjectsListParam param = new SettleProjectsListParam();
        IPage<SettleProjects> settleProjectsIPage = settleProjectsMapper.testSubordinate(new Page<>(1, 10), param);
        log.info("data is {}", JSONObject.toJSONString(settleProjectsIPage));

    }
    /**
     * 查询service上的拦截
     */
    @Test
    public void testService() {
        login();
        SettleProjectsListParam param = new SettleProjectsListParam();
        LayuiPageInfo pageBySpec = projectsService.findPageBySpec(param);
        log.info("data is {}", JSONObject.toJSONString(pageBySpec));

    }
    @Autowired
    private AuthMybatisMapperHelper mybatisMapperHelper;
    /**
     * 测试自定义收起helper
     */
    @Test
    public void testCustomer() {
        AuthContext.AuthInfo authInfo = new AuthContext.AuthInfo();
        authInfo.setUseRole(false);
        authInfo.setRoleIds(Lists.newArrayList(1904882446309621762L));
        authInfo.setUseDept(false);
        authInfo.setDeptIds(Lists.newArrayList(1898938728194326529L));
        authInfo.setCurrentUserId(1L);
        authInfo.setUseSubordinate(true);
        SettleProjectsListParam param = new SettleProjectsListParam();

        IPage<SettleProjects> bizInflucerIPage = mybatisMapperHelper.executeByCustomer(t->settleProjectsMapper.queryProjectByPage(new Page<>(1,10), param),authInfo);
        log.info("data is {}", JSONObject.toJSONString(bizInflucerIPage));

    }
}
